@use "../theme/var";

.admin_sidebar {
  &_wrapper {
    position: relative;

    .linkList {
      border-bottom: 1px solid var.$baseclr;
      // margin-bottom: 7px;

      @media (max-width: 991px) {
        border: none;
      }

      &:last-child {
        margin-bottom: 0;
        border-bottom: 0;
      }

      a {
        display: flex;
        align-items: center;
        padding-bottom: 3px;
        padding-top: 3px;

        .linktext {
          display: flex;
          align-items: center;
          justify-content: center;
          color: var.$baseclr;
          text-decoration: none;
          padding: 0.5rem 0;
          font-size: 1.25rem;
          font-weight: 600;
          line-height: 1.4;
          width: 60px;
          height: 60px;
          border-radius: 10rem;
          border: 2px solid var.$baseclr;
          background-color: var.$clr04498C;
          transition: all ease-in-out 0.3s;

          svg {
            width: 30px;
          }

          @media (max-width: 991px) {
            width: 56px;
            height: 56px;
          }
        }

        &:hover,
        &.active {
          @media screen and (min-width: 992px) {
            background-color: #283f67;
          }

          .linktext {
            border-color: #32cd33;
            box-shadow: 0 0 10px 4px rgba(#32cd33, 0.5);
          }
        }

        .fulltext {
          color: var.$white;
          font-size: 1.15rem;
          padding-left: 0.625rem;
          font-weight: 600;

          @media (max-width: 1279px) {
            font-size: 1rem;
          }

          @media (max-width: 991px) {
            display: none;
          }
        }
      }
    }
  }

  .scroll-btn {
    background-color: var.$baseclr;
    color: white;
    border: none;
    padding: 0;
    cursor: pointer;
    font-size: 1.2rem;
    min-width: 30px;
    min-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10rem;
    position: absolute;
    display: none;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;

    @media (max-width: 767px) {
      display: flex;
    }

    &.left {
      left: 10px;

      img {
        transform: rotate(180deg);
      }
    }

    &.right {
      right: 10px;
    }

    &:hover {
      background-color: var.$baseclr;
    }

    &.disabled,
    &:disabled {
      background-color: #414c60;
    }
  }
}