@use "../theme/var";

.common_head_container {
  position: sticky;
  top: 80px;
  bottom: 50px;
  z-index: 999;

  @media (width <=1199px) {
    top: 77.3px;
  }

  @media (width <=767px) {
    top: 64.16px;
  }

  @media (width <=360px) {
    top: 64.16px;
  }
}

.common_head {
  background-color: var.$clr031940;
  padding: 1.25rem;
  border-radius: 1.25rem;
  border-bottom: 1px solid #064197;

  @media (max-width: 1199px) {
    padding: 15;
  }

  @media (max-width: 767px) {
    border: 1px solid #064197;
  }

  &_md {
    @media (max-width: 550px) {
      display: none;
    }
  }

  &_small {
    display: none;

    button {
      min-width: 30px;
      min-height: 30px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #00adef;

      @media (max-width: 550px) {
        min-width: 25px;
        min-height: 25px;

        svg {
          height: 15px !important;
          width: 15px !important;
        }
      }

      svg {
        height: 20px;
        width: 20px;
      }
    }

    .layout_fix {
      display: flex;
      justify-content: space-between;
    }

    @media (max-width: 550px) {
      display: block;
    }
  }

  .commom_tradeacct {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .account {
      background-color: var.$clr04498C;
      padding: 8px 15px;
      height: 100%;
      width: 100%;
      border-radius: 10px 10px 0 0;

      h6 {
        color: var.$white;
        font-size: 1rem;
        line-height: 1;

        @media (max-width: 1199px) {
          font-size: 14px;
        }
      }
    }

    .number,
    select {
      background-color: var.$white;
      padding: 5px 15px;
      height: 100%;
      width: 100%;
      border-radius: 0 0 10px 10px;
      font-size: 1rem;
      font-weight: 600;

      @media (max-width: 1199px) {
        font-size: 14px;
      }
    }

    .form-select:focus {
      border-color: transparent !important;
      box-shadow: none !important;
    }
  }

  &_search {
    .commonSearch {
      .form-control {
        width: 100%;
        border: 0;

        @media (max-width: 1199px) {
          min-height: 66px;
        }

        @media (max-width: 767px) {
          min-height: 60px;
        }
      }
    }
  }

  &_datebox {
    background-color: rgba(255, 255, 255, 0.3);
    min-height: 70px;
    border-radius: 1rem;
    padding: 8px 15px;

    @media (max-width: 1199px) {
      padding: 8px 10px;
      min-height: 66px;
    }

    @media (max-width: 767px) {
      min-height: auto;
    }

    h6 {
      color: rgba(255, 255, 255, 0.6);
      padding-top: 5px;

      @media (max-width: 1199px) {
        font-size: 14px;
      }
    }

    h5 {
      @media (max-width: 1199px) {
        font-size: 16px;
      }
    }
  }
}

.head_draft {
  margin-top: 10px;

  &_icons {
    display: flex;
    align-items: center;
    gap: 5px;

    p {
      font-weight: 600;
    }

    svg,
    img {
      width: 20px;
      height: 20px;
    }
  }
}