@use "../theme/var" as *;

.trade_importer {
    &_card {
        padding: 1.25rem;
        border-radius: 1.25rem;
        background: $gradientcardblue;
        border: 2px solid $baseclr;

        &.greengrandient {
            border: 0;
        }

        &_head {
            font-size: 3rem;
            font-weight: bolder;
        }

        &_message {
            background-color: rgba(50, 205, 51, 0.7);
            padding: 1.6rem 1.25rem;
            border-radius: 1.25rem;

            h4 {
                margin-bottom: 1rem;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            &.message_redbg {
                background-color: rgba(255, 105, 106, 0.7);
            }
        }

        .trade_manager_trade_entry_box {
            .green_arrow {
                svg {
                    path {
                        fill: $green !important;
                    }
                }
            }
        }
    }
}

.trade_head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 30px;
    position: relative;

    @media screen and (max-width: 991px) {
        justify-content: unset;
    }

    .common_heading {
        h2 {
            @media screen and (max-width: 1599px) {
                font-size: 35px;
            }

            @media screen and (max-width: 1279px) {
                font-size: 28px;
            }
        }
    }

    &_title {
        display: flex;
        flex-wrap: wrap;

        @media screen and (max-width: 767px) {
            justify-content: center;
            margin-top: 20px;
        }

        h4 {
            margin-right: 10px;
            margin-bottom: 10px;

            @media screen and (max-width: 1599px) {
                font-size: 18px;
                line-height: normal;
            }
        }
    }

    .head_draft {

        &_icons {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 5px;

            svg {
                width: 20px;
                height: 20px;
            }
        }
    }
}