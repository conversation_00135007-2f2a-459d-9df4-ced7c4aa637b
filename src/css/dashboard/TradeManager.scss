@use "../theme/var" as *;

.trade_manager {
    .trade_head {
        position: relative;
        display: flex;
        justify-content: center;

        @media (width<=400px) {
            gap: 10px;
            justify-content: end;
        }
    }

    &_btns {
        .btn-style {
            min-height: 70px;
            text-transform: uppercase;
            font-size: 28px;
            font-weight: bold;
            line-height: 34.66px;
            letter-spacing: -1px;

            @media (max-width: 1199px) {
                min-height: 60px;
                font-size: 20px;
            }

            @media (max-width: 767px) {
                min-height: 50px;
            }

            @media (max-width: 575px) {
                width: 100% !important;
            }

            svg {
                transition: all ease-in-out 0.3s;
                width: 28px;

                @media screen and (max-width: 1199px) {
                    width: 21px;
                }
            }
        }
    }

    &_entrylist {
        &_box {
            border: 2px solid $baseclr;
            border-radius: 0.625rem;
            padding: 1.25rem;
            margin-top: 1.25rem;
            display: flex;
            background: radial-gradient(50% 50% at 50% 50%, rgba(0, 185, 255, 0.2) 21.5%, rgba(0, 83, 153, 0.2) 100%),
                linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.05) 47.5%, rgba(255, 255, 255, 0) 100%);
            text-transform: uppercase;
            cursor: pointer;
            transition: background 0.3s ease;

            &:hover {
                background: radial-gradient(50% 50% at 50% 50%, #00b9ff66 21.5%, #00539966),
                    linear-gradient(135deg, #fff0, #ffffff20 47.5%, #fff0);
            }

            h5 {
                font-size: 1.5rem;
                font-weight: 800;
                min-width: 300px;

                @media (max-width: 1199px) {
                    font-size: 1.25rem;
                    min-width: 200px;
                }

                @media (max-width: 767px) {
                    font-size: 1rem;
                    min-width: auto;
                }


                &:nth-child(2) {
                    margin-left: 20px;
                }
            }
        }
    }

    &_trade_entry {
        &_box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 1.25rem;
            padding: 1.5rem 1.25rem;
            border-radius: 1.25rem;
            cursor: pointer;

            @media (max-width: 1199px) {
                padding: 10px 10px;
            }

            &_headtext {

                @media (max-width: 767px) {
                    flex-wrap: wrap;
                }
            }

            h5 {
                font-size: 1.5rem;
                font-weight: 800;
                // margin-left: 1.5rem;
                text-transform: uppercase;

                // &:last-child {
                //     margin-left: 1.5rem;
                // }

                @media (max-width: 1279px) {
                    font-size: 1rem;
                }

                @media (max-width: 1199px) {
                    font-size: 0.875rem;
                    margin-left: 10px;
                }

                @media (max-width: 767px) {
                    padding: 4px 0;
                }

                &:last-child {
                    margin-left: 0;
                }
            }

            .solidArrow {
                transform: rotate(0deg);

                @media screen and (max-width: 1199px) {
                    svg {
                        width: 20px;
                    }
                }

                &.infoIcon {
                    svg {
                        width: 37px;
                        height: 37px;

                        circle {
                            fill: $redlightclr;
                        }
                    }
                }
            }

            .endArrow {
                transform: rotate(180deg);
                margin-left: 1.5rem;

                @media screen and (max-width: 1199px) {
                    margin-left: 10px;
                }
            }
        }
    }

    .setting_btn {
        background-color: transparent;
        border: 0;

        svg {
            filter: brightness(0) invert(1);

            @media (width<=550px) {
                width: 30px;
                height: 30px;
            }
        }

    }
}