@use "../theme/var";

.portfolio_manager {
  &_heading {
    background-color: #0b2c62;
    padding: 10px 30px;
    width: fit-content;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;

    @media screen and (max-width: 767px) {
      padding: 10px 23px;
    }

    &.active {
      background-color: var.$clr031940;
    }
  }

  &_card {
    background-color: var.$clr031940;
    padding: 20px 15px;
    border-radius: 0 0 25px 25px;

    p {
      font-size: 18px;
      font-weight: 400;

      span {
        color: var.$baseclr;
      }
    }

    .innerCard {
      background-color: #283f68;
      padding: 10px;
      border-radius: 0 0 20px 20px;
      border-top: 1px solid #ffffff80;
      margin-bottom: 15px;

      .cardHeading {
        display: flex;
        gap: 10px;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ffffff80;

        .whiteCircle {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background-color: #fff;
        }

        p {
          font-size: 16px;
          font-weight: 600;
        }
      }

      .blueCard {
        background-color: var.$clr031940;
        padding: 8px 10px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
        min-height: 40px;

        p {
          font-size: 14px;
          font-weight: 600;
        }
      }

      select {
        cursor: pointer;
      }

      .whiteCard {
        display: flex;
        align-items: center;
        padding: 8px 10px;
        border-radius: 8px;
        background-color: var.$white;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        width: 100%;
        overflow: hidden;
        min-height: 40px;
        height: 100%;

        select,
        input {
          display: flex;
          align-items: center;
          height: 100%;
          background-color: #fff !important;
          color: var.$black;
          border: none;
          width: 100%;
          font-size: 14px;
          font-weight: 600;
          text-align: center;
          -webkit-appearance: none;
          -moz-appearance: none;
          text-overflow: "";

          option {
            color: var.$black;
          }

          &:focus-visible {
            outline: none !important;
          }
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        input[type="number"] {
          -moz-appearance: textfield;
        }
      }

      .DisabledCard {
        background-color: var.$clr031940;
        padding: 8px 10px;
        border-radius: 8px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        width: 100%;
        overflow: hidden;
        min-height: 40px;
        height: 100%;

        select,
        input {
          display: flex;
          align-items: center;
          height: 100%;
          background-color: var.$clr031940;
          color: var.$white;
          border: none;
          width: 100%;
          font-size: 14px;
          font-weight: 600;
          text-align: center;
          cursor: default;
          -webkit-appearance: none;
          -moz-appearance: none;
          text-overflow: "";

          option {
            color: var.$black;
          }

          &:focus-visible {
            outline: none !important;
          }
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        input[type="number"] {
          -moz-appearance: textfield;
        }
      }
    }

    .submit-manual-btn {
      background-color: #4b8ef9;
      border-radius: 50px;
      padding: 8px;
      width: 100% !important;
      font-weight: 600;
      color: #fff;
    }

    .special-fields {
      border: 1px solid #ffffff33;
      border-radius: 8px;
      padding: 5px;
    }

    .special-fields-error {
      position: absolute;
      top: -100px;
      padding: 8px 10px;
      border-radius: 8px;
      margin: 0.5rem 0;
      background-color: #ffa9a9;
      color: #000;
      text-align: center;
      margin-bottom: 10px;
      font-size: 16px;
    }

    &_deleteAll {
      position: relative;

      .section_text {
        font-weight: 600;
        font-size: 16px;
      }

      .section_text:first-child {
        margin-top: 10px;
      }

      .section_text:nth-child(3) {
        margin-top: 25px;
      }

      button {
        margin-top: 20px;
      }

      .deleteAllModal_overlay {
        position: absolute;
        top: 0%;
        height: 100%;
        width: 100%;
        background-color: #2840687e;
        z-index: 8;

        .deleteAll_modal {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          border: 1px solid white;
          background-color: var.$clr031940;
          padding: 16px;
          margin: 0 auto;
          width: fit-content;
          margin-top: 5px;
          box-sizing: border-box;

          p {
            font-weight: 600;
            font-size: 16px;
          }

          button {
            margin-bottom: 25px;
          }

          @media screen and (max-width: 1180px) {
            width: 60%;
          }

          @media screen and (max-width: 1023px) {
            width: 80%;
          }

          @media screen and (max-width: 767px) {
            width: 100%;

            button {
              width: 100% !important;
              max-width: 100%;
              box-sizing: border-box;
              margin-bottom: 0px;
            }
          }
        }
      }
    }
  }

  .porfolio-custom-select {
    position: relative;

    .header {
      min-height: 40px;
      box-shadow: none;
      outline: none;
      width: 100%;
      padding: 8px 10px;
      border-radius: 8px;
      background-color: #fff;
      color: #000;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      span {
        overflow: hidden;
        white-space: nowrap !important;
        // width: 100% !important;
      }
    }

    .body {
      width: 200%;
      top: 44px;
      right: 0;
      padding: 8px 10px;
      position: absolute;
      border-radius: 8px;
      border: 1px solid #191c23;
      background-color: #fff;
      max-height: 250px;
      overflow-y: scroll;
      overflow-x: hidden;
      z-index: 99;

      @media screen and (max-width: 500px) {
        width: 230%;
      }


      .search {
        background-color: #909090;
        padding: 8px 10px;
        margin-bottom: 8px;
        width: 100%;
        border-radius: 0.5rem;
        display: flex;
        gap: 10px;
        align-items: center;

        input {
          background-color: transparent;
          width: 100%;
          color: #fff;

          &:focus-visible {
            outline: none !important;
          }

          &::placeholder {
            color: var.$white;
            opacity: 1 !important;
          }
        }
      }

      ul {
        li {
          cursor: pointer;
          padding: 10px;
          border-radius: 8px;
          border-bottom: 0 !important;
          margin-bottom: 0 !important;
          color: #000;
          font-size: 14px;
          font-weight: 600;

          &:hover {
            background-color: #909090;
            color: #fff;
          }
        }
      }
    }
  }
}