@use "../theme/var" as *;

.strategy_builder {
    &_card {
        padding: 0.625rem;
        border-radius: 1rem;
        background-color: rgba(0, 173, 239, 0.4);

        &_include {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            background-color: $clr031940;
            border-radius: 15px;
            padding: 0.625rem 1.25rem;

            h5 {
                display: flex;
                align-items: center;

                span {
                    width: 24px;
                    height: 24px;
                    background-color: $white;
                    border-radius: 10rem;
                    margin-right: 10px;
                }
            }
        }
    }

    .trade_head {
        &_title {
            h4 {
                font-size: 28px !important;
                margin-right: 0 !important;
                margin-bottom: 0 !important;

                @media (max-width:1199px) {
                    font-size: 20px;
                }
            }
        }

        &_btns {
            display: flex;
            justify-content: end;

            @media (max-width:767px) {
                .btn-style {
                    width: calc(50% - 5px);
                    min-width: auto;
                    min-height: 46px;
                    font-size: 18px;
                }
            }
        }
    }

    .strategy_builder_form {
        label {
            font-size: 20px;
            font-weight: 600;
            color: #fff;

            @media (width <=550px) {
                font-size: 16px;
            }

            @media (551 <=width <=768px) {
                font-size: 18px;
            }
        }

        textarea {
            font-size: 18px;
            font-weight: 600;
            background-color: #FFFFFF33;
            color: #fff;

            @media (width <=550px) {
                font-size: 14px;
            }

            @media (width <=768px) {
                font-size: 16px;
            }
        }

        .no-min-height {
            min-height: unset !important;
            resize: none;

            &::-webkit-scrollbar {
                display: none;
            }
        }

        .character-count {
            text-align: end;
            margin-top: 10px;
            color: #f5f6f7;
            font-size: 14px;
        }


        button {
            text-transform: uppercase;
            font-size: 28px;
            font-weight: 600;
        }
    }

    .common_heading {
        h2 {
            @media (max-width:1199px) {
                font-size: 24px !important;
            }
        }

        @media (max-width:767px) {
            margin: 10px 0 30px;
        }
    }

    &_portfolio {
        background-color: #031940;
        padding: 10px 20px;
        border-radius: 15px;
        margin-bottom: 1.25rem;

        span {
            font-size: 20px;
            font-weight: 600;

            @media (width <=425px) {
                font-size: 14px;
            }

            @media (width <=768) {
                font-size: 16px;
            }
        }

        .greenCircle {
            min-width: 24px;
            min-height: 24px;
            background-color: #32CD33;
            border-radius: 50%;

            @media (width <=768) {
                min-width: 20px;
                min-height: 20px;
            }
        }

        .redCircle {
            min-width: 24px;
            min-height: 24px;
            background-color: #FF696A;
            border-radius: 50%;

            @media (width <=768) {
                min-width: 20px;
                min-height: 20px;
            }
        }
    }

    .sectionDivider {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 1.25rem;

        span {
            border: 1px solid #fff;
            padding: 5px 10px;
            border-radius: 30px;
        }

        .center-line {
            border-top: 1px solid #FFFFFF33;
            width: 100%;
        }
    }

    &_addStrategy {
        background-color: #00ADEF66;
        padding: 15px 10px;
        border-radius: 15px;
        margin-bottom: 1.25rem;

        .include_trades {
            background-color: #031940;
            padding: 10px;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;

            .head {
                display: flex;
                gap: .75rem;
                align-items: center;
            }

            img {
                cursor: pointer;
            }

            .roundedDot {
                min-width: 24px;
                min-height: 24px;
                border-radius: 10px;
                border: 1px dashed #fff;
                background-color: #FFFFFF1A;

                @media (width <=768) {
                    min-width: 20px;
                    min-height: 20px;
                }
            }

            &_circle {
                min-width: 24px;
                min-height: 24px;
                background-color: #12B5CB;
                border-radius: 50%;

                @media (width <=768) {
                    min-width: 20px;
                    min-height: 20px;
                }
            }

            span {
                font-size: 20px;
                font-weight: 600;

                @media (width <=425px) {
                    font-size: 14px;
                }

                @media (width <=768) {
                    font-size: 16px;
                }
            }
        }

        .exclude_trades {
            background-color: #031940;
            padding: 10px;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;

            .head {
                display: flex;
                gap: .75rem;
                align-items: center;
            }

            img {
                cursor: pointer;
            }

            .roundedDot {
                width: 24px;
                height: 24px;
                border-radius: 10px;
                border: 1px dashed #fff;
                background-color: #FFFFFF1A;
            }

            &_circle {
                min-width: 24px;
                min-height: 24px;
                border: 2px dashed #E97512;
                border-radius: 50%;

                @media (width <=768) {
                    min-width: 20px;
                    min-height: 20px;
                }
            }

            span {
                font-size: 20px;
                font-weight: 600;

                @media (width <=425px) {
                    font-size: 14px;
                }

                @media (width <=768) {
                    font-size: 16px;
                }
            }
        }

        .modal_overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 99998;
        }

        .select_section {
            background-color: #031940;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 15px;

            .selectField {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-right: 2px solid #FFFFFF66;
                padding-right: 20px;
                cursor: pointer;

                @media (width <=767px) {
                    border-right: none !important;
                    padding-right: 0px !important;
                }

                p,
                span {
                    font-weight: 600;
                    font-size: 16px;
                }

                span {
                    color: #FFFFFFCC;
                    margin-bottom: 10px;
                }
            }

            .condition_section {

                .conditions-wrapper {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .show-condition {
                    width: fit-content;
                    background-color: #031940;
                    border: 1px dashed #FFFFFF80;
                    padding: 10px 20px;
                    color: #fff;
                    border-radius: 50px;
                    font-weight: 600;
                    display: flex;
                    gap: 10px;
                    align-items: center;
                    cursor: pointer;

                    img {
                        height: 15px;
                        width: 15px;
                    }
                }

                .condition-dropdown {
                    position: absolute;
                    top: 0;
                    left: 0;
                    background-color: #031942;
                    width: 370px;
                    border: 1px solid #00ADEF;
                    padding: 15px 20px;
                    border-radius: 20px;
                    z-index: 999;

                    @media (width <=500px) {
                        width: 100%;
                    }

                    .divider {
                        border-bottom: 1px solid #FFFFFF33;
                        margin: 10px 0;
                    }

                    p {
                        font-size: 20px;
                        font-weight: 600;
                        color: #FFFFFF;
                    }

                    .error-message {
                        color: #ff696a !important;
                        font-size: 16px !important;
                        font-weight: 700 !important;
                        padding-bottom: 0 !important;
                    }

                    .select-operation {
                        background-color: #fff;
                        border-radius: 15px;
                        padding: 10px 20px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        cursor: pointer;

                        span {
                            font-weight: 600;
                            color: #000;
                        }
                    }

                    .select-operation-dropdown {
                        position: absolute;
                        top: 0;
                        width: 100%;
                        background-color: #fff;
                        border-radius: 15px;

                        span {
                            display: block;
                            font-weight: 600;
                            color: #000;
                            padding: 10px 20px;
                            cursor: pointer;
                        }
                    }

                    .expression {

                        input {
                            background-color: #fff;
                            border-radius: 15px;
                            padding: 10px 20px;
                            font-weight: 600;
                            color: #000;
                            width: 100%;

                            &:focus {
                                box-shadow: none;
                                outline: 0;
                            }

                            &::placeholder {
                                color: #000;
                                opacity: 1;
                            }
                        }
                    }

                    .btns {
                        display: flex;
                        justify-content: end;
                        gap: 10px;

                        .confirm {
                            color: #00ADEF;
                        }

                        .cancel {
                            color: #FFFFFF;
                        }
                    }
                }
            }
        }

        .search_section {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            width: 60%;
            background-color: #031940;
            padding: 20px;
            border-radius: 15px;
            min-height: 90vh;
            max-height: 90vh;
            border: 1px solid #00adef;

            @media only screen and (width <=1024px) {
                width: 80%;
            }

            @media only screen and (width <=500px) {
                width: 90%;
            }

            .closeModal {
                cursor: pointer;
                width: 20px;
            }

            .search {
                width: 100%;
                background-color: #FFFFFF33;
                padding: 10px 20px;
                border-radius: 15px;
                display: flex;
                gap: 10px;

                input {
                    background-color: transparent;
                    width: 100%;
                    color: #FFFFFF99;

                    &:focus {
                        box-shadow: none;
                        outline: 0;
                    }
                }
            }

            .scope_section_wrapper {
                position: relative;
                display: flex;
                align-items: center;
                border-bottom: 2px solid white;
            }

            .scope_section {
                height: 60px;
                position: relative;
                display: flex;
                align-items: center;
                overflow-x: auto;
                scroll-behavior: smooth;

                &::-webkit-scrollbar {
                    display: none;
                }

                .active {
                    background-color: #00ADEF33;
                    border-bottom: 2px solid #00ADEF;
                }

                .scopeName {
                    display: flex;
                    white-space: nowrap;
                    align-items: center;
                    gap: 10px;
                    padding: 15px 10px;
                    cursor: pointer;
                    transition: 0.3s all ease-in-out;

                    p {
                        font-size: 18px;
                        font-weight: 400;

                        @media (width <=550px) {
                            font-size: 14px;
                        }

                        @media (551px <=width <=900px) {
                            font-size: 16px;
                        }
                    }

                    .scopeCount {
                        font-weight: 600;
                        background-color: #fff;
                        width: fit-content;
                        color: #000;
                        border-radius: 50px;
                        padding: 5px 10px;

                        @media (width <=550px) {
                            font-size: 14px;
                        }

                        @media (551px <=width <=900px) {
                            font-size: 16px;
                        }
                    }

                    &:hover {
                        background-color: #00ADEF33;
                    }
                }

            }

            .scope_content {
                .strategy-scope-tabs {
                    display: flex;

                    @media (width <=900px) {
                        display: block;
                    }
                }

                .left_side {
                    width: 230px;
                    border-right: 1px solid #fff;
                    padding-right: 20px;

                    @media (width <=900px) {
                        width: 100%;
                        display: flex;
                        border-right: none !important;
                        padding-right: 0 !important;
                    }

                    .active {
                        background-color: #00ADEF33;
                    }

                    .scope_dimension,
                    .scope_metrices {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 20px;
                        border-bottom: 1px solid #FFFFFF66;
                        cursor: pointer;
                        transition: 0.3s all ease-in-out;

                        @media (width <=900px) {
                            width: 50%;
                            justify-content: center;
                            gap: 10px;
                        }

                        &:hover {
                            background-color: #00ADEF33;
                        }

                        svg {
                            transform: rotate(-90deg);

                            @media (width <=900px) {
                                transform: rotate(0deg);
                            }
                        }
                    }
                }

                .right_side {
                    background-color: #fff;
                    width: 100%;
                    min-height: calc(90vh - 165px);
                    max-height: calc(90vh - 165px);
                    overflow-y: auto;

                    @media (width <=900px) {
                        min-height: calc(90vh - 225px);
                        max-height: calc(90vh - 225px);
                    }

                    overflow-y: auto;
                    width: 100%;

                    .default {
                        font-size: 20px;
                        color: #000;
                        font-weight: 600;
                        text-align: center;
                        margin-top: 100px;
                    }

                    .scope_dimension_show,
                    .scope_metrices_show {
                        display: flex;
                        gap: 20px;
                        align-items: center;
                        padding: 10px 20px;
                        color: #000000;
                        font-size: 16px;
                        font-weight: 600;
                        background-color: #fff;
                        border-bottom: 1px solid #0000000D !important;
                        cursor: pointer;

                        &:hover {
                            background-color: #F9F9F9;
                        }

                        .name {
                            text-transform: uppercase;
                            color: #000000;
                            font-size: 16px;
                            font-weight: 600;
                        }

                        // &:nth-child(odd) {
                        //     background-color: #0000000D;

                        //     &:hover {
                        //         background-color: #0000001a;
                        //     }
                        // }
                    }
                }
            }

            .move-pre-arrow {
                position: fixed;
                left: 12px;
                z-index: 1;
                max-width: 40px;
                min-width: 40px;
                height: 60px;
                background-color: #031940;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;

                .icon {
                    max-width: 30px;
                    min-width: 30px;
                    max-height: 30px;
                    min-height: 30px;
                    border-radius: 50%;
                    background-color: #00ADEF;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    img {
                        transform: rotate(-180deg);
                    }
                }

            }

            .move-next-arrow {
                position: fixed;
                right: 12;
                z-index: 1;
                max-width: 40px;
                min-width: 40px;
                height: 60px;
                background-color: #031940;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;

                .icon {
                    max-width: 30px;
                    min-width: 30px;
                    max-height: 30px;
                    min-height: 30px;
                    border-radius: 50%;
                    background-color: #00ADEF;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
        }

        .whiteBtn {
            background-color: #FFFFFF;
            border-radius: 15px;
            padding: 10px 20px;
            color: #00ADEF;
            font-size: 16px;
            font-weight: 600;
        }

    }

    &_conditionGroup {

        .include,
        .exclude {
            background-color: #031940;
            padding: 10px 20px;
            border-radius: 15px;
            margin-bottom: 1.25rem;
            cursor: pointer;

            button {
                display: flex;
                align-items: center;
                gap: 10px;

                img {
                    width: 14px;
                    height: 14px;
                }

                span {
                    font-size: 18px;
                    font-weight: 400;
                }
            }
        }
    }
}