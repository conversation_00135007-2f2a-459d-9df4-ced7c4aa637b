// @import "../../assets/theme/var";
@import "../../assets/theme/_var.scss";

.w-md-100 {
    @media (max-width: 991px) {
        width: 100%;
    }
}

.p-md-200 {
    @media (max-width: 991px) {
        padding: 0px 200px;
    }

    @media (max-width: 700px) {
        padding: 0px 0px;
    }
}

.errorPage {
    height: calc(70vh - 90px);
    padding: 50px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-inline: 1rem;

    &_inner {
        display: flex;
        align-items: center;
        justify-content: center;
        align-items: center;

        @media (max-width: 991px) {
            flex-direction: column;
        }

        figure {
            width: 300px;

            @media (max-width: 991px) {
                width: 200px;
            }
        }
    }

    &_content {
        padding-left: 2rem;

        @media (max-width: 991px) {
            padding-left: 0;
            text-align: center;
            padding-top: 2rem
        }

        p {
            margin-bottom: 1rem;
            font-size: 1.25rem;

            @media (max-width: 991px) {
                font-size: 1rem;
            }
        }

        .commonSearch {
            position: relative;

            @media (max-width: 991px) {
                width: 100%;
                max-width: 400px;
                justify-content: center;
                margin: 0 auto;

                .form-control {
                    width: 100%;
                }
            }
        }
    }
}

.w-400px {
    width: 400px !important;
}