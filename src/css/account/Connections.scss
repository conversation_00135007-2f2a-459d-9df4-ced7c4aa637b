@use "../theme/var";

.connections_sec {
  .table {
    &_heading {
      border: 1px solid var.$borderclr;
      border-radius: 15px;
      padding: 10px 1.25rem;
    }
  }

  .commonSearch {
    .form-control {
      min-height: 40px;
      font-size: 1rem;
      border: 0;

      @media (max-width: 767px) {
        width: 100%;
        font-size: 14px;
      }

      &:focus {
        box-shadow: none;
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
        opacity: 1;
      }
    }
  }

  .submit-request {
    h6 {
      font-size: 18px;
      font-weight: 400;
    }

    .submit_request {
      color: var.$baseclr;
      font-weight: 700;

      svg {
        transition: all 0.3s ease-in-out;
      }

      &:hover {
        color: var.$yellow;

        svg {
          margin-left: 5px;

          path {
            fill: var.$yellow;
          }
        }
      }
    }
  }

  .connetionTable {
    .common_blackcard_innerbody {
      margin-top: 20px;
      max-height: 400px;
      overflow-y: scroll;
    }
  }
}