@use "../theme/var";

.account_details {
  .account_card_list {
    .form-control {
      color: #fff !important;
    }

    // .form-select {
    //   --bs-form-select-bg-img: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e);
    // }

    ul {
      li {
        justify-content: flex-start !important;


        @media screen and (max-width: 767px) {
          justify-content: space-between;
        }

        span {
          padding-right: 15px;

          // @media screen and (min-width: 768px) {
          //   width: 250px;
          // }
        }
      }
    }
  }
}

.add_plusicon {
  button {
    svg {
      width: 16px;
      height: 16px;
      margin-right: 10px;

      path {
        fill: var.$baseclr;
      }
    }
  }
}

.account_setup_phone_number {
  label {
    cursor: pointer;

    p {
      font-weight: 300;
    }
  }
}



.new-address-section {
  ul {
    li {
      border-bottom: 1px solid #666;
      padding-bottom: 1.25rem;
      align-items: center;

      &:last-child {
        padding-bottom: 0 !important;
        border-bottom: none !important;
      }
    }
  }

  .show-address-details {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .name {
      font-size: 1.125rem !important;
      font-weight: 600 !important;
      color: #fff;
      margin-top: 0 !important;
    }

    .address,
    .city {
      font-size: 1rem;
      font-weight: 400;
      color: #fff;
      margin-top: 0 !important;
    }

    .remove-address-confirmation {
      p {
        font-size: 1.125rem !important;
        font-weight: 600 !important;
        color: #fff;
      }

      .btns {
        margin-top: 10px;
        display: flex;
        align-items: center;
        gap: 10px;

        .btn-style {
          min-height: 40px;
        }
      }

    }
  }

  .btns {
    display: flex;
    align-items: center;
    gap: 10px;

    button {
      display: flex;
      align-items: center;
      gap: 10px;

      svg {
        width: 16px;
        height: 16px;

        path {
          fill: var.$baseclr;
        }
      }

      span {
        color: #00adef;
        font-size: 18px;
        padding: 0;
        font-weight: 600;
        font-family: Gilroy-Semibold, sans-serif;
        transition: all .3s ease-in-out;
        white-space: nowrap;
      }

      &:hover {
        span {
          color: var.$yellow;
        }

        svg {
          path {
            fill: var.$yellow;
          }
        }
      }
    }
  }
}

.border-bottom-none {
  border-bottom: 0 !important;
}

// Account header layout with status indicator
.account_header_main {
  display: flex;
  flex-direction: column;
  gap: 8px;

  h6 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #fff;
    line-height: 1.2;
  }

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    gap: 16px;
  }
}

.account_status_indicator {
  display: flex;
  align-items: center;

  @media (max-width: 767px) {
    margin-left: 0;
  }
}

// Status indicator styles for account details components
.status_indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease-in-out;

  span {
    font-weight: 500;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.9);
    white-space: nowrap;
    line-height: 1;
  }

  svg,
  img {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  // State-specific styling
  &.status-loading {
    background-color: rgba(0, 173, 239, 0.1);
    border-color: rgba(0, 173, 239, 0.2);

    span {
      color: #00adef;
    }
  }

  &.status-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: rgba(40, 167, 69, 0.2);

    span {
      color: #28a745;
    }
  }

  &.status-error {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.2);

    span {
      color: #dc3545;
    }
  }

  &.status-default {
    background-color: rgba(108, 117, 125, 0.1);
    border-color: rgba(108, 117, 125, 0.2);

    span {
      color: #fff;
    }
  }

  @media (max-width: 767px) {
    padding: 3px 6px;

    span {
      font-size: 12px;
    }

    svg,
    img {
      width: 14px;
      height: 14px;
    }
  }
}