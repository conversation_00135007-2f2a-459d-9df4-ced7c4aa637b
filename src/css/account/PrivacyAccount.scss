@use "../theme/var";

.step_verification {
  &_content {
    padding-right: 1rem;

    @media (max-width: 767px) {
      padding-right: 0;

      p {
        font-size: 14px;
        ;
        line-height: 20px;
        margin-bottom: 10px;
      }
    }
  }
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background-color: #9c9a9f;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked+.slider {
  background-color: #0099d1;
}

input:checked+.slider:before {
  transform: translateX(22px);
  background-color: white;
}

.privacy_sec {
  .sms-update {
    margin-top: 10px;

    .btn-style {
      min-height: 40px;
      margin-top: 10px;
      font-size: 16px !important;
    }

    .add-number {
      font-weight: 700 !important;
      color: #00adef !important;
    }
  }
}