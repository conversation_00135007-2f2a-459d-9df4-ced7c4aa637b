@use "../theme/var";

.payment_methods {
  .account_card_list {
    ul {
      li {
        justify-content: flex-start;

        span {
          padding-right: 15px;

          @media screen and (min-width: 768px) {
            width: 250px;
          }
        }
      }
    }

    p {
      color: var.$textclr;
    }

    .payment_redeem {
      .form-control {
        width: 100%;
      }

      ::placeholder {
        color: #fff;
        opacity: 1;
      }

      .btn-style {
        font-size: 14px;
        font-weight: 600;
        line-height: 26px;
        min-width: 100px !important;
        padding: 8px 12px !important;
        min-height: 40px;

        @media (max-width: 991px) {
          font-size: 14px;
          line-height: 24px;
          min-width: 130px;
          padding: 8px 15px;
          min-height: 52px;
        }
      }

      .error-messages {
        .success {
          color: #32CD33;
        }

        .invalid {
          color: #ff696a;
        }
      }
    }
  }
}

.add_payment_method,
.manage-payment-method {
  .card-details {
    border-right: 1px solid #fff;

    @media (max-width: 991px) {
      border-right: 0 !important;
      border-bottom: 1px solid #fff;
      padding-bottom: 1rem;
    }
  }

  label {
    font-size: 18px !important;
    font-weight: 500 !important;
    color: #fff !important;
    margin-bottom: 8px;
  }

  .payment-method-images {
    display: flex;
    align-items: center;
    gap: 12px;

    img {
      height: 50px !important;
    }
  }

  .address-details {
    position: relative;

    .choose-billing {
      font-size: 18px;
      font-weight: 500;
      cursor: pointer;
      color: #00ADEF;
    }

    .show-save-address {
      position: absolute;
      top: 30px;
      z-index: 9999;
      width: 100%;
      background-color: #fff;
      border-radius: 15px;
      padding: 10px 0;

      ul {
        li {
          display: flex;
          justify-content: start !important;
          gap: 10px;
          border-bottom: 1px solid #00000033;
          padding: 10px;
          margin-bottom: 0 !important;
          cursor: pointer;

          &:hover {
            background-color: #d8d8d8;
          }

          span {
            font-size: 14px;
            font-weight: 600;
            color: #000000CC;
          }

        }
      }

      button {
        width: 100%;
        margin: 10px;
        margin-bottom: 0 !important;
        display: flex;
        gap: 5px;
        align-items: center;

        span {
          font-size: 14px;
          font-weight: 700;
          color: #00ADEF;
        }

        svg {
          width: 16px;
          height: 16px;

          path {
            fill: #00ADEF;
          }
        }
      }
    }

    .show-selected-address {
      display: flex;
      justify-content: space-between;
      background-color: #00ADEF33;
      border-radius: 15px;
      padding: 15;

      svg {
        width: 16px;
        height: 16px;

        path {
          stroke: #fff;
        }
      }
    }

    .bottom-area {
      position: absolute;
      bottom: 0;

      p {
        font-size: 18px;
        font-weight: 400;
        color: #fff;

        a {
          text-decoration: underline !important;
        }
      }

      button {
        border-radius: 10px;
        min-height: 40px !important;
      }
    }
  }

  .remove-payment-confirmation {
    p {
      font-size: 1.125rem !important;
      font-weight: 600 !important;
      color: #fff;
    }

    .btns {
      margin-top: 10px;
      display: flex;
      align-items: center;
      gap: 10px;

      .btn-style {
        min-height: 40px;
      }
    }

  }
}