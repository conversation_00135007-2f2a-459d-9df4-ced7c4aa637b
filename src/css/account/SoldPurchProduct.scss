@use "../theme/var";

.account {
  &_card {
    &_sold,
    &_purchased {
      color: var.$black !important;
      .mini_card {
        border: 1px solid #********;
        padding: 10px;
        border-radius: 15px;
        margin-bottom: 10px;
      }

      .main_inform {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-bottom: 6px;
      }
      .star-rating {
        font-size: 14px;
        font-weight: 600;
      }
      .most_recent {
        color: var.$black !important;
        font-size: 14px;
        font-weight: 600;
      }
      .respon_sell_feedback {
        align-items: start;
        gap: 17px;

        @media (max-width: 700px) {
          display: block;

          h6 {
            margin-top: 12px;
          }
          .activeListing_photo {
            max-width: 100%;
            height: 150px;
          }
        }
      }
      .activeListing_photo {
        width: 100%;
        aspect-ratio: 1 / 1;
        max-width: 158px;
        border-radius: 5px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      h6 {
        color: var.$black !important;
        font-size: 18px;
        font-weight: 400;
        font-family: "Gilroy-Semibold", sans-serif;
      }
      .inner_price_text {
        color: var.$black !important;
        font-size: 15px;
        font-weight: 600;
      }
      .actions_btn {
        margin-top: 15px;
        display: flex;
        justify-content: end;
        gap: 8px;

        @media (min-width: 701px) {
          flex-wrap: wrap;
        }
        @media (max-width: 700px) {
          flex-direction: column-reverse;

          button {
            width: 48%;
          }
        }
        div {
          flex-wrap: wrap;
          display: flex;
          justify-content: end;
          gap: 8px;
        }
      }
      .round-border-btn,
      .rounded-border-btn {
        border: 1px solid #00000033;
        background-color: #0000000d;
        padding: 7px 10px;
        gap: 5px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 50px;
        display: flex;
        align-items: center;
        transition: all 0.4s ease-in-out;
        justify-content: center;

        &:hover {
          border: 1px solid rgba(0, 0, 0, 0.477);
        }
      }
      .rounded-border-btn {
        padding: 10px 18px 10px 18px !important;
      }
      .round-bluefill-btn {
        background-color: var.$baseclr;
        color: white;
        padding: 5px 15px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 50px;
        transition: 0.3s ease-in-out;

        &:hover {
          background-color: var.$baseclrhover;
        }
      }
      .round-redfill-btn {
        background-color: var.$redlightclr;
        color: white;
        padding: 5px 15px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 50px;
        transition: 0.3s ease-in-out;

        &:hover {
          background-color: var.$redbghover;
        }
      }
      .round-greenfill-btn {
        background-color: var.$green;
        color: white;
        padding: 5px 15px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 50px;
        transition: 0.3s ease-in-out;

        &:hover {
          background-color: var.$greenbtnhover;
        }
      }
    }
  }
}
