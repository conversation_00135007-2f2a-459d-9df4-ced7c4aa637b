.form-check-input {
  width: 72px !important;
  height: 34px !important;
  border-radius: 30px !important;
  position: relative;
  background-color: #fff !important;
  border: none !important;
  cursor: pointer;
  appearance: none;
  transition: background-color 0.3s ease;
}

.form-check-input::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  top: 2px;
  left: 2px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform 0.3s ease-in-out;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

.form-check-input:checked {
  background-color: #00adef !important;
}

.form-check-input:checked::before {
  transform: translateX(38px);
}

.form-check-input:focus {
  box-shadow: none !important;
}