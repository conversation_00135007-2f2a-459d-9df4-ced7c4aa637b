@use "../theme/var";

.our_mission {
  padding: 170px 0 205px;
  position: relative;
  z-index: 1;
  background: linear-gradient(180deg, #000000 74.64%, #001331 90%);
  mix-blend-mode: screen;

  @media (min-width: 1500px) {
    // min-height: 50vw;
  }

  @media (max-width: 767px) {
    padding: 50px 0;
  }

  &::before {
    @media (max-width: 767px) {
      content: "";
      position: absolute;
      top: 0%;
      left: 0;
      width: 100%;
      height: 75%;
      background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-maximize-profits.png");
      background-position: top;
      background-repeat: no-repeat;
      z-index: 0;
    }
  }

  &_content {
    max-width: 900px;
    margin: 0 auto;

    p {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      letter-spacing: -1px;
      text-align: left;
      padding-top: 2rem;
      width: 100%;

      @media (max-width: 767px) {
        font-size: 20px;
        line-height: 30px;
      }

      @media (max-width: 575px) {
        font-size: 18px;
        line-height: 28px;
      }
    }
  }
}