@use "../theme/var";

.testimonials_sec {
  // background-color: $clr001331;
  padding-bottom: 150px;
  padding-top: 85px;
  position: relative;
  z-index: 1;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 60%;
    border-radius: 2rem;
    z-index: -1;
  }

  @media (max-width: 1199px) {
    padding-bottom: 80px;
  }

  @media (max-width: 767px) {
    padding-bottom: 70px;
    background-color: #001331;
    padding-top: 50px;
  }

  &_content {
    @media (max-width: 767px) {
      // text-align: center;
      padding-bottom: 30px;
    }

    h3 {
      font-size: 3rem;
      font-weight: 800;
      margin-bottom: 20px;

      @media (max-width: 1199px) {
        font-size: 2.5rem;
      }

      @media (max-width: 767px) {
        font-size: 2rem;
      }
    }

    p {
      font-size: 22px;
      font-weight: 600;

      @media (max-width: 767px) {
        font-size: 18px;
      }
    }
  }

  .slider-container {
    max-width: 850px;
    margin-left: auto;

    .slick-slider {
      .slick-list {
        .slick-track {
          .slick-slide {
            padding-left: 60px;
            padding-top: 100px;

            @media (max-width: 991px) {
              padding-top: 0;
              padding-left: 20px;
            }

            @media (max-width: 767px) {
              padding-left: 10px;
            }

            @media (max-width: 575px) {
              padding-left: 0;
            }

            &.slick-active {
              padding-top: 0;

              &.slick-current {
                padding-top: 100px;

                @media (max-width: 991px) {
                  padding-top: 0;
                }
              }
            }
          }
        }
      }

      .slick-arrow {

        &.slick-prev,
        &.slick-next {
          position: absolute;
          bottom: -30px;
          left: auto;
          right: 0;
          top: auto;
          width: 50px;
          height: 50px;
          border-radius: 10rem;
          background-color: var.$baseclr;
          z-index: 2;

          @media (max-width: 991px) {
            bottom: -99px;
          }

          @media (max-width: 767px) {
            width: 40px;
            height: 40px;
            bottom: -70px;
            top: auto;
          }
        }

        &.slick-prev {
          right: 70px;

          @media (max-width: 767px) {
            right: 60px;
          }
        }
      }
    }
  }

  .bottom-btn-0 {
    @media (min-width: 1200px) {
      position: absolute !important;
      bottom: -3px;
      left: auto;
      right: 0 !important;

    }

    @media (max-width: 1200px) {
      display: flex;
    }
  }

  .testimonial_card {
    background-color: var.$white;
    padding: 3rem 3rem;
    display: flex !important;
    justify-content: space-between;
    flex-direction: column;
    min-height: 450px;

    @media (min-width: 991px) {
      max-width: 380px;
    }

    @media (max-width: 991px) {
      max-width: 100%;
      padding: 2rem 1.5rem;
    }

    p {
      color: var.$black;
      font-size: 20px;
      padding: 2.5rem 0;
      // max-width: 250px;
      margin: 0 auto;
    }

    h3 {
      color: var.$baseclr;
      font-size: 1.5rem;
      margin-bottom: 0;
      text-align: right;
      position: relative;

      @media (max-width: 991px) {
        padding-right: 2rem;
      }

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        right: -3rem;
        width: 40px;
        height: 30px;
        background-color: var.$baseclr;

        @media (max-width: 991px) {
          right: -24px;
          width: 30px;
        }
      }
    }
  }
}