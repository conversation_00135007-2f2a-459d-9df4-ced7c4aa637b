@use "../theme/var" as *;

.trades_tabs {
    position: relative;
    z-index: 1;
    background-color: $clr042053;

    .tab-content {
        margin-top: 5rem;

        @media screen and (max-width: 991px) {
            margin-top: 3rem;
        }

        h3 {
            padding: 0 1rem;
            font-size: 2.8rem;
            font-weight: 800;
        }
    }

    &_content {
        figure {
            img {
                display: block;
                margin: 0 auto;

                @media screen and (max-width: 991px) {
                    height: 500px;
                    object-fit: cover;
                }

                @media screen and (max-width: 767px) {
                    height: 300px;
                    object-fit: cover;
                }
            }
        }
    }
}

.icons_big_tabs {

    @media (max-width: 767px) {
        .position-relative {
            max-width: 260px;
            margin: 0 auto;
        }
    }

    .big_tabs {

        @media (max-width: 767px) {
            display: flex;
            flex-wrap: nowrap;
            white-space: nowrap;
            overflow-x: hidden;
        }

        &.nav {
            margin: 0 -10px;

            @media (max-width: 767px) {
                margin: 0 10px;
                // padding: 0 40px;
            }

            .nav-item {
                display: flex;
                width: 16.66%;
                padding: 0 10px;

                @media (max-width: 1199px) {
                    width: 33.33%;
                    padding: 10px 10px;
                }

                @media (max-width: 767px) {
                    width: 100%;
                    min-width: 240px;
                }
            }
        }
    }

    .tab-content {
        margin-top: 5rem;

        @media screen and (max-width: 991px) {
            margin-top: 3rem;
        }
    }

    .scroll-btn {
        background-color: $baseclr;
        color: white;
        border: none;
        padding: 0;
        cursor: pointer;
        font-size: 1.2rem;
        min-width: 30px;
        min-height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10rem;
        position: absolute;
        display: none;
        top: 50%;
        transform: translateY(-50%);

        @media (max-width: 767px) {
            display: flex;
        }

        &.left {
            left: -30px;

            svg,
            img {
                transform: rotate(180deg);
            }

        }

        &.right {
            right: -30px;
        }

        &:hover {
            background-color: $baseclr;
        }

        &.disabled,
        &:disabled {
            background-color: #414c60;
        }
    }
}