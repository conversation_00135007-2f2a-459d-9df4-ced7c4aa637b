@use "../theme/var";

.answer_trades {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
  background-color: var.$clr042053;
  position: relative;
  z-index: 1;

  @media (max-width: 767px) {
    padding-top: 3.125rem;
    padding-bottom: 3.125rem;
  }

  &::after {
    content: "";
    position: absolute;
    top: -20%;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png");
    background-position: center;
    background-repeat: no-repeat;
    z-index: -1;
  }

  &_heading {
    h3 {
      color: var.$baseclr;
      font-weight: 600;
      line-height: normal;
      margin-bottom: 0.625rem;
      text-align: center;
      font-size: 2.8rem;

      @media (max-width: 991px) {
        margin-top: 5px;
        font-size: 1.65rem;
      }
    }
  }

  &_contentbox {
    padding: 3rem 0 3.25rem;

    @media (max-width: 767px) {
      padding-bottom: 0;
    }

    &_content {
      span {
        height: 60px;
        // text-align: center;
        display: flex;
        justify-content: center;
      }

      h4 {
        margin: 1.25rem 0 1rem;
        font-size: 26px !important;
        font-weight: 600;

        @media (max-width: 767px) {
          margin-bottom: 10px;
        }
      }

      p {
        line-height: 27px;
        font-weight: 600;
        font-size: 18px;

        @media (max-width: 767px) {
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
        }
      }

      @media (max-width: 991px) {
        margin-bottom: 40px;
      }
    }
  }

  &_logoicon {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}