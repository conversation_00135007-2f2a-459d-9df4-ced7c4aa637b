@use "../theme/var";

.learning_resources {
  padding: 84px 0;
  background: radial-gradient(
      50% 50% at 50% 50%,
      rgba(0, 185, 255, 0.4) 21.5%,
      rgba(0, 83, 153, 0.4) 100%
    ),
    linear-gradient(
      135deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.15) 47.5%,
      rgba(255, 255, 255, 0) 100%
    );
  position: relative;
  z-index: 1;

  @media (max-width: 767px) {
    padding: 60px 0;
  }

  &_heading {
    max-width: 780px;
    margin: 0 auto;

    h2 {
      text-align: left;

      @media (max-width: 767px) {
        text-align: center;
      }
    }

    p {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      letter-spacing: -1px;
      text-align: left;
      padding-top: 2rem;

      @media (max-width: 767px) {
        text-align: center;
        padding-top: 1rem;
        font-size: 18px;
        line-height: 24px;
      }
    }
  }

  &_content {
    max-width: 964px;
    margin: 0 auto;
    padding-top: 50px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    @media (max-width: 767px) {
      padding-top: 30px;
    }

    &_col {
      width: 50%;
      padding: 0 35px;

      @media (max-width: 991px) {
        padding: 0 25px;
      }

      @media (max-width: 767px) {
        width: 100%;
        padding: 20px 0 0;
      }
    }

    &_card {
      height: 350px;
      border: 4px solid var.$baseclr;
      border-radius: 70px;
      position: relative;
      z-index: 1;
      overflow: hidden;
      display: block;

      @media (max-width: 767px) {
        height: 150px;
        border-radius: 2rem;
        // width: 100%;
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0%;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 70px;
        z-index: 0;

        @media (max-width: 767px) {
          border-radius: 2rem;
        }
      }

      img {
        border-radius: 70px;
        height: 100%;
        width: 100%;
        object-fit: cover;
        transition: all ease-in-out 0.3s;

        @media (max-width: 767px) {
          border-radius: 2rem;
        }
      }

      &:hover {
        img {
          transform: scale(1.1);
        }
      }

      h3 {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: var.$white;
        font-weight: 600;
        z-index: 1;
        text-align: center;
        width: 100%;
        font-size: 3rem;

        @media (max-width: 991px) {
          font-size: 35px;
        }

        @media (max-width: 767px) {
          font-size: 18px;
        }
      }
    }
  }
}
