@use "../theme/var";

.commonPolicy {
  padding: 4rem 0;
  position: relative;

  h2,
  h1 {
    color: var.$white;
    font-weight: 600;
    line-height: 140%;
    letter-spacing: 0.2px;
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  h3,
  h4 {
    color: var.$white;
    font-weight: 700;
    margin-top: 1.25rem;
    margin-bottom: 0.75rem;
  }

  h5 {
    color: var.$white;
    font-weight: 700;
    margin-top: 1rem;
    margin-bottom: 0.75rem;
  }

  h4,
  h3 {
    font-weight: 600;
    line-height: 140%;
    letter-spacing: 0.2px;
  }

  p {
    font-size: 1.125rem;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: 0.2px;
    color: var.$clrc5c5d5;
    margin-bottom: 15px;

    @media screen and (max-width: 991px) {
      font-size: 1rem;
    }

    strong {
      font-weight: 600;
      opacity: 1;
      color: var.$white;
      font-size: 1.125rem;

      @media screen and (max-width: 991px) {
        font-size: 1rem;
      }
    }
  }

  strong {
    font-weight: 600;
    opacity: 1;
    font-size: 1.125rem;

    @media screen and (max-width: 991px) {
      font-size: 1rem;
    }
  }

  ul {
    list-style: disc;
    margin-left: 2rem;
    margin-top: 1rem;
    margin-bottom: 1rem;

    li {
      color: var.$clrc5c5d5;
      font-size: 1.125rem;
      font-weight: 500;
      line-height: 140%;
      letter-spacing: 0.2px;
      padding: 2px 0;

      @media screen and (max-width: 991px) {
        font-size: 1rem;
      }
    }
  }

  ol {
    padding-left: 1.6rem;

    li {
      list-style: auto;
      color: var.$clrc5c5d5;
      font-size: 1.125rem;
      font-weight: 500;
      line-height: 140%;
      letter-spacing: 0.2px;
      padding: 5px 0;

      @media screen and (max-width: 991px) {
        font-size: 1rem;
      }

      &::marker {
        font-size: 1rem;
      }

      ol {
        padding-left: 0;

        li {
          &::marker {
            font-size: 1rem;
          }

          ul {
            li {
              list-style: disc;
            }
          }
        }
      }
    }
  }

}

.inner_content {
  counter-reset: alphabet-counter;
}