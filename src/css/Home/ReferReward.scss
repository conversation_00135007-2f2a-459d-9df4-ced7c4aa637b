@use "../theme/var";

.refer_reward {
  position: relative;
  z-index: 1;
  padding: 50px 0;

  &::after {
    content: "";
    position: absolute;
    top: 0%;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png");
    background-position: center;
    background-repeat: no-repeat;
    z-index: -1;
  }

  .container {
    max-width: 1130px;
  }

  &_logo {
    @media screen and (max-width: 991px) {
      width: 100px;
      margin-right: 1rem;
    }

    @media screen and (max-width: 767px) {
      width: 70px;
      margin-right: 1rem;
    }
  }

  &_heading {
    h1 {
      font-size: 80px;
      font-weight: 800;
      line-height: 90px;

      @media screen and (max-width: 991px) {
        font-size: 40px;
        line-height: 50px;
      }

      @media screen and (max-width: 767px) {
        font-size: 28px;
        line-height: 36px;
      }
    }

    h5 {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      letter-spacing: -1px;
      text-align: center;
      max-width: 630px;
      margin: 0 auto;

      @media screen and (max-width: 991px) {
        font-size: 18px;
        line-height: 28px;
      }

      @media (max-width: 767px) {
        font-size: 15px;
        line-height: 23px;
      }
    }
  }
}