"use client";
import 'bootstrap/dist/css/bootstrap.min.css';
import SideBar from "@/Components/common/Dashboard/SideBar";
import Header from "@/Components/UI/Header";
import Footer from "@/Components/UI/Footer";
import "../css/dashboard/layout.scss";
import "@/css/dashboard/Dashboard.scss";
import 'bootstrap/dist/css/bootstrap.min.css';
import "@/css/app.scss";
import { LanguageProvider } from "@/context/LanguageContext";
import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";

const DashboardLayout = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const token = Cookies.get("authToken");

    if (!token) {
      router.replace("/login");
    } else {
      setIsLoading(false);
    }
  }, [router]);

  if (isLoading) return null;

  return (
    <LanguageProvider>
      <Header />
      <main className="admin_layout">
        <div className="admin_layout_sidebar">
          <SideBar />
        </div>
        <div className="admin_layout_content">{children}</div>
      </main>
      <Footer />
    </LanguageProvider>

  );
};

export default DashboardLayout;
