import 'bootstrap/dist/css/bootstrap.min.css';
import { Container } from "react-bootstrap";
import "../css/auth/authGlobals.scss";
import "../css/common/CommonButton.scss"



export default function AuthLayout({ children }) {
  return (
    <div className="loginCommon">
      <Container fluid className="px-0">
        <main className="mx-0 d-flex flex-wrap">
          <div className="px-0 referralCol d-none d-lg-flex">
            <img src="https://cdn.tradereply.com/dev/site-assets/tradereply-financial-tools.jpg" alt="Access your TradeReply account – Log in, sign up, or recover credentials" />
          </div>
          <div className="px-0 loginCol">{children}</div>
        </main>
      </Container>
    </div>
  );
}
