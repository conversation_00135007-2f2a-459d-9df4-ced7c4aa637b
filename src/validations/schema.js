import * as Yup from "yup";
import { errorMessages } from "./errorMessages";

export const loginSchema = Yup.object({
  email: Yup.string()
    .email(errorMessages.EMAIL_INVALID)
    .matches(/\.[a-zA-Z]{2,4}$/, errorMessages.EMAIL_INVALID)
    .required(errorMessages.EMAIL_REQUIRED),
  password: Yup.string()
    .required(errorMessages.PASSWORD_REQUIRED)
  // .min(8, errorMessages.PASSWORD_MIN_LENGTH)
  // .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)
  // .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)
  // .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)
  // .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),
});

export const signupSchema = Yup.object({
  email: Yup.string()
    .trim()
    .email(errorMessages.EMAIL_INVALID)
    .required(errorMessages.EMAIL_REQUIRED)
    .matches(/\.[a-zA-Z]{2,4}$/, errorMessages.EMAIL_INVALID),
  password: Yup.string()
    .trim()
    .required(errorMessages.PASSWORD_REQUIRED)
    .min(8, errorMessages.PASSWORD_MIN_LENGTH)
    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)
    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)
    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)
    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),
  password_confirmation: Yup.string()
    .trim()
    .oneOf([Yup.ref('password'), null], errorMessages.PASSWORD_CONFIRMATION)
    .required(errorMessages.PASSWORD_CONFIRMATION_REQUIRED),
});

export const changePasswordSchema = Yup.object({
  new_password: Yup.string()
    .trim()
    .required(errorMessages.PASSWORD_REQUIRED)
    .min(8, errorMessages.PASSWORD_MIN_LENGTH)
    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)
    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)
    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)
    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),
  confirm_new_password: Yup.string()
    .trim()
    .oneOf([Yup.ref('new_password'), null], errorMessages.PASSWORD_CONFIRMATION)
    .required(errorMessages.PASSWORD_CONFIRMATION_REQUIRED),
});

export const localAccountSchema = Yup.object({
  emailOrUsername: Yup.string()
    .test("is-email-or-username", "Enter a valid email or username", (value) => {
      if (!value) return false; // Reject empty input

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Standard email pattern
      const usernameRegex = /^[a-zA-Z0-9_.]+$/; // Allows letters, numbers, underscores, and dots

      return emailRegex.test(value) || usernameRegex.test(value);
    })
    .required("Email or username is required"),
});


export const forgetSchema = Yup.object({
  email: Yup.string()
    .email(errorMessages.EMAIL_INVALID)
    .required(errorMessages.EMAIL_REQUIRED),
});


export const createUsernameSchema = Yup.object().shape({
  name: Yup.string()
    .trim()
    .required("This field is required")
    .max(100, "User name cannot exceed 100 characters"),
  // .test("unique", "This username is already taken. Please choose another one."),
});
export const securitySchema = Yup.object().shape({
  security_code: Yup.string()
    .trim()
    .required("This field is required")
});
export const resetSchema = Yup.object({
  password: Yup.string()
    .trim()
    .required(errorMessages.PASSWORD_REQUIRED)
    .min(8, errorMessages.PASSWORD_MIN_LENGTH)
    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)
    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)
    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)
    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref("password")], "Passwords must match")
    .required("Password confirmation is required"),
});

export const blogValidationSchema = Yup.object().shape({
  title: Yup.string()
    .trim()
    .required("Title is required")
    .min(5, "Title must be at least 5 characters")
    .max(100, "Title cannot exceed 100 characters"),
  content: Yup.string()
    .trim()
    .required("Content is required")
    .min(50, "Content must be at least 20 characters")
    .max(1000, "Content cannot exceed 100 characters"),
});

export const titleDescValidationSchema = Yup.object().shape({
  title: Yup.string()
    .trim()
    .required("Title is required")
    .min(5, "Title must be at least 5 characters")
    .max(100, "Title cannot exceed 100 characters"),
  content: Yup.string()
    .trim()
    .required("Content is required")
    .min(50, "Content must be at least 20 characters")
    .max(1000, "Content cannot exceed 100 characters"),
});

export const ArticleSchema = Yup.object({
  primary_category_id: Yup.string().required("Primary Category is required"),
  // secondary_category_id: Yup.array()
  //   .of(Yup.string())
  //   .min(1, "Select at least one secondary category")
  //   .required("Secondary Category is required"),
  title: Yup.string().required("Page Title is required"),
  title: Yup.string().required("Image URL is required"),

  content: Yup.string().required("Body Text is required"),
  summary: Yup.string()
    .max(250, "Max 250 characters allowed")
    .required("Page Summary is required"),
});
export const checkoutSchema = Yup.object({
  firstName: Yup.string()
    .trim()
    .required("This field is required")
    .max(100, "First name cannot exceed 100 characters"),
  lastName: Yup.string()
    .trim()
    .required("This field is required")
    .max(100, "Last name cannot exceed 100 characters"),
  country: Yup.string()
    .trim()
    .required("This field is required"),
  address: Yup.string()
    .trim()
    .required("This field is required")
    .max(100, "Address cannot exceed 100 characters"),
  // cardNumber: Yup.string()
  //   .matches(/^\d{4} \d{4} \d{4} \d{4}$/, "Card number must be in format 1234 5678 9012 3456")
  //   .required("Card number is required"),
  // expireDate: Yup.string()
  //   .matches(/^(0[1-9]|1[0-2]) \/ \d{4}$/, "Date must be in MM / YYYY format")
  //   .required("This field is required"),

  // securityCode: Yup.string()
  //   .matches(/^\d{3}$/, "Code must be 3 digits")
  //   .required("This field is required"),
});