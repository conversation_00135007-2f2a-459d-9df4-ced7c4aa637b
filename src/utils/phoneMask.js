/**
 * Mask phone number for display
 * Shows only last 2 digits: ********01
 * Follows project requirements for phone number display
 */
export const maskPhone = (phoneNumber) => {
    if (!phoneNumber) return 'Add Phone Number';
    
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    if (cleaned.length < 2) {
        return phoneNumber;
    }

    const lastTwoDigits = cleaned.slice(-2);
    const starsCount = Math.max(cleaned.length - 2, 8);
    
    return '*'.repeat(starsCount) + lastTwoDigits;
};



/**
 * Validate phone number format
 */
export const validatePhoneNumber = (phoneNumber) => {
    if (!phoneNumber || !phoneNumber.trim()) {
        return 'Phone number is required';
    }
    
    // Remove all non-digit characters for validation
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    if (cleaned.length < 7) {
        return 'Phone number is too short';
    }
    
    if (cleaned.length > 15) {
        return 'Phone number is too long';
    }
    
    return null; // Valid
};

/**
 * Clean phone number (remove all non-digit characters except +)
 */
export const cleanPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return '';
    return phoneNumber.replace(/[^\d+]/g, '');
};
