/**
 * Environment Detection Utility
 * 
 * Provides consistent environment detection and URL handling
 * across the frontend application for both client and server-side code.
 */

/**
 * Detect the current environment
 * @returns {string} 'development' | 'production'
 */
export function getEnvironment() {
  // Check NODE_ENV first
  if (process.env.NODE_ENV === 'production') {
    return 'production';
  }
  
  // In browser, check the hostname
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    
    // Production domains
    if (hostname === 'tradereply.com' || hostname === 'www.tradereply.com' || hostname === 'dev.tradereply.com') {
      return 'production';
    }
    
    // Development domains
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('.local')) {
      return 'development';
    }
  }
  
  // Default to development for safety
  return 'development';
}

/**
 * Get the current origin/base URL
 * @returns {string} The current origin URL
 */
export function getCurrentOrigin() {
  // In browser environment
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  
  // In server-side environment, determine based on environment
  const env = getEnvironment();
  
  if (env === 'production') {
    // Check if we're on dev subdomain or main domain
    if (process.env.NEXT_PUBLIC_API_BASE_URL?.includes('dev.tradereply.com')) {
      return 'https://dev.tradereply.com';
    }
    return 'https://tradereply.com';
  }
  
  // Development fallback
  return 'http://localhost:3000';
}

/**
 * Get the appropriate API base URL
 * @returns {string} The API base URL
 */
export function getApiBaseUrl() {
  // Use environment variable if available
  if (process.env.NEXT_PUBLIC_API_BASE_URL) {
    return process.env.NEXT_PUBLIC_API_BASE_URL;
  }
  
  // Fallback based on environment
  const env = getEnvironment();
  
  if (env === 'production') {
    return 'https://dev.tradereply.com';
  }
  
  return 'http://127.0.0.1:8000';
}

/**
 * Check if running in development mode
 * @returns {boolean} True if in development
 */
export function isDevelopment() {
  return getEnvironment() === 'development';
}

/**
 * Check if running in production mode
 * @returns {boolean} True if in production
 */
export function isProduction() {
  return getEnvironment() === 'production';
}

/**
 * Get environment-appropriate base URL for URL parsing
 * This is specifically for URL constructor usage
 * @returns {string} Base URL for URL constructor
 */
export function getBaseUrlForParsing() {
  return getCurrentOrigin();
}

/**
 * Environment configuration object
 */
export const ENV_CONFIG = {
  development: {
    domains: ['localhost', '127.0.0.1', '::1', '.local', '.dev', '.test'],
    defaultOrigin: 'http://localhost:3000',
    defaultApiBase: 'http://127.0.0.1:8000',
    secure: false
  },
  production: {
    domains: ['tradereply.com', 'dev.tradereply.com', 'www.tradereply.com'],
    defaultOrigin: 'https://dev.tradereply.com',
    defaultApiBase: 'https://dev.tradereply.com',
    secure: true
  }
};

/**
 * Get configuration for current environment
 * @returns {object} Environment configuration
 */
export function getEnvConfig() {
  const env = getEnvironment();
  return ENV_CONFIG[env];
}
