import axios from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

const axiosInstance = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/`,
  withCredentials: true,
  headers: {
    "Accept": "application/json"
  },
});

// Flag to prevent multiple simultaneous logout redirects
let isRedirecting = false;

axiosInstance.interceptors.request.use(
  (config) => {
    const token = Cookies.get("authToken"); // Fetch latest token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response Interceptor: Handle Unauthorized Errors
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status } = error.response;

      if (status === 401) {
        handleUnauthorizedAccess();
      } else if (status === 404) {
        console.error('Resource not found!');
      } else if (status >= 500) {
        console.error('Server error! Please try again later.');
      }
    } else {
      console.error('Network error or request timeout.');
    }

    return Promise.reject(error);
  }
);

/**
 * Handle unauthorized access with proper cleanup and redirect
 */
function handleUnauthorizedAccess() {
  // Prevent multiple simultaneous redirects
  if (isRedirecting) {
    return;
  }

  isRedirecting = true;

  console.log('Unauthorized access detected. Logging out...');

  // Clear all authentication data
  Cookies.remove("authToken");
  localStorage.removeItem("user");
  localStorage.removeItem("lastActivity");
  localStorage.setItem("loggedOut", Date.now());
  sessionStorage.clear();

  // Show user notification
  // toast.error('Your session has expired. Please log in again.', {
  //   duration: 4000,
  //   position: 'top-center',
  // });

  // Use window.location for redirect (most reliable method)
  if (typeof window !== 'undefined') {
    // Check if we're already on login page to prevent redirect loop
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  }

  // Reset redirect flag after a delay
  setTimeout(() => {
    isRedirecting = false;
  }, 1000);
}

export default axiosInstance;
