'use client';

import "@/css/error/errorPage.scss"
import CommonSearch from "@/Components/UI/CommonSearch";
import Header from "@/Components/UI/Header";
import Footer from "@/Components/UI/Footer";
import { useCallback, useEffect, useState } from "react";
import { get } from "@/utils/apiUtils";
import { debounce } from "lodash";
import Link from "next/link";
import MetaHead from "@/Seo/Meta/MetaHead";

export default function NotFound() {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchedArticles, setSearchedArticles] = useState([]);

  useEffect(() => {
    if (!searchKeyword) return;
    const fetchHome = async () => {
      try {
        const response = await get("/search/home", { key: searchKeyword });
        setSearchedArticles(response.data);
      } catch (error) {
        console.error("Failed to fetch Education Article:", error);
      }
    };
    fetchHome();
  }, [searchKeyword]);

  // useEffect(() => {
  //     const robotsMeta = document.querySelector('meta[name="robots"]');
  //     if (robotsMeta) {
  //       robotsMeta.parentNode.removeChild(robotsMeta);
  //     }
  //     const customMeta = document.createElement('meta');
  //     customMeta.name = 'robots';
  //     customMeta.content = 'index, follow';
  //     document.head.appendChild(customMeta);
  //   }, []);


  const debouncedSearch = useCallback(
    debounce((searchTerm) => {
      setSearchKeyword(searchTerm);
    }, 300),
    []
  );

  const handleHomeSearch = (event) => {
    debouncedSearch(event.target.value);
  };

 useEffect(() => {
  const unwantedRobotsMeta = document.querySelector('meta[name="robots"][content="noindex"]');
  if (unwantedRobotsMeta) {
    unwantedRobotsMeta.remove();
  }
}, []);


  const metaArray = {
    title: "Page Not Found | TradeReply",
    robots: "noindex, follow", // Works now
    description: "Oops! The page you’re looking for isn’t available. Return to TradeReply.com to explore trading tools, strategies, and more.",
    canonical_link: "https://www.tradereply.com/404",
    og_site_name: "TradeReply",
    og_title: "Page Not Found | TradeReply",
    og_description: "Oops! The page you’re looking for isn’t available. Return to TradeReply.com and find the tools and resources you need.",
    twitter_title: "Page Not Found | TradeReply",
    twitter_description: "Oops! The page you’re looking for isn’t available. Return to TradeReply.com and find the tools and resources you need.",
  };

  return (
    <>
      <MetaHead props={metaArray} />
      <Header />
      <div id="error-page" className="errorPage">
        <div className="errorPage_inner">
          <figure>
            <img src='https://cdn.tradereply.com/dev/site-assets/tradereply-404.svg' alt="404" />
          </figure>
          <div className="errorPage_content ">
            <p className="p-md-200">The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.</p>
            <div className="d-flex w-md-100">
              <div className="banner_sec_search w-md-100">
                <CommonSearch
                  label="Crypto & Stock Trading KPIs"
                  placeholder="Explore Key Terms & Concepts"
                  onChange={handleHomeSearch}
                  icon={true}
                  name={"fourOfour"}
                />
                {
                  searchedArticles.length > 0 && searchKeyword ?
                    <div className="position-relative top-1">
                      <div id="comboList" className="list-group position-absolute w-100">
                        <div className="d-flex justify-content-center">
                          <div className="w-400px">
                            {searchedArticles.map((item, index) => (
                              <Link key={index}
                                href={item.type == "education" ? `/education/${item?.slug}` : `/blog/${item?.slug}`}
                                className="list-group-item list-group-item-action text-start d-flex justify-content-between align-items-center">
                                <span>
                                  {item.title}
                                </span>
                                <span className="search-highlight text-uppercase">
                                  {item.type}
                                </span>
                              </Link>
                            ))}

                            <Link href={`/search?search=${searchKeyword}`} className="list-group-item list-group-item-action text-primary  text-center" id="viewAll">View All</Link>
                          </div>
                        </div>
                      </div>
                    </div>
                    :
                    <></>
                }
              </div>
            </div>
            {/* <div className="errorPage_content_btn mt-4"><Link className="btn-style" to="/">Back to homepage</Link></div> */}
            {/* <i>{error.statusText || error.message}</i> */}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}
