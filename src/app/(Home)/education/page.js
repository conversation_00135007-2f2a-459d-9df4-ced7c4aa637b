import EducationClient from "./EducationClient";
import MetaHead from "@/Seo/Meta/MetaHead";
import { get } from "@/utils/apiUtils";
import { cookies } from 'next/headers';


export default async function EducationPage({ params, searchParams }) {
  // const page = searchParams?.page || 1;
  const page = parseInt(params.id) || 1;
  // const key = searchParams?.key || '';
  const cookieStore = cookies();
  const key = cookieStore.get("educationSearchKey")?.value || "";

  const canonicalLink = page === 1
  ? `https://www.tradereply.com/education`
  : `https://www.tradereply.com/education/page/${page}`;

  let educationArticles = [];
  let educationPagination = {};

  let nextLink = null;

  try {
    const response = await get("/article", {
      key,
      type: "education",
      page,
      per_page: 25,
    });

    educationArticles = response.data.education;
    educationPagination = response.data.meta;

    if (educationPagination?.current_page < educationPagination?.total) {
      nextLink = `https://www.tradereply.com/education/page/${educationPagination.current_page + 1}`;
    }
  
    console.log("educationPagination!!!!!!", educationPagination);


  } catch (error) {
    console.error("Failed to fetch education articles:", error);
  }
   // SEO Meta handling
   const isSearch = key?.trim() !== "";

   const metaArray = {
    title: "TradeReply Education Center | Learn Trading Strategies",
    description: "Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.",
    og_title: "TradeReply Education Center | Learn Trading Strategies",
    og_description: "Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.",
    og_site_name: "TradeReply",
    twitter_title: "TradeReply Education Center | Learn Trading Strategies",
    twitter_description: "Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.",
      noindex: isSearch,
      ...(isSearch ? {} : {
        canonical_link: canonicalLink,
      }),
      rel_next: nextLink,
  };

  return (
    <>
    <EducationClient
      initialArticles={educationArticles}
      initialPagination={educationPagination}
      initialSearch={key}
      initialPage={page}
      metaArray={metaArray} 
    />
  </>
  );
}
