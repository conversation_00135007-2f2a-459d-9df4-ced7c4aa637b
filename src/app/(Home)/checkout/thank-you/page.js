"use client";
import React, { useState } from 'react'
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from '@/Seo/Meta/MetaHead';
import { Container } from "react-bootstrap";
import { Row, Col } from "react-bootstrap";
import "@/css/Home/Checkout.scss";
import CommonButton from '@/Components/UI/CommonButton';
import NavLink from '@/Components/UI/NavLink';
import { TopRightArrowIcon, MoneyWithWings, FlatBlueBook } from "@/assets/svgIcons/SvgIcon";
import Link from 'next/link';

export default function page() {
    const [isSubscription, setIsSubscription] = useState(false);

    const buttonTitle = isSubscription ? 'Manage Subscription' : 'Go to My Purchases';
    const buttonHref = isSubscription ? '/account/subscriptions' : '/account/purchased-products';

    const toggleSubscription = () => {
        setIsSubscription(prev => !prev);
    };

    const metaArray = {
        noindex: true,
        title: "Order Confirmed | Thank You for Your Purchase | TradeReply",
        description: "Thank you for your purchase! Your order has been confirmed. Explore more tools and services at TradeReply to optimize your trading strategy.",
        canonical_link: "https://www.tradereply.com/checkout/thank-you",
        og_site_name: "TradeReply",
        og_title: "Order Confirmed | Thank You for Your Purchase | TradeReply",
        og_description: "Thank you for your purchase! Your order has been confirmed. Explore more tools and services at TradeReply to optimize your trading strategy.",
        twitter_title: "Order Confirmed | Thank You for Your Purchase | TradeReply",
        twitter_description: "Thank you for your purchase! Your order has been confirmed. Explore more tools and services at TradeReply to optimize your trading strategy.",
    };

    return (
        <>
            <HomeLayout>
                <MetaHead props={metaArray} />
                <div className="checkout">
                    <Container>
                        <div className='checkoutContainer'>
                            <button className='btn-style' onClick={toggleSubscription} style={{ margin: '10px 0' }}>
                                {isSubscription ? 'Subscribing' : 'Purchase'}
                            </button>
                            <p className='checkoutContainer_title text-center'>
                                {isSubscription ? 'Thank You for Subscribing!' : 'Thank You for Your Purchase!'}
                            </p>
                            <p className='checkoutContainer_subtitle text-center'>
                                {isSubscription ? 'Your subscription is active!' : 'Your order is confirmed! You now have access to your digital products.'}

                            </p>
                            <p className='checkoutContainer_subtitle text-center'>
                                {isSubscription ? 'Premium tools are now unlocked—check your email and get started.' : 'Check your email for confirmation, and start exploring right away!'}
                            </p>
                            <div className='AccessOrder'>
                                <div className='border-gray col'></div>
                                <p>
                                    {isSubscription ? 'Start Using Your Subscription' : 'Access Your Order'}
                                </p>
                                <div className='border-gray col'></div>
                            </div>
                            <Link href={buttonHref} className="w-100" target="_blank">
                                <CommonButton title={buttonTitle} fluid />
                            </Link>
                            <div className='earn'>
                                <p>Looking for more ways to earn? Check out our </p>
                                <Link href="/partner" target="_blank" className='d-flex gap-2 align-items-center'>
                                    Affiliate Program
                                    <TopRightArrowIcon />
                                </Link>
                            </div>
                            <Row>
                                <Col sm={12} lg={6}>
                                    <div className='checkoutContainer_earnReward'>
                                        <div className='d-flex gap-2 align-items-center'>
                                            <MoneyWithWings />
                                            <p className='heading'>Earn Rewards for Sharing TradeReply!</p>
                                        </div>
                                        <p className='subHeading'>Love TradeReply? Invite your friends and you’ll both get $15 towards any subscription when they sign up and subscribe!</p>
                                        <button className="green_btn btn-style w-100">
                                            Invite a Friend & Earn $15
                                        </button>
                                        <p>Referral rewards apply only to new subscribers. </p>
                                        <NavLink href="/terms" target="_blank">
                                            <span>Terms & conditions apply </span>
                                        </NavLink>
                                    </div>
                                </Col>
                                <Col sm={12} lg={6} className='mt-3 mt-lg-0'>
                                    <div className='checkoutContainer_trading'>
                                        <div className='d-flex gap-2 align-items-center'>
                                            <FlatBlueBook />
                                            <p className='heading'>Level Up Your Trading Knowledge</p>
                                        </div>
                                        <p className='subHeading'>Explore essential insights and strategies in our Education Center.</p>
                                        <div className='button-over-image'>
                                            <img src="https://cdn.tradereply.com/dev/site-assets/tradereply-learn-trading-strategies.png" alt="Tradereply Cart Image" />
                                            <div className='exploreBtn'>
                                                <NavLink href="/education" target="_blank" className='w-100'>
                                                    <button className="white_btn btn-style w-100">
                                                        Explore Education
                                                    </button>
                                                </NavLink>
                                            </div>
                                        </div>
                                    </div>
                                </Col>
                            </Row>
                        </div>
                    </Container>
                </div >
            </HomeLayout >
        </>
    )
}
