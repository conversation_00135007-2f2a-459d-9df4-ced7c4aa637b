import { Col, Container, Row } from "react-bootstrap";
import CommonHeading from "@/Components/UI/CommonHeading";
import Link from "next/link";
import "../../../css/Home/Sitemap.scss";
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from "@/Seo/Meta/MetaHead";

const sitemapbox = [
  {
    icon: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-rocket.svg",
    title: "Main Pages",
    content: [
      {
        text: "Homepage",
        to: "/",
      },
      {
        text: "Blog Sitemap",
        to: "/blog-sitemap",
      },
      {
        text: "Category",
        to: "/category",
      },
      {
        text: "Education Center",
        to: "/education",
      },
      {
        text: "Trading Calculator",
        to: "/trading-calculator",
      },
      {
        text: "Marketplace",
        to: "/marketplace",
      },
      {
        text: "Features",
        to: "/features",
      },
      {
        text: "Pricing",
        to: "/pricing",
      },
    ],
  },
  {
    icon: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-account.svg",
    title: "User Account",
    content: [
      {
        text: "Log In / Sign Up",
        to: "/login",
      },
      {
        text: "Help Center",
        to: "/help",
      },
      {
        text: "Feedback & Bugs",
        to: "/help/feedback",
      },
      {
        text: "Services Status",
        to: "/status",
      },
      {
        text: "Refer A Friend",
        to: "/refer-a-friend",
      },
      {
        text: "Partner Program",
        to: "/partner",
      },
      {
        text: "Advertising",
        to: "/advertising",
      },
    ],
  },
  {
    icon: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-notesetting.svg",
    title: "Extra Resources",
    content: [
      {
        text: "Brand Assets",
        to: "/brand-assets",
      },
      {
        text: "Accessibility",
        to: "/accessibility",
      },
      {
        text: "Privacy Policy",
        to: "/privacy",
      },
      {
        text: "Cookies Policy",
        to: "/cookies",
      },
      {
        text: "Terms & Conditions",
        to: "/terms",
      },
      {
        text: "Disclaimer",
        to: "/disclaimer",
      },
      {
        text: "Brokers",
        to: "/brokers",
      },
    ],
  },
];
const Sitemap = () => {
  const metaArray = {
    title: "TradeReply Sitemap | Explore All Pages",
    description: "Navigate TradeReply.com with ease. Access the full sitemap to explore all pages, including trading tools, strategies, market insights, and educational resources.",
    canonical_link: "https://www.tradereply.com/sitemap",
    og_site_name: "TradeReply",
    og_title: "TradeReply Sitemap | Explore All Pages",
    og_description: "Navigate TradeReply.com easily with our comprehensive sitemap. Find all the pages and resources you need in one place.",
    twitter_title: "TradeReply Sitemap | Explore All Pages",
    twitter_description: "Navigate TradeReply.com easily with our comprehensive sitemap. Find all the pages and resources you need in one place.",
  };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <div className="py-100 sitemap">
        <Container>
          <div className="sitemap_inner">
            <div className="sitemap_heading text-center">
              <CommonHeading title="TradeReply Sitemap" centered />
            </div>
            <div className="sitemap_content mt-4 mt-md-5">
              <Row>
                {sitemapbox?.map((item, index) => (
                  <Col key={index} sm={4} xs={12}>
                    <div className="sitemap_content_box">
                      <div className="sitemap_content_box_icon">
                        <img src={item?.icon} alt={item?.title ? `${item.title} section - TradeReply sitemap` : "TradeReply sitemap icon"} />
                      </div>
                      <h4>{item?.title}</h4>
                      <ul>
                        {item?.content?.map((item, index) => (
                          <li key={index}>
                            <Link href={item?.to}>{item.text}</Link>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          </div>
        </Container>
      </div>
    </HomeLayout>
  );
};

export default Sitemap;
