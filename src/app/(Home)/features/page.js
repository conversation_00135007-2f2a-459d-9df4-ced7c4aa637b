"use client";

import { Col, Container, Row } from "react-bootstrap";
import AnswerTrades from "@/Components/common/Home/AnswerTrades";
import Testimonials from "@/Components/common/Home/Testimonials";
import CommonButton from "@/Components/UI/CommonButton";
import { CheckIcon } from "@/assets/svgIcons/SvgIcon";
import { useRouter } from "next/navigation";
import HomeLayout from "@/Layouts/HomeLayout";
import "@/css/Home/Features.scss";
import Typewriter from "@/Components/common/TypeWriter";
import Link from "next/link";
import MetaHead from "@/Seo/Meta/MetaHead";

const images = {
  stock: "https://cdn.tradereply.com/dev/site-assets/tradereply-stock-analysis.png",
  log: "https://cdn.tradereply.com/dev/site-assets/tradereply-log-trades.png",
  why: "https://cdn.tradereply.com/dev/site-assets/tradereply-70-percent-rule.png",
  build: "https://cdn.tradereply.com/dev/site-assets/tradereply-trading-strategies.png",
  dashboard: "https://cdn.tradereply.com/dev/site-assets/tradereply-portfolio-analysis.png"
};

const listItems = [
  [
    "On-the-Go Learning Support",
    "Multiple Trade Accounts",
    "Dynamic Dashboards",
    "Strategy Building",
    "Chart & KPI Widgets",
    "Advanced Filters",
    "Analytics Presets"
  ],
  [
    "Autosync Brokers",
    "CSV Trade Import",
    "Widget Customization",
    "Marketplace Access",
    "Upload Chart Images",
    "Heatmaps",
    "Trade Tagging"
  ],
  [
    "Advanced Dimensions",
    "Trade Calculators",
    "Intuitive Trade Building",
    "Accurate Dates & Times",
    "Fast Web Servers",
    "Social Networking",
    "Drill Down Metrics"
  ]
];
const heads = [
  'Stock',
  'Stock',
  'Stock',
  'Crypto',
  'Crypto',
  'Crypto',
]
const bodies = [
  'sis',
  'zer',
  'zed',
]
const titles = [
  {
    head: 'Stock',
    body: 'sis'
  },
  {
    head: 'Stock',
    body: 'zer'
  },
  {
    head: 'Stock',
    body: 'zed'
  },
  {
    head: 'Crypto',
    body: 'sis'
  },
  {
    head: 'Crypto',
    body: 'zer'
  },
  {
    head: 'Crypto',
    body: 'zed'
  },
];

const Section = ({ title, description, imgSrc }) => (
  <section className="text-center py-100">
    <div className="features_heading">
      <h2>{title}</h2>
      {description && <div className="mt-4">{description}</div>}
    </div>
    <div className="mt-5 pt-xl-4">
      <img src={imgSrc} alt={title} />
    </div>
  </section>
);

const Features = () => {
  const router = useRouter();

  const metaArray = {
    title: "TradeReply Features | Stock & Crypto Trading Tools",
    description: "Explore TradeReply.com's advanced features: stock trade analysis, flexible dashboards, dynamic visuals, comprehensive metrics, and powerful strategy building.",
    canonical_link: "https://www.tradereply.com/features",
    og_site_name: "TradeReply",
    og_title: "TradeReply Features | Stock & Crypto Trading Tools",
    og_description: "Explore TradeReply.com's advanced features: stock trade analysis, flexible dashboards, dynamic visuals, comprehensive metrics, and powerful strategy building.",
    twitter_title: "TradeReply Features | Stock & Crypto Trading Tools",
    twitter_description: "Explore TradeReply.com's advanced features: stock trade analysis, flexible dashboards, dynamic visuals, comprehensive metrics, and powerful strategy building.",
  };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <div className="features">
        <Container>
          <div className="features_inner">
            <section className="text-center features_stock">
              <Typewriter words={titles} period={2000} />
              <div className="mt-5 pt-xl-4">
                <img src={images.stock} alt="Stock and Crypto Trading Analysis" />
              </div>
            </section>

            <section className="features_yourTrade py-100">
              <AnswerTrades />
            </section>

            <Section
              title="Log & Sync Trades"
              description={<h4>Effortlessly log and sync all your trades ensuring accurate records and up-to-date performance metrics across all your accounts.</h4>}
              imgSrc={images.log}
            />

            <Section
              title="Why Use TradeReply?"
              description={
                <>
                  <h4>70% of traders who use data analytics are <span className="green_text font-semibold">successful</span></h4>
                  <h4>70% of traders who do NOT use data analytics <span className="red_text font-semibold">fail</span></h4>
                </>
              }
              imgSrc={images.why}
            />

            <section className="features_testimonial">
              <Testimonials />
              <div className="mt-0 text-center">
                <Link href="/pricing">
                  <CommonButton title="Optimize Trading" className="px-xl-5" />
                </Link>
              </div>
            </section>

            <Section
              title="Build Trading Strategies"
              description={<h4>Create and optimize trading strategies using advanced tools and historical data for effective, data-driven plans tailored to your goals.</h4>}
              imgSrc={images.build}
            />

            <Section
              title="Personalized Dashboards"
              description={<h4>Add KPI widgets, and filter by metrics, strategies, trades, tickers, accounts, and more for customized trading dashboards.</h4>}
              imgSrc={images.dashboard}
            />

            <section className="features_transformsec">
              <Row>
                {listItems.map((features, index) => (
                  <Col md={4} xs={12} key={index}>
                    <ul>
                      {features.map((feature) => (
                        <li key={feature} className="d-flex">
                          <CheckIcon height="28" width="28" /> {feature}
                        </li>
                      ))}
                    </ul>
                  </Col>
                ))}
              </Row>
              <div className="features_heading my-5 pt-xl-5 text-center">
                <h2>Ready to Transform Your Trading?</h2>
                <h4 className="mt-4">
                  Harness the power of advanced analytics and personalized dashboards to revolutionize your trading experience.
                </h4>
                <div className="mt-4 mt-md-5">
                  <Link href="/signup">
                    <CommonButton
                      onClick={() => router.push("/signup")}
                      title="Join Free, Upgrade Anytime"
                      className="px-xl-5 green-btn"
                    />
                  </Link>
                </div>
              </div>
            </section>
          </div>
        </Container>
      </div>
    </HomeLayout>
  );
};

export default Features;
