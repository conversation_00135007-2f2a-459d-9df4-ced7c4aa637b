"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { Col, Container, Row } from "react-bootstrap";
import { useRouter } from "next/navigation";
import { debounce } from "lodash";
import Link from "next/link";
import CommonSearch from "@/Components/UI/CommonSearch";
import CustomPagination from "@/Components/UI/CustomPagination";
import RecentPost from "@/Components/common/Home/RecentPost";
import { RightArrowIcon, CrossIcon } from "@/assets/svgIcons/SvgIcon";
import Loader from "@/Components/common/Loader";
import "@/css/Home/Category.scss";
import { useParams } from "next/navigation";
import { get } from "@/utils/apiUtils";
import { getCookie, setCookie, deleteCookie } from "cookies-next";
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from "@/Seo/Meta/MetaHead";


export default function CategoryContent({
    initialListingAllCategories,
    initialAllCategoryArticles,
    initialCategoryMeta,
    initialSelectedCategory,
    currentPage,
    keyWord,
    metaArray: initialMetaArray,
}) {
    const router = useRouter();
    const params = useParams();
    const slug = params.detail;

    const sliderRef = useRef(null);
    const [disableLeft, setDisableLeft] = useState(true);
    const [disableRight, setDisableRight] = useState(false);
    const [listingAllCategories, setListingAllCategories] = useState(initialListingAllCategories);
    const [allCategoryArticles, setAllCategoryArticles] = useState(initialAllCategoryArticles);
    const [selectedFilter, setSelectedFilter] = useState(initialSelectedCategory?.title);
    const [searchInputValue, setSearchInputValue] = useState(keyWord);
    const [isMobile, setIsMobile] = useState(false);
    const [categoryMeta, setCategoryMeta] = useState(initialCategoryMeta);
    const [selectedCategory, setselectedCategory] = useState(initialSelectedCategory);
    const [isLoading, setisLoading] = useState(false);
    const [newPage, setNewPage] = useState(currentPage);
    const [searchKeyword, setSearchKeyword] = useState(keyWord);
    const [metaArray, setMetaArray] = useState(initialMetaArray || {});
    const hasSearch = searchKeyword.trim() !== "";
    const page = parseInt(params?.id) || 1;
    console.log("searchKeywordssssssss",);
    
    useEffect(() => {
        const searchKey = getCookie("categorySearchKey");
        const categoryId = getCookie("category-id");
    
        const effectiveSearchKey = searchKey || categoryId || "";
    
        if (effectiveSearchKey) {
            setSearchKeyword(effectiveSearchKey);
            setSearchInputValue(effectiveSearchKey);
            setCookie("category-id", effectiveSearchKey, { path: "/", sameSite: "lax" }); // Always sync category-id
            deleteCookie("categorySearchKey", { path: "/" }); // Clear temp cookie after use
        }
    }, [slug]);
    
    
    useEffect(() => {
        if (typeof window !== "undefined") {
            const checkScreenSize = () => {
                setIsMobile(window.innerWidth <= 768);
            };

            checkScreenSize();
            window.addEventListener("resize", checkScreenSize);

            return () => {
                window.removeEventListener("resize", checkScreenSize);
            };
        }
    }, []);


    const smoothScroll = (amount) => {
        if (sliderRef.current) {
            const start = sliderRef.current.scrollLeft;
            const end = start + amount;
            const duration = 300; // Duration in ms
            const startTime = performance.now();

            const step = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = 1;
                const scrollAmount = start + (end - start) * progress;

                sliderRef.current.scrollLeft = scrollAmount;

                if (progress < 1) {
                    requestAnimationFrame(step);
                }
            };
            requestAnimationFrame(step);
        }
    };
    const scrollLeft = () => {
        if (isMobile) {
            smoothScroll(-100); // Scroll left by 100px
        }
        else {
            smoothScroll(-200);
        }

    };

    const scrollRight = () => {
        if (isMobile) {
            smoothScroll(100); // Scroll left by 100px
        }
        else {
            smoothScroll(200);
        }

    };
    const checkScrollPosition = () => {
        if (sliderRef.current) {
            const { scrollLeft, scrollWidth, clientWidth } = sliderRef.current;
            setDisableLeft(scrollLeft === 0);
            setDisableRight(scrollLeft + clientWidth >= scrollWidth);
        }
    };

    useEffect(() => {
        if (sliderRef.current) {
            sliderRef.current.addEventListener("scroll", checkScrollPosition);
        }
        return () => {
            if (sliderRef.current) {
                sliderRef.current.removeEventListener("scroll", checkScrollPosition);
            }
        };
    }, []);



    const handleCategoryClick = (slug) => {
        console.log('slug', slug);
    }
    const resetSlecetedFilter = () => {
        setisLoading(true);
        router.push('/category');
    }
    const updateURL = (page) => {
        const newQuery = `page/${page}`;
        if (page == 1) {
            router.replace(`/category`);
        }
        else {
            router.replace(`/category/${newQuery}`);
        }
    };

    useEffect(() => {
        const getNewCategories = async () => {

            //   setisLoading(true);
            try {
                const response = await get(`/category`, {
                    page: newPage,
                    slug: selectedCategory?.slug,
                    key: searchKeyword,
                });
                setListingAllCategories(response?.data?.allcategories);
                setAllCategoryArticles(response?.data?.articles);
                setCategoryMeta(response?.data?.meta);
                setselectedCategory(response?.data?.selected_category);
                setSelectedFilter(response?.data?.selected_category?.title || 'All');
                setSearchInputValue(searchKeyword);

                const totalPages = response?.data?.meta?.total || 1;
                setCookie("total-pages", totalPages, { path: "/", sameSite: "lax" });
                const isSearch = searchKeyword.trim() !== "";

                const canonicalLink =
                    newPage === 1
                        ? `https://www.tradereply.com/category${slug ? `/${slug}` : ""}`
                        : `https://www.tradereply.com/category${slug ? `/${slug}` : ""}/page/${newPage}`;

                const relNextLink =
                    newPage < totalPages
                        ? `https://www.tradereply.com/category${slug ? `/${slug}` : ""}/page/${newPage + 1}`
                        : null;

                setMetaArray({
                    title:  `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
                    description: description,
                    og_title:  `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
                    og_description: description,
                    og_site_name: "TradeReply",
                    twitter_title:  `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
                    twitter_description: description,
                    noindex: isSearch,
                    canonical_link: canonicalLink,
                    rel_next: relNextLink || "", // 🔧 Explicitly set empty if not present
                });



            } catch (error) {
                console.log("CAtegory Error ", error);
            } finally {
                setisLoading(false);
            }
        };

        getNewCategories();
    }, [searchKeyword, slug]);

    const handleDataFromChild = (page) => {
        if (page === newPage) return; // Don't do anything if already on same page

        setCookie("category-id", searchKeyword, { path: "/", sameSite: "lax" });
        setNewPage(page);
        setisLoading(true);

        if (page === 1) {
            router.replace(`/category/${slug}`);
        } else {
            router.replace(`/category/${slug}/page/${page}`);
        }
    };





    const debouncedSearch = useCallback(
        debounce((searchTerm) => {
            setSearchKeyword(searchTerm);
            setNewPage(1);
            setCookie("category-id", searchTerm, { path: "/", sameSite: "lax" });
        }, 400),
        [slug]
    );

    const handleHomeSearch = (event) => {
        const input = event.target.value;
        setSearchInputValue(input);
        debouncedSearch(input);
    };
    const handleClearSearch = async () => {
        deleteCookie("category-id", { path: "/" });
        const isAlreadyOnCategory = window.location.pathname === `/category/${slug}`;
        if (isAlreadyOnCategory) {
            setSearchKeyword("");
            setNewPage(1);
        } else {
            await router.push(`/category/${slug}`);
            // Set newPage here if needed
        }
    };

    return (
        <HomeLayout>
            <MetaHead props={metaArray} />
            <section className="categorySec py-100">
                <Container>
                    <div className="categorySec_heading text-center">
                        <h1>TradeReply Categories</h1>
                        <p>Browse categories to find relevant articles and insights.</p>

                        <div className="categorySec_search">
                            <CommonSearch
                                placeholder="Search for terms"
                                icon={true}
                                onChange={handleHomeSearch}
                                name={"categoryDetail"}
                                value={searchInputValue}
                                onClear={handleClearSearch}

                            />
                        </div>
                    </div>
                    <div className="categorySec_fliters">
                        <div className="categorySec_fliters_inner">
                            <button
                                className={`scroll-btn left ${disableLeft ? "disabled" : ""}`}
                                disabled={disableLeft}
                                onClick={scrollLeft}
                            >
                                <RightArrowIcon />
                            </button>
                            <div className="slider" ref={sliderRef}>
                                {/* "All" category button */}
                                <Link
                                    href={"/category"}
                                    className={`categorySec_fliters_boxbutton text-nowrap ${!selectedCategory ? 'active' : ''}`}
                                >
                                    All
                                </Link>

                                {listingAllCategories?.map((item) => (
                                    <div
                                        key={item?.id}
                                        className={`categorySec_fliters_boxbutton text-nowrap ${item.slug === selectedCategory?.slug ? 'active disabled' : ''}`}
                                    >
                                        {item.slug === selectedCategory?.slug ? (
                                            // If the category is selected, render non-clickable text instead of a link
                                            <span>{item?.title}</span>
                                        ) : (
                                            // If it's not selected, allow it to be clickable
                                            <Link
                                                href={`/category/${item.slug}`}
                                                onClick={() => {
                                                    setisLoading(true);
                                                    if (searchInputValue.trim()) {
                                                      setCookie("category-id", searchInputValue.trim(), { path: "/", sameSite: "lax" });
                                                    } else {
                                                      deleteCookie("category-id", { path: "/" });
                                                    }
                                                  }}
                                            >

                                                {item?.title}
                                            </Link>
                                        )}
                                    </div>
                                ))}
                            </div>


                            <button
                                className={`scroll-btn right ${disableRight ? "disabled" : ""}`}
                                disabled={disableRight}
                                onClick={scrollRight}
                            >
                                <RightArrowIcon />
                            </button>
                        </div>


                        <div className="flex gap-3 flex-wrap">
                            {/* Filter Tag */}
                            {selectedFilter && (
                                <div className="categorySec_fliters_boxadd">
                                    <h6 className="d-flex align-items-center flex-wrap gap-2">
                                        <span className="d-flex align-items-center gap-2">
                                            <span>Filter:</span>
                                            <span>{selectedFilter}</span>
                                            {selectedFilter !== "All" && (
                                                <span
                                                    className="ml-2 pe-auto cursor-pointer"
                                                    onClick={() => {
                                                        setNewPage(1);
                                                        setisLoading(true);
                                                        if (searchInputValue.trim()) {
                                                        setCookie("category-id", searchInputValue.trim(), { path: "/", sameSite: "lax" });
                                                        } else {
                                                        deleteCookie("category-id", { path: "/" });
                                                        }
                                                        router.push("/category");

                                                    }}
                                                >
                                                    <CrossIcon />
                                                </span>
                                            )}
                                        </span>
                                    </h6>
                                </div>
                            )}

                            {/* Search Tag */}
                            {searchInputValue && (
                                <div className="categorySec_fliters_boxadd">
                                    <h6 className="d-flex align-items-center flex-wrap gap-2">
                                        <span className="d-flex align-items-center gap-2">
                                            <span>Search:</span>
                                            <span>{searchInputValue}</span>
                                            <span
                                                className="ml-2 pe-auto cursor-pointer"
                                                onClick={handleClearSearch}
                                            >
                                                <CrossIcon />
                                            </span>
                                        </span>
                                    </h6>
                                </div>
                            )}
                        </div>

                    </div>
                    {isLoading ?
                        <Loader />
                        :
                        <div className="categorySec_term">
                            {selectedCategory ?
                                <div className="categorySec_term_content">
                                    <h4> Category – {selectedCategory?.title}</h4>
                                    <p className="mb-5 mt-3">{selectedCategory?.content}</p>
                                </div>
                                :
                                <div className="categorySec_term_content">
                                    <h4> All Categories</h4>
                                    <p className="mb-5 mt-3">No category selected</p>
                                </div>

                            }

                            <div className="d-flex justify-content-end w-100 mb-4">
                                <CustomPagination
                                    links={categoryMeta}
                                    onDataSend={handleDataFromChild}
                                    pageUrl={`category/${slug}`}
                                    // metaArray={metaArray}
                                    flag={!!searchKeyword}
                                    useDynamicParams={true}
                                />
                            </div>
                            <div className="blog_recentPost">
                                {allCategoryArticles?.map((item, index) => (
                                    <div key={index}>
                                        <Row>
                                            <Col xs={12} className="d-flex">
                                                <div>
                                                    {item.name}
                                                </div>
                                                <RecentPost
                                                    img={item.feature_image_url}
                                                    title={item?.title}
                                                    text={item?.summary}
                                                    // coinname={item.coinname}
                                                    // onClick={() => navigate(":id")}
                                                    href={item.type == "education" ? `/education/${item?.slug}` : `/blog/${item?.slug}`}
                                                />
                                            </Col>
                                        </Row>
                                    </div>
                                ))}
                                <div className="blog_pagination justify-content-center justify-content-md-end d-flex mt-4">
                                    {/* <CustomPagination /> */}
                                </div>
                            </div>
                            <div className="d-flex justify-content-end w-100">
                                <CustomPagination
                                    links={categoryMeta}
                                    onDataSend={handleDataFromChild}
                                    pageUrl={`category/${slug}`}
                                    // metaArray={metaArray}
                                    flag={!!searchKeyword}
                                    useDynamicParams={true}
                                />
                            </div>

                        </div>
                    }
                </Container>
            </section>
        </HomeLayout>

    );
}
