'use client';

import { Col, Container, Row } from "react-bootstrap"
import "./SearchPage.scss"
import Link from "next/link";
import HomeLayout from "@/Layouts/HomeLayout";
import CommonButton from "@/Components/UI/CommonButton";
import CommonSearch from "@/Components/UI/CommonSearch";
import CustomPagination from "@/Components/UI/CustomPagination";
import { debounce } from "lodash";
import { get } from "@/utils/apiUtils";
import { useCallback, useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { CrossIcon } from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";

const SearchPage = () => {

    const searchParams = useSearchParams();
    const router = useRouter();
    const search = searchParams.get("search");
    const [key, setKey] = useState(search);
    const [searchKeyword, setSearchKeyword] = useState(search);
    const [searchedArticles, setSearchedArticles] = useState([]);
    const [searchPagination, setsearchPagination] = useState(null);
    const [newPage, setNewPage] = useState(null);


    useEffect(() => {

        // if (!searchKeyword) {
        //     setSearchedArticles([])
        //     return;
        // }

        const fetchHome = async () => {
            try {
                const response = await get("/search/", { page: newPage, perpage: 25, key: searchKeyword });
                setSearchedArticles(response.data.articles.data);
                setsearchPagination(response.data.meta);
            } catch (error) {
                console.error("Failed to fetch Education Article:", error);
            }
        };
        fetchHome();
    }, [searchKeyword, newPage]);

    const debouncedSearch = useCallback(
        debounce((searchTerm) => {
            setSearchKeyword(searchTerm);
            router.push(`/search?search=${encodeURIComponent(searchTerm)}`);
        }, 300),
        []
    );


    const handleHomeSearch = (event) => {
        const value = event.target.value;
        if (value === "") {
            setNewPage(1);
        }
        debouncedSearch(value);
    };


    const handleDataFromChild = (childData) => {
        window.scrollTo(0, 0);
        setNewPage(childData);
        console.log(newPage);
    };

    const [reset, setReset] = useState(false);

    const metaArray = {
        noindex: true,
        title: "Search Results | TradeReply",
        description: "Find what you’re looking for on TradeReply.com. Browse search results to discover trading tools, strategies, market insights, and educational resources.",
        canonical_link: "https://www.tradereply.com/search",
        og_site_name: "TradeReply",
        og_title: "Search Results | TradeReply",
        og_description: "Find what you’re looking for on TradeReply.com. Browse search results for trading tools, strategies, market insights, and educational resources.",
        twitter_title: "Search Results | TradeReply",
        twitter_description: "Find what you’re looking for on TradeReply.com. Browse search results for trading tools, strategies, market insights, and educational resources.",
    };

    return (
        <>
           <MetaHead props={metaArray} />
            <div className="education py-100">
                <Container>
                    <div className="education_heading text-center">
                        <h1>Explore TradeReply</h1>

                        <div className="education_search mt-4">
                            <CommonSearch
                                placeholder="Find What You're Looking For"
                                icon={true}
                                onChange={handleHomeSearch}
                                name="search"
                                reset={reset}
                                value={key}
                            />
                        </div>
                    </div>
                    <div className="education_fliters">
                        <div className="education_fliters_boxadd">
                            <h6>Filter:
                                <span className="ml-3">
                                    {searchKeyword}
                                </span>
                                <span className="ml-2 pe-auto" onClick={() => {
                                    router.push('/search');
                                    setReset(!reset);
                                    setKey('');
                                    setSearchKeyword('');
                                }}>
                                    {searchKeyword ? <CrossIcon /> : <></>}
                                </span>
                            </h6>
                        </div>

                    </div>

                    <div className="education_term">
                        <Row className="mb-3 education_term_head">
                            <Col md={3} lg={2} className="d-none d-md-block">
                                <h4>Type</h4>
                            </Col>
                            <Col md={6} lg={8} className="d-none d-md-block">
                                <h4>Description</h4>
                            </Col>
                            <Col xs={12} md={3} lg={2}>
                                <div className="education_pagination mb-4">
                                    {searchedArticles?.length > 0
                                        ?
                                        <CustomPagination
                                            links={searchPagination}
                                            onDataSend={handleDataFromChild}
                                            pageUrl={"education/page/"}
                                        />
                                        : <></>
                                    }
                                </div>

                            </Col>
                        </Row>
                        <div>
                            {searchedArticles && searchedArticles.length > 0 ?
                                (searchedArticles?.map((item, index) => {
                                    return (
                                        <Row key={index} className="education_term_list align-items-center">
                                            <Col xs={6} md={3} lg={2}>
                                                <h6>{item.title}</h6>
                                            </Col>
                                            <Col xs={6} md={3} lg={2} className="order-md-last">
                                                <div className="text-end">
                                                    <Link
                                                        href={item.type == "education" ? `/education/${item?.slug}` : `/blog/${item?.slug}`} >
                                                        <CommonButton type="button" title="Read More" className="read_more_button" />
                                                    </Link>
                                                </div>
                                            </Col>
                                            <Col xs={12} md={6} lg={8}>
                                                <p>{item.summary}</p>
                                            </Col>
                                        </Row>
                                    )
                                }))
                                :
                                <p className="text-center">no results found. </p>

                            }
                        </div>
                        <div className="education_pagination mb-4">
                            {searchedArticles?.length > 0
                                ?
                                <CustomPagination
                                    links={searchPagination}
                                    onDataSend={handleDataFromChild}
                                    pageUrl={"education/page/"}
                                />
                                : <></>
                            }
                        </div>

                    </div>
                </Container>
            </div>
            </>
    )
}

export default SearchPage