@use "../../../assets/theme/_var.scss" as *;

.blogsiteMap {
    @media screen and (max-width: 767px) {
        padding-bottom: 0 !important;
    }

    &_heading {
        &_link {
            color: $white;
            font-size: 1.25rem;
            display: inline-block;
            margin-top: 1.5rem;
            font-weight: 600;

            @media screen and (max-width: 767px) {
                font-size: 1.25rem;
            }

            &:hover {
                color: $baseclr;

                svg {
                    path {
                        fill: $baseclr;
                    }
                }
            }
        }
    }

    &_content {
        &_box {
            margin-top: 50px;

            @media screen and (max-width: 767px) {
                text-align: center;
            }

            &_icon {
                img {
                    height: 70px;
                    margin-bottom: 1.25rem;
                    display: flex;
                    align-items: flex-end;

                    @media screen and (max-width: 767px) {
                        justify-content: center;
                    }
                }
            }

            h4 {
                color: $textclr;
                font-size: 1.65rem;
                font-weight: 800;
            }

            ul {
                padding-top: 10px;

                li {
                    a {
                        color: $white;
                        display: block;
                        margin-top: 0.625rem;
                        font-size: 1.25rem;
                        font-weight: 500;
                        line-height: 24.5px;
                        letter-spacing: -0.10000000149011612px;

                        @media (max-width: 767px) {
                            font-size: 1rem;
                        }

                        &:hover {
                            color: $baseclr;
                        }
                    }
                }
            }
        }
    }
}