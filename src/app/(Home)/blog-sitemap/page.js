'use client';

import { Col, Container, Row } from "react-bootstrap";
import "./BlogSitemap.scss";
import CommonHeading from "@/Components/UI/CommonHeading";
import Link from "next/link";
import { RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import HomeLayout from "@/Layouts/HomeLayout";
import { useEffect, useState } from "react";
import { get } from "@/utils/apiUtils";
import MetaHead from "@/Seo/Meta/MetaHead";

const BlogSitemap = () => {
    const [blogsData, setBlogsData] = useState([]);

    useEffect(() => {
        const fetchBlogSiteData = async () => {
            try {
                const response = await get(`/blog-sitemap`);
                setBlogsData(response.data?.data);
            } catch (error) {
                console.log("error", error);
            }
        };


        fetchBlogSiteData();
    },[]);




    const metaArray = {
        title: "TradeReply Blog Sitemap | Find Articles by Category",
        description: "Navigate TradeReply.com's blog with ease. Access a complete list of articles organized by category to find insights and strategies that match your interests.",
        canonical_link: "https://www.tradereply.com/blog-sitemap",
        og_site_name: "TradeReply",
        og_title: "TradeReply Blog Sitemap | Find Articles by Category",
        og_description: "Navigate TradeReply.com's blog with ease. Access a complete list of articles organized by category to find insights and strategies that match your interests.",
        twitter_title: "TradeReply Blog Sitemap | Find Articles by Category",
        twitter_description: "Navigate TradeReply.com's blog with ease. Access a complete list of articles organized by category to find insights and strategies that match your interests.",
    };

    return (
        <HomeLayout>
            <MetaHead props={metaArray} />
            <div className="py-100 blogsiteMap">
                <Container>
                    <div className="blogsiteMap_inner">
                        <div className="blogsiteMap_heading text-center">
                            <CommonHeading title="TradeReply Blog Sitemap" centered />
                            <Link className="blogsiteMap_heading_link" href="/blog">
                                Visit the TradeReply Blog Homepage <span className="ms-2"><RightArrowIcon /></span>
                            </Link>
                        </div>
                        <div className="blogsiteMap_content mt-4 mt-md-5">
                            <Row>
                                {blogsData.length > 0 ? blogsData.map((items, index) => (
                                    <Col key={index} sm={4} xs={12}>
                                        <div className="blogsiteMap_content_box">
                                            <div className="blogsiteMap_content_box_icon">
                                                <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-bookmark.svg" alt="icon" />
                                            </div>
                                            <h4>{items?.title}</h4>
                                            <ul>
                                                {items?.blog?.map((item, index) => (
                                                    <li key={index}>
                                                        <Link href={`/blog/${item?.slug}`}>
                                                            {item.title}
                                                        </Link>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    </Col>
                                )) : <p className="text-center">No blogs found.</p>}
                            </Row>
                        </div>
                    </div>
                 
                </Container>

            </div>

        </HomeLayout>
    );
};

export default BlogSitemap;
