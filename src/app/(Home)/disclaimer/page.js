import { Container } from "react-bootstrap";
import HomeLayout from "@/Layouts/HomeLayout";
import "../../../css/Home/PrivacyPolicy.scss";
import MetaHead from "@/Seo/Meta/MetaHead";
import NoInvestmentAdvice from "./Partials/NoInvestmentAdvice";
import DigitalProducts from "./Partials/DigitalProducts";
import LimitationsLiability from "./Partials/LimitationsLiability";
import IntellectualProperty from "./Partials/IntellectualProperty";
import ReportingViolations from "./Partials/ReportingViolations";

const Disclaimer = () => {
  const metaArray = {
    title: "Disclaimer | TradeReply",
    description:
      "Read TradeReply.com's disclaimer for information on the limitations of liability, accuracy, and content use across our platform.",
    canonical_link: "https://www.tradereply.com/disclaimer",
    og_site_name: "TradeReply",
    og_title: "Disclaimer | TradeReply",
    og_description:
      "Read TradeReply's disclaimer to understand the limitations and responsibilities regarding the use of our trading tools and information.",
    twitter_title: "Disclaimer | TradeReply",
    twitter_description:
      "Read TradeReply's disclaimer to understand the limitations and responsibilities regarding the use of our trading tools and information.",
  };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <div className="commonPolicy">
        <Container>
          <h1>Disclaimer</h1>
          <NoInvestmentAdvice />
          <DigitalProducts />
          <LimitationsLiability />
          <IntellectualProperty />
          <ReportingViolations />
        </Container>
      </div>
    </HomeLayout>
  );
};

export default Disclaimer;
