
import { Col, Container, Row } from "react-bootstrap";
import CommonSearch from "@/Components/UI/CommonSearch";
import { CartIcon } from "@/assets/svgIcons/SvgIcon";
import CustomSelect from "@/Components/UI/Select";
import CustomBreadcrumb from "@/Components/UI/CustomBreadcrumb";
import CustomPagination from "@/Components/UI/CustomPagination";
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/Home/Marketplace.scss";
import Accordions from "./commons/Accordians";
import Link from "next/link";
import Image from 'next/image';
import MegaMenu from "@/Components/UI/MegaMenu";

const Marketplace = () => {
  const options = [
    {
      value: "Shop",
      label: "Shop",
    },
    {
      value: "Buy",
      label: "Buy",
    },
    {
      value: "Sell",
      label: "Sell",
    },
  ];
  const shortoptions = [
    {
      value: "Most popular",
      label: "Most popular",
    },
    {
      value: "Newest Items",
      label: "Newest Items",
    },
    {
      value: "Highest to Lowest Price",
      label: "Highest to Lowest Price",
    },
    {
      value: "Lowest to Highest Price",
      label: "Lowest to Highest Price",
    },
  ];
  const marketproducts = [
    {
      img: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
      title: "Mastering the stock market",
      price: "$11.95",
      rating: 5,
      reviews: "100"
    },
    {
      img: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
      title: "Mastering the stock market",
      price: "$11.95",
      rating: 5,
      reviews: "200"
    },
    {
      img: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
      title: "Mastering the stock market",
      price: "$11.95",
      rating: 5,
      reviews: "300"
    },
    {
      img: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
      title: "Mastering the stock market",
      price: "$11.95",
      rating: 5,
      reviews: "400"
    },
    {
      img: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
      title: "Mastering the stock market",
      price: "$11.95",
      rating: 5,
      reviews: "500"
    },
    {
      img: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
      title: "Mastering the stock market",
      price: "$11.95",
      rating: 5,
      reviews: "600"
    },
  ];

  const metaArray = {
    title: "Marketplace | Explore Products & Services | TradeReply",
    description: "Explore a wide range of products and services in the TradeReply Marketplace. Find the tools you need to enhance your trading experience.",
    canonical_link: "https://www.tradereply.com/marketplace",
    og_site_name: "TradeReply",
    og_title: "Marketplace | Explore Products & Services | TradeReply",
    og_description: "Explore a wide range of products and services in the TradeReply Marketplace. Find the tools you need to enhance your trading experience.",
    twitter_title: "Marketplace | Explore Products & Services | TradeReply",
    twitter_description: "Explore a wide range of products and services in the TradeReply Marketplace. Find the tools you need to enhance your trading experience."
  };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <div className="marketplace py-100">
        <Container>
          <section className="marketplace_inner">
            <div className="marketplace_heading text-center">
              <h1>TradeReply Marketplace</h1>
            </div>
            <div className="marketplace_shopcart d-flex align-items-center justify-content-between justify-content-md-center">
              <div className="marketplace_shopcart_selectshop">
                <MegaMenu/>
              </div>
              <div className="marketplace_shopcart_btn order-md-last">
                <button type="button" className="d-flex align-items-center">
                  <CartIcon />
                  Cart (0)
                </button>
              </div>
              <div className="education_search">
                <CommonSearch placeholder="Explore Products & Strategies" icon={true}
                  name={"marketplace"}
                />
              </div>
            </div>
            <div className="marketplace_inner_heading">
              <h4>TradeReply Courses</h4>
              <CustomBreadcrumb href="#" linkname="Shop" pagename="Courses" />
            </div>
          </section>

          <section className="marketplace_products">
            <Row>
              <Col md={3} xs={12}>
                <Accordions />

              </Col>
              <Col md={9} xs={12}>
                <div className="marketplace_products_productcol">
                  <div className="d-flex align-items-md-center justify-content-between">
                    <div className="marketplace_products_count">
                      <p className="blue_text">1-20 of 20</p>
                    </div>
                    <div className="d-lg-flex justify-content-end">
                      <div className="marketplace_products_pagination ms-lg-5 mb-3 mb-lg-0 order-lg-last">
                        <CustomPagination pageUrl={"marketplace/page/"} />
                      </div>
                      <div className="marketplace_products_sort d-flex align-items-center ">
                        <h5>Sort by:</h5>
                        <CustomSelect
                          options={shortoptions}
                          placeholder="Most popular"
                        />
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="marketplace_products_product_row">
                      <Row className="gx-xl-4">
                        {marketproducts?.map((item, index) => {
                          return (
                            <Col key={index} xl={4} md={6} xs={6}>
                              <Link href="/marketplace/details">
                                <div className="marketplace_products_card">
                                  <div className="marketplace_products_card_img">
                                    <img src={item.img} alt={item.title ? `${item.title} - TradeReply marketplace product` : "TradeReply marketplace item"} />
                                  </div>
                                  <div className="marketplace_products_card_content">
                                    <h4 className="my-2 py-1">{item.title}</h4>
                                    <h4>{item.price}</h4>
                                  </div>
                                  <div className="marketplace_products_card_rating mt-2">
                                    {Array.from({ length: item.rating }).map((_, i) => (
                                      <Image
                                        key={i}
                                        src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-star-single.svg"
                                        alt="star"
                                        width={18}
                                        height={18}
                                      />
                                    ))}
                                    <span>{item.reviews}</span>
                                  </div>
                                </div>
                              </Link>
                            </Col>
                          );
                        })}
                      </Row>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </section>
        </Container>
      </div>
    </HomeLayout>
  );
};

export default Marketplace;
