
'use client'
import { debounce } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { get } from "@/utils/apiUtils";
import { Accordion } from "react-bootstrap";
import CommonSearch from "@/Components/UI/CommonSearch";
import Link from "next/link";
import { BsPersonFill } from "react-icons/bs"; // Make sure this is imported




const Accordions = ({ href, linkname, pagename }) => {
  console.log("CustomBreadcrumb href:", href, typeof href);

  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchedArticles, setSearchedArticles] = useState([]);
  const [activeKey, setActiveKey] = useState(null);

  useEffect(() => {
    if (!searchKeyword) return;

    const fetchHome = async () => {
      try {
        const response = await get("/search/home", { key: searchKeyword });
        setSearchedArticles(response.data);
      } catch (error) {
        console.error("Failed to fetch search results:", error);
      }
    };

    fetchHome();
  }, [searchKeyword]);

  const debouncedSearch = useCallback(
    debounce((searchTerm) => {
      setSearchKeyword(searchTerm);
    }, 300),
    []
  );

  const handleHomeSearch = (event) => {
    debouncedSearch(event.target.value);
  };

  const handleAccordionToggle = (key) => {
    setActiveKey(prevKey => (prevKey === key ? null : key));
  };


  return (
    <>
      <div className="marketplace_products_filter">
        <Accordion defaultActiveKey="0">
          <Accordion.Item eventKey="0">
            <Accordion.Header>Asset Type</Accordion.Header>
            <Accordion.Body className="pt-0">
              <button type="button">Stocks</button>
              <button type="button">Forex (Foreign Exchange)</button>
              <button type="button">Cryptocurrency</button>
              <button type="button">Options</button>
              <button type="button">Futures</button>
              <button type="button">Indices</button>
              <button type="button">Commodities</button>
              <button type="button">Bonds & Fixed Income</button>
              <button type="button">General Trading (Applies to Multiple Markets)</button>
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item eventKey="1">
            <Accordion.Header>Rating</Accordion.Header>
            <Accordion.Body className="pt-0">
              <button type="button">5 Stars (Highest Rated)</button>
              <button type="button">4+ Stars (Highly Rated)</button>
              <button type="button">3+ Stars (Mid-Tier & Above)</button>
              <button type="button">New & Unrated</button>
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item eventKey="2">
            <Accordion.Header>Format</Accordion.Header>
            <Accordion.Body className="pt-0">
              <button type="button">Video Course</button>
              <button type="button">Ebook / PDF Guide</button>
              <button type="button">Live Webinar</button>
              <button type="button">Recorded Webinar</button>
              <button type="button">Trading Indicator (TradingView, MT4, MT5, etc.)</button>
              <button type="button">Trading Bot / Automation Script</button>
              <button type="button">Spreadsheet / Calculator</button>
              <button type="button">Market Report / Research Document</button>
              <button type="button">Private Mentorship / Coaching Session</button>
              <button type="button">Community / Membership Access</button>
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item eventKey="3">
            <Accordion.Header>Skill Level</Accordion.Header>
            <Accordion.Body className="pt-0">
              <button type="button">Beginner-Friendly</button>
              <button type="button">Intermediate</button>
              <button type="button">Advanced</button>
              <button type="button">All Levels</button>
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item eventKey="4">
            <Accordion.Header>Price Range</Accordion.Header>
            <Accordion.Body className="pt-0">
              <button type="button">Free</button>
              <button type="button">Under $50</button>
              <button type="button">$50 - $100</button>
              <button type="button">$100 - $250</button>
              <button type="button">$250 - $500</button>
              <button type="button">$500+ (Premium Products & Mentorships)</button>
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item eventKey="5">
            <Accordion.Header>Trading Platform</Accordion.Header>
            <Accordion.Body className="pt-0">
              <button type="button">TradingView</button>
              <button type="button">MetaTrader 4 (MT4)</button>
              <button type="button">MetaTrader 5 (MT5)</button>
              <button type="button">cTrader</button>
              <button type="button">ThinkorSwim</button>
              <button type="button">NinjaTrader</button>
              <button type="button">Python / Algorithmic Trading</button>
              <button type="button">Excel / Google Sheets</button>
              <button type="button">General (Not Platform-Specific)</button>
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item eventKey="6">
            <Accordion.Header>Trading Style</Accordion.Header>
            <Accordion.Body className="pt-0">
              <button type="button">Day Trading</button>
              <button type="button">Swing Trading</button>
              <button type="button">Scalping</button>
              <button type="button">Trend Trading</button>
              <button type="button">Algorithmic & Bot Trading</button>
              <button type="button">Options Selling / Income Strategies</button>
              <button type="button">Crypto Arbitrage & DeFi Trading</button>
              <button type="button">Long-Term Investing</button>
              <button type="button">Market Psychology & Risk Management</button>
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item eventKey="7">
            <Accordion.Header>Time Commitment</Accordion.Header>
            <Accordion.Body className="pt-0">
              <button type="button">Under 1 Hour</button>
              <button type="button">1 - 3 Hours</button>
              <button type="button">3 - 10 Hours</button>
              <button type="button">10+ Hours (In-Depth Masterclasses)</button>
            </Accordion.Body>
          </Accordion.Item>

          <Accordion.Item eventKey="8">
            <Accordion.Header>Seller Type</Accordion.Header>
            <Accordion.Body className="pt-0">
              <button type="button">Verified Seller</button>
              <button type="button">Top Rated Seller</button>
              <button type="button">New Seller</button>
              <button type="button">TradeReply Official Content</button>
            </Accordion.Body>
          </Accordion.Item>
          {/* for filter  */}
          <Accordion.Item eventKey="9">
            <Accordion.Header>Seller</Accordion.Header>
            <Accordion.Body className="pt-0" style={{ zIndex: '9999' }}>
              <CommonSearch
                label=""
                placeholder="Search Seller Name"
                onChange={handleHomeSearch}
                icon={true}
                name="sellerSearch"
              />

              {/* {searchedArticles.length > 0 && searchKeyword ? (
                <div className="position-relative">
                  <div id="comboList" className="list-group position-absolute w-100">
                    {searchedArticles.map((item, index) => (
                      <div
                        key={index}
                        className="list-group-item d-flex justify-content-between align-items-center"
                      >
                        <div className="d-flex align-items-center gap-2">
                          <img
                            src={item.profileImage || "/default-profile.png"} // Replace with real field or default
                            alt="Profile"
                            className="rounded-circle"
                            style={{ width: "24px", height: "24px" }}
                          />
                          <span className="text-primary" style={{ fontWeight: 500 }}>
                            {item.title}
                          </span>
                        </div>

                        <button type="button" className="btn btn-sm btn-primary px-3">
                          Add
                        </button>
                      </div>
                    ))}

                    <Link
                      href={`/search?search=${searchKeyword}`}
                      className="list-group-item list-group-item-action text-primary text-center"
                      id="viewAll"
                    >
                      View All
                    </Link>
                  </div>
                </div>
              ) : null} */}


              {searchKeyword ? (
                <div className="position-relative">
                  <div id="comboList" className="list-group position-absolute w-100" style={{
                    zIndex: 99999,
                    backgroundColor: "white", // optional, to avoid background bleed-through
                    boxShadow: "0 4px 12px rgba(0,0,0,0.1)" // optional, better visibility
                  }}
                  >

                    {[...Array(5)].map((_, index) => (
                      <div
                        key={index}
                        className="list-group-item px-2 py-1"
                        style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
                      >
                        {/* LEFT: Icon + Name */}
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <BsPersonFill className="me-2 text-dark" />
                          <span className="text-primary" style={{ fontWeight: 600, fontSize: "16px" }}>
                            George Mans
                          </span>
                        </div>

                        {/* RIGHT: Add Button */}
                        <div>
                          <button
                            type="button"
                            className="btn btn-sm"
                            style={{
                              backgroundColor: "#00ADEF",
                              color: "#fff",
                              borderRadius: "20px",
                              padding: "2px 12px",
                              fontSize: "12px",
                              fontWeight: "600"
                            }}
                          >
                            Add
                          </button>
                        </div>
                      </div>
                    ))}

                    {/* View All */}
                    <Link
                      href="/marketplace/sellers"
                      className="list-group-item text-center text-primary"
                      style={{ fontSize: "13px", cursor: "pointer", display: "block" }}
                    >
                      View All
                    </Link>
                  </div>
                </div>
              ) : null}


            </Accordion.Body>
          </Accordion.Item>
        </Accordion>

      </div>

    </>
  );
};

export default Accordions;
