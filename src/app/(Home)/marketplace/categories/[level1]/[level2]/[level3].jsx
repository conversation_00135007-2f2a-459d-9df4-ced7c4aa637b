import fs from 'fs';
import path from 'path';

export default function CategoryPage({ categoryPath }) {
  return (
    <div className="p-8">
      <h1 className="text-xl font-bold">Category Page</h1>
      <p>Current Path: {categoryPath.join(' / ')}</p>
    </div>
  );
}

export async function getStaticPaths() {
  const filePath = path.join(process.cwd(), 'data', 'tradereply-categories.json');
  const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

  const paths = [];

  jsonData.categories.forEach((level1) => {
    level1.subcategories.forEach((level2) => {
      level2.subcategories.forEach((level3) => {
        paths.push({
          params: {
            level1: level1.slug,
            level2: level2.slug,
            level3: level3.slug,
          },
        });
      });
    });
  });

  return {
    paths,
    fallback: false, // Set to true if you want to generate pages on demand
  };
}

export async function getStaticProps(context) {
  const { level1, level2, level3 } = context.params;

  return {
    props: {
      categoryPath: [level1, level2, level3],
    },
  };
}
