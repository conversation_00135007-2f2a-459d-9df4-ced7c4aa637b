"use client";

import { useRouter } from 'next/navigation';
import HomeLayout from "@/Layouts/HomeLayout";
import RecentPost from "@/Components/common/Home/RecentPost";
import { Container } from "react-bootstrap";
import Link from "next/link";
import CustomBreadcrumb from "@/Components/UI/CustomBreadcrumb";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
dayjs.extend(relativeTime);

export default function BlogContent({response}) {
const blog = response?.data;
const nextBlog = response?.next_article || null;
const sanitizedContent = blog?.content;
const formattedDate = dayjs(blog?.created_at).format("MMMM DD, YYYY");
const time = blog?.created_at ? dayjs(blog?.created_at).fromNow() : ""

const handleNextBlogClick = () => {
    if (nextBlog) {
      router.push(`/blog/${nextBlog.slug}`);
    }
};

  return (
   <HomeLayout>
               <div className="blog_detail py-100">
                 <Container>
                   <div className="blog_detail_inner">
                     <div className="blog_detail_heading">
                       <Link href={`/category/${blog?.primary_category?.slug ?? "N/A"}`}>
                         <button type="button" className="blog_detail_tag">
                           {blog?.primary_category?.title ?? "N/A"}
                         </button>
                       </Link>
                       <h1>
                         {blog?.title}
                       </h1>
                       <div className="blog_detail_breadcrumb">
                         <CustomBreadcrumb
                           href={`/blog`}
                           linkname={'Blog'}
                           pagename={blog?.title}
                         />
                       </div>
                       <h5>{formattedDate}</h5>
                     </div>

                     <div className="blog_detail_postimg">
                       <img
                         src={blog?.feature_image_url}
                         alt={blog?.title || "Blog article image"}
                       />
                     </div>

                     <div className="blog_detail_text">
                       <p dangerouslySetInnerHTML={{ __html: sanitizedContent }} />
                     </div>

                     <div className="blog_detail_author">
                       {nextBlog ? (
                         <>
                           <button
                             type="button"
                             className="blog_detail_author_btn"
                             onClick={handleNextBlogClick}
                           >
                             Next Article
                           </button>

                           <RecentPost
                             img={nextBlog?.feature_image_url}
                             title={nextBlog?.title}
                             text={nextBlog?.summary}
                             coinname={nextBlog?.coinname}
                             href={`/blog/${nextBlog?.slug}`}
                             time={time}
                             category={nextBlog?.primary_category?.title ?? "N/A"}
                           />
                         </>
                       ) : (
                         <p>No next article available.</p>
                       )}
                     </div>
                   </div>
                 </Container>
               </div>
             </HomeLayout>
  );
}