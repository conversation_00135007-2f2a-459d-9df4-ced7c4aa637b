import BlogClient from "./BlogClient";
import { get } from "@/utils/apiUtils";
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from "@/Seo/Meta/MetaHead";

export default async function BlogPage({ searchParams }) {
  const page = searchParams?.page || 1;

  let data = {
    latest_blogs: [],
    top_blogs: [],
    meta: {}
  };

  try {
    const response = await get("/article", {
      page,
      type: "blog"
    });
    data = response.data;
  } catch (error) {
    console.error("Error fetching blog data:", error);
  }

  const metaArray = {
      title: "TradeReply Blog | Insights & Strategies for Traders",
      description: "Explore the latest insights and strategies on TradeReply.com's blog. Stay updated with expert analysis on trading, market trends, and financial tools.",
      og_site_name: "TradeReply",
      og_title: "TradeReply Blog | Insights & Strategies for Traders",
      og_description: "Explore the latest insights and strategies on TradeReply.com's blog. Stay updated with expert analysis on trading, market trends, and financial tools.",
      twitter_title: "TradeReply Blog | Insights & Strategies for Traders",
      twitter_description: "Explore the latest insights and strategies on TradeReply.com's blog. Stay updated with expert analysis on trading, market trends, and financial tools."
   };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <BlogClient initialData={data} currentPage={page} />
    </HomeLayout>
  );
}
