
'use client';


import { Container } from "react-bootstrap";
import CommonButton from "@/Components/UI/CommonButton";
import NavLink from "@/Components/UI/NavLink";
import "../../../css/Home/ReferReward.scss";
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from "@/Seo/Meta/MetaHead";

const ReferReward = () => {
  const metaArray = {
    title: "Refer a Friend | Earn Rewards with TradeReply",
    description:
      "Invite your friends to TradeReply.com and earn rewards. Share the benefits of advanced trading tools and real-time analytics with your network.",
    canonical_link: "https://www.tradereply.com/refer-a-friend",
    og_site_name: "TradeReply",
    og_title: "Refer a Friend | Earn Rewards with TradeReply",
    og_description:
      "Invite your friends to TradeReply and earn rewards. Share the benefits of advanced trading tools and real-time analytics with your network.",
    twitter_title: "Refer a Friend | Earn Rewards with TradeReply",
    twitter_description:
      "Invite your friends to TradeReply and earn rewards. Share the benefits of advanced trading tools and real-time analytics with your network.",
  };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <div className="py-100 refer_reward">
        <Container>
          <div className="refer_reward_inner">
            <div className="refer_reward_heading text-center">
              <div className="d-flex align-items-center justify-content-center">
                <img
                  className="refer_reward_logo"
                  src="https://cdn.tradereply.com/dev/site-assets/tradereply-trading-tools.svg"
                  alt="Invite friends to TradeReply and earn rewards for referrals"
                />

                <h1>Refer & Reward</h1>
              </div>
              <h5 className="my-4 py-2">
                Love TradeReply? Share it and Earn! <br /> Invite your friends
                to join TradeReply and both of you get rewarded! For every
                friend who signs up and subscribes, you&apos;ll each receive $15
                towards any subscription.
              </h5>
              <div className="text-center my-5">
                <CommonButton
                  title="invite Friends & Earn $15"
                  className="white-btn"
                />
              </div>
              <h5 className="">
                Complete details in{" "}
                <NavLink href="/terms">Terms of Service</NavLink>
              </h5>
            </div>
          </div>
        </Container>
      </div>
    </HomeLayout>
  );
};

export default ReferReward;
