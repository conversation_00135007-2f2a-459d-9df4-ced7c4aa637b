import React from "react";

export default function ReferFriendProgram() {
  return (
    <>
      <div className="inner_content">
        <h4>5. Refer a Friend Program</h4>

        <h5>5.1 Program Overview</h5>
        <p>
          TradeReply may offer a Refer a Friend program that allows users to invite
          friends to register for TradeReply services. When a referred friend
          successfully registers and subscribes to a paid plan, both the referring
          user and the referred friend may be eligible to receive a $15 subscription
          credit. These credits have no cash value and can only be applied toward
          future TradeReply subscription payments.
        </p>

        <h5>5.2 Eligibility and Application of Credits</h5>
        <ul>
          <li>
            Referral credits are available only to users who meet all eligibility
            requirements described in these Terms and any additional program terms
            published by TradeReply.
          </li>
          <li>
            New referred users who are eligible for the 30-day free trial may still
            participate in the Refer a Friend program.
          </li>
          <li>
            Referred users may apply their $15 credit immediately toward their first
            paid billing cycle, whether after completing the 30-day free trial or by
            choosing to subscribe without a trial.
          </li>
          <li>
            The referred user must pay the advertised subscription price of their
            chosen plan (monthly or annual) for their first billing cycle, minus only
            the Refer a Friend credit. No other promotional codes, account credits,
            or site discounts may be applied to that billing cycle in combination
            with the Refer a Friend credit.
          </li>
          <li>
            The referring user will receive their $15 credit only after the referred
            user has successfully completed their first paid billing cycle as
            described, paid the required amount, and not requested a refund,
            chargeback, or payment reversal.
          </li>
          <li>
            If the referred user cancels, requests a refund, or reverses payment for
            their first paid billing cycle, no credit will be awarded to the
            referring user.
          </li>
        </ul>

        <h5>5.3 Refunds, Chargebacks, and Abuse</h5>
        <ul>
          <li>
            If the referred user requests a refund, issues a chargeback, or otherwise
            reverses payment for their first paid billing cycle, the referring user
            will forfeit their $15 credit, and any credits already issued may be
            revoked by TradeReply.
          </li>
          <li>
            TradeReply reserves the right to recover any credits that were improperly
            awarded or used in violation of these Terms by charging the user’s
            payment method on file or applying an offset to future invoices.
          </li>
        </ul>

        <h5>5.4 Technology and Tracking</h5>
        <p>
          To operate the Refer a Friend program, TradeReply may use cookies, local
          storage, session tracking, referral URLs, IP addresses, and standard
          browser or device data in accordance with our Privacy Policy. By
          participating in the program, you consent to TradeReply’s use of these
          technologies to track and verify referral activity.
        </p>

        <h5>5.5 Program Changes and Termination</h5>
        <p>
          TradeReply reserves the right to change, suspend, or terminate the Refer a
          Friend program at any time, with or without notice, including the ability
          to modify eligibility requirements or revoke credits if fraudulent,
          abusive, or suspicious activity is detected.
        </p>
      </div>
    </>
  );
}
