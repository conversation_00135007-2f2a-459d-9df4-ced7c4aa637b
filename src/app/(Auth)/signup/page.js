"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import InputError from "@/Components/UI/InputError";
import TextInput from "@/Components/UI/TextInput";
import { useRouter } from "next/navigation";
import AuthLayout from "@/Layouts/AuthLayout";
import CommonButton from "@/Components/UI/CommonButton";
import { RightArrowIconSvg, CheckIcon, RedErrorCircle } from "@/assets/svgIcons/SvgIcon";
import ThirdPartyLogin from "@/Components/common/ThirdPartyLogin";
import LoginFooter from "@/Components/UI/LoginFooter";
import { signupSchema } from "@/validations/schema";
import NavLink from "@/Components/UI/NavLink";
import { Formik, Field, Form, ErrorMessage } from "formik";
import axios from "axios";
import { register } from "@/utils/auth";
import toast from "react-hot-toast";
import MetaHead from "@/Seo/Meta/MetaHead";
import AuthLogo from "@/Components/common/AuthLogo";
import PasswordValidation from "@/Components/common/Auth/PasswordValidation";
import { v4 as uuidv4 } from "uuid";
import { hashInput } from "@/utils/hashInput";


const initialValues = {
  email: "",
  password: "",
  password_confirmation: ""
};

export default function Register() {
  const router = useRouter();
  const [authError, setAuthError] = useState("");
  const [showPasswordCheck, setShowPasswordCheck] = useState(false);
  const [password, setPassword] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const uuid = uuidv4(); // Generate a unique identifier
  useEffect(() => {
    sessionStorage.removeItem("masked_email");
    sessionStorage.removeItem("identifier_type");
    sessionStorage.removeItem("signup_data");
    sessionStorage.removeItem("signup_facebook");
    sessionStorage.removeItem("signup_google");
    sessionStorage.removeItem("reset_password_data");
    const savedData = JSON.parse(sessionStorage.getItem("signup_data"));

    if (!savedData || Date.now() > savedData.expiry) {
      sessionStorage.removeItem("signup_data");
      router.push("/signup");
    }
  }, []);

  const handleSubmit = async (values, { setErrors, resetForm }) => {
    const { email, password } = values;
    const expiresInMinutes = 15;
    const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;

    const allowedPricings = ["free", "pro", "premium", "essential"];

    const pricing = allowedPricings.includes(sessionStorage.getItem("pricing"))
      ? sessionStorage.getItem("pricing")
      : "free";

    const trial = sessionStorage.getItem("trial") === "true";

    try {
      const response = await register({ email, password, uuid, pricing, trial });

      if (!response.success) {
        setAuthError(response.errors.email?.[0]);
        return;
      }
      setAuthError("");
      sessionStorage.removeItem("pricing");
      sessionStorage.removeItem("trial");
      sessionStorage.setItem("masked_email", hashInput(values.email));
      sessionStorage.setItem(
        "signup_data",
        JSON.stringify({ uuid, expiresAt })
      );

      router.push(`/security-check?signup=signup_data`);
    } catch (error) {
      resetForm();
      setAuthError("An unexpected error occurred.");
    }
  };


  const metaArray = {
    noindex: true,
    title: "Sign Up for TradeReply | Start Trading Smarter",
    description: "Join TradeReply.com today! Sign up to unlock advanced trading tools, real-time analytics, and expert strategies to enhance your trading success.",
    canonical_link: "https://www.tradereply.com/signup",
    og_site_name: "TradeReply",
    og_title: "Sign Up for TradeReply | Start Trading Smarter",
    og_description: "Join TradeReply.com today! Sign up to unlock advanced trading tools, real-time analytics, and expert strategies to enhance your trading success.",
    twitter_title: "Sign Up for TradeReply | Start Trading Smarter",
    twitter_description: "Join TradeReply.com today! Sign up to unlock advanced trading tools, real-time analytics, and expert strategies to enhance your trading success.",
  };

  return (
    <AuthLayout>
      <MetaHead props={metaArray} />
      <div className="loginCommon_rightSide signup_form">
        <div className="loginCommon_rightSide_inner">
          <div className="backbtn">
            <Link href={"/"}>
              <RightArrowIconSvg color="svg-white_baseblue" /> Return to Home
            </Link>
          </div>
          <div className="loginCommon_rightSide_formBox">
            <AuthLogo />
            <div className="loginHeading">
              <h1>Sign up with</h1>
            </div>
            <ThirdPartyLogin type="signup" />
            <div className="orLine">
              <span>or start with</span>
            </div>
            <div className="loginTabs">
              <div className="loginForm">
                <Formik
                  initialValues={initialValues}
                  validationSchema={signupSchema}
                  onSubmit={handleSubmit}
                >
                  {({
                    handleChange,
                    values,
                    errors,
                    touched,
                    isSubmitting,
                    resetForm,
                    dirty,
                    isValid,
                    submitCount
                  }) => (
                    <Form>
                      <div className="authCorrectIcon">
                        <div className="checkIcon">
                          {values.email && !errors.email && dirty && <CheckIcon width="25" height="25" />}
                        </div>
                        <Field name="email">
                          {({ field, meta }) => (
                            <TextInput
                              {...field}
                              placeholder="Email"
                              type="text"
                              maxLength={100}
                              error={meta.touched && meta.error ? <InputError message={meta.error} /> : null}
                              isError={meta.touched && meta.error ? true : false}
                              onChange={(e) => {
                                field.onChange(e);
                                setAuthError(null);
                              }}
                            />
                          )}
                        </Field>
                      </div>

                      <div className="authCorrectIcon">
                        <div className="checkIcon">
                          {values.password && !errors.password && dirty && <CheckIcon width="25" height="25" />}
                        </div>
                        <Field name="password">
                          {({ field, meta, form: { touched, errors } }) => (
                            <>
                              <TextInput
                                {...field}
                                placeholder="Password"
                                type="password"
                                maxLength={64}
                                onChange={(e) => {
                                  const cleanedValue = e.target.value.replace(/\s/g, "");
                                  field.onChange({ target: { name: e.target.name, value: cleanedValue } });
                                  setPassword(cleanedValue);
                                  setShowPasswordCheck(true);
                                  setIsFocused(false);
                                  setAuthError(null);
                                }}
                                onBlur={(e) => {
                                  field.onBlur(e);
                                  if (e.target.value.replace(/\s/g, "").length === 0) {
                                    setShowPasswordCheck(false);
                                    setIsFocused(false);
                                  }
                                }}
                                onFocus={() => {
                                  setShowPasswordCheck(true);
                                }}
                                error={
                                  submitCount > 0 && meta.error ? <InputError message={meta.error} /> : null
                                }
                                isError={submitCount > 0 && meta.error ? true : false}
                              />
                              {showPasswordCheck && (
                                <PasswordValidation
                                  password={password}
                                  isValid={touched.password && !errors.password}
                                />
                              )}
                            </>
                          )}
                        </Field>
                      </div>

                      <div className="authCorrectIcon">
                        <div className="checkIcon">
                          {values.password_confirmation && !errors.password_confirmation && dirty && <CheckIcon width="25" height="25" />}
                        </div>
                        <Field name="password_confirmation">
                          {({ field, meta }) => (
                            <TextInput
                              {...field}
                              placeholder="Confirm Password"
                              maxLength={64}
                              type="password"
                              error={values.password_confirmation && errors.password_confirmation && dirty ? <InputError message={meta.error} /> : null}
                              isError={values.password_confirmation && errors.password_confirmation && dirty ? true : false}
                              onChange={(e) => {
                                field.onChange(e);
                                setAuthError(null);
                              }}
                            />
                          )}
                        </Field>
                      </div>

                      <div className="w-100">
                        <CommonButton
                          type="submit"
                          title={isSubmitting ? 'Loading' : 'Create Account'}
                          fluid
                          disabled={!(dirty && isValid) || isSubmitting}
                        />
                      </div>

                      <div className="anAccount mt-3 text-center">
                        <h6>
                          Already have an account?
                          <NavLink href="/login" className="ml-1">Login</NavLink>
                        </h6>
                      </div>

                      {authError && (
                        <div className="d-flex justify-content-center">
                          <div className="signup_invalid_credential mt-3">
                            <div className="d-flex gap-1 align-items-center mb-1">
                              <RedErrorCircle />
                              <p className="ml-2">{authError}</p>
                            </div>

                          </div>
                        </div>
                      )}
                    </Form>
                  )}
                </Formik>

              </div>
            </div>
          </div>
          <div className="mt-4 mt-md-5">
            <LoginFooter />
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}

