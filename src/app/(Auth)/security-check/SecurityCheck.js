"use client";
import { useRef, useState, useEffect } from "react";
import { ResendCodeIcon, ContactCustomerSupport } from "@/assets/svgIcons/SvgIcon";
import { securitySchema } from "@/validations/schema";
import { resendVerificationCode, verifyEmailToken } from "@/utils/auth";
import { Formik, Field, Form } from "formik";
import { useRouter, useSearchParams } from "next/navigation";
import CommonButton from "@/Components/UI/CommonButton";
import AuthLayout from "@/Layouts/AuthLayout";
import Link from "next/link";
import LoginFooter from "@/Components/UI/LoginFooter";
import AuthLogo from "@/Components/common/AuthLogo";
import MetaHead from "@/Seo/Meta/MetaHead";
import InputError from "@/Components/UI/InputError";
import toast from "react-hot-toast";
import { post, verifyRestoreCode } from "@/utils/apiUtils";
import { hashInput } from "@/utils/hashInput";
import "../../../css/common/textInput.scss"
import { v4 as uuidv4 } from "uuid";
import axios from "axios";
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';
import { getSecurityConfig, getCachedProtectedRoutes, isValidSecureRoute } from "@/utils/securityConfig";
import { SECURITY_PROTECTED_ROUTES } from "@/config/securityRoutes";
import UseRestoreCodeModal from "./UseRestoreCodeModal";
import NewRestoreCodeModal from "./NewRestoreCodeModal";


const initialValues = {
    security_code: "",
};

export default function SecurityCheck() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const inputRefs = useRef([]);
    const expiredOnceRef = useRef(false);
    const dispatch = useDispatch();

    const [isSessionValid, setIsSessionValid] = useState(null);
    const [maskedEmail, setMaskedEmail] = useState("");
    const [type, setType] = useState("");
    const [resendMessage, setResendMessage] = useState("");
    const [cooldown, setCooldown] = useState(0);
    const [isButtonDisabled, setIsButtonDisabled] = useState(false);
    const [isRotating, setIsRotating] = useState(false);
    const [securitySessionId, setSecuritySessionId] = useState(null);
    const [isCheckingExistingCookie, setIsCheckingExistingCookie] = useState(true);
    const [showManualRedirect, setShowManualRedirect] = useState(false);
    const [manualRedirectUrl, setManualRedirectUrl] = useState(null);

    // Restore code modal states
    const [showUseRestoreCodeModal, setShowUseRestoreCodeModal] = useState(false);
    const [showNewRestoreCodeModal, setShowNewRestoreCodeModal] = useState(false);
    const [newRestoreCode, setNewRestoreCode] = useState("");
    const [isVerifyingRestoreCode, setIsVerifyingRestoreCode] = useState(false);

    const provider = searchParams.get("provider");
    const resetPassword = searchParams.has("resetPassword");
    const rawNextUrl = searchParams.get("next"); // For account security verification
    const verificationType = searchParams.get("type"); // For email update verification
    const isEmailUpdate = verificationType === "email-update";

    // Security verification protected routes (from centralized local configuration)
    const securityProtectedRoutes = SECURITY_PROTECTED_ROUTES;

    // Note: Using imported isValidSecureRoute function from securityConfig utils

    // Validate and sanitize the next URL
    const nextUrl = rawNextUrl && isValidSecureRoute(rawNextUrl) ? rawNextUrl : null;
    const isAccountSecurity = !!nextUrl && !isEmailUpdate; // If valid next parameter exists and not email update, it's account security verification

    // Log security validation for monitoring
    if (rawNextUrl && !nextUrl) {
        console.warn('Invalid next URL detected and rejected:', rawNextUrl);
    }

    const dataType = resetPassword
        ? "reset_password_data"
        : provider
            ? `signup_${provider}`
            : "signup_data";

    // Function to check if security cookie is valid
    const isValidSecurityCookie = (cookieValue) => {
        if (!cookieValue) return false;

        // Simple validation for 'true' value (legacy)
        if (cookieValue === 'true') return true;

        // Simple validation for 'verified' value (fallback)
        if (cookieValue === 'verified') return true;

        // Check if it's a session token (64 character hex string)
        if (typeof cookieValue === 'string' && cookieValue.length === 64 && /^[a-f0-9]+$/i.test(cookieValue)) {
            return true;
        }

        // Check if it's a UUID format (with hyphens)
        if (typeof cookieValue === 'string' && /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(cookieValue)) {
            return true;
        }

        // Validate encrypted payload with timestamp
        try {
            const payload = JSON.parse(atob(cookieValue));
            if (payload.verified_at) {
                const verifiedAt = new Date(payload.verified_at);
                const now = new Date();
                const diffInSeconds = (now.getTime() - verifiedAt.getTime()) / 1000;
                // Check if within reasonable window (backend will do authoritative validation)
                // Use 15 minutes as a safe buffer since backend config may vary
                return diffInSeconds <= 900;
            }
        } catch (e) {
            return false;
        }
        return false;
    };

    // Function to get cookie value
    const getCookie = (name) => {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    };


    // ------------------------
    // Check for existing valid security cookie and redirect immediately
    useEffect(() => {
        if (isAccountSecurity && nextUrl) {
            // Get cookie name from local configuration
            const config = getSecurityConfig();
            const cookieName = config.cookie_name;

            const securityCookie = getCookie(cookieName);
            if (securityCookie && isValidSecurityCookie(securityCookie)) {
                console.log('Valid security cookie found, redirecting immediately to:', nextUrl);

                // Extract path from full URL if needed
                let redirectUrl = nextUrl;
                try {
                    if (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://')) {
                        const url = new URL(redirectUrl);
                        redirectUrl = url.pathname + url.search + url.hash;
                    }
                } catch (e) {
                    console.warn('Failed to parse next URL:', redirectUrl);
                }

                console.log('Redirecting already verified user to:', redirectUrl);

                // Use window.location.href for production reliability
                window.location.href = redirectUrl;
                return;
            }
        }

        // If we reach here, no valid cookie found, so show the verification form
        setIsCheckingExistingCookie(false);
    }, [isAccountSecurity, nextUrl, router]);

    // ------------------------
    // Validate session
    useEffect(() => {
        const checkSessionValidity = () => {
            // For account security verification or email update, skip session storage validation
            if (isAccountSecurity || isEmailUpdate) {
                return true;
            }

            // If no valid next parameter and no dataType, redirect to account overview
            if (!dataType && !rawNextUrl) {
                console.warn('No valid session data or next parameter found, redirecting to account overview');
                router.replace('/account/overview');
                return false;
            }

            if (!dataType) return false;

            const savedData = JSON.parse(sessionStorage.getItem(dataType) || "{}");

            if ((!savedData.uuid || !savedData.expiresAt) && !expiredOnceRef.current) {
                expiredOnceRef.current = true;
                router.replace("/login");
                return false;
            }

            const expired = Date.now() > savedData.expiresAt;

            if (expired && !expiredOnceRef.current) {
                expiredOnceRef.current = true;
                sessionStorage.removeItem(dataType);
                sessionStorage.removeItem("masked_email");
                sessionStorage.removeItem("identifier_type");

                sessionStorage.setItem("sessionExpired", "true");
                // toast.error("Session expired. Please request a new code.");

                router.replace(resetPassword ? "/locate-account" : "/login");
                return false;
            }

            return true;
        };

        if (checkSessionValidity()) {
            setIsSessionValid(true);

            const interval = setInterval(checkSessionValidity, 5000);
            return () => clearInterval(interval);
        }
    }, [dataType, isAccountSecurity]);

    // ------------------------
    // Load masked email from sessionStorage or fetch from backend
    useEffect(() => {
        if (!isSessionValid) return;

        // For email update verification, get data from session storage
        if (isEmailUpdate) {
            const sessionId = sessionStorage.getItem('email_update_session_id');
            const maskedEmail = sessionStorage.getItem('email_update_masked_email');

            if (sessionId && maskedEmail) {
                setSecuritySessionId(sessionId);
                setMaskedEmail(maskedEmail);
                setType("email");
            } else {
                console.warn("Email update session data not found, redirecting back");
                router.replace("/account/email/setup");
            }
            return;
        }

        // For account security verification, send verification code immediately
        if (isAccountSecurity) {
            const sendSecurityCode = async () => {
                try {
                    const response = await post('/security-verification/send-code');
                    if (response.success) {
                        setSecuritySessionId(response.session_id);
                        setMaskedEmail(response.masked_email || "your email");
                        setType("email");
                        } else {
                        console.log("Failed to send verification code",response);
                        toast.error("Failed to send verification code");
                        router.replace("/account/overview");
                    }
                } catch (error) {
                    console.log("Failed to send verification code",error);
                    toast.error("Failed to send verification code");
                    router.replace("/account/overview");
                }
            };
            sendSecurityCode();
            return;
        }

        const masked = sessionStorage.getItem("masked_email");
        const identifierType = sessionStorage.getItem("identifier_type");

        if (masked) {
            setMaskedEmail(masked);
            if (identifierType) setType(identifierType);
            return;
        }

        if (dataType.startsWith("signup_") && dataType !== "signup_data") {
            const fetchMaskedEmail = async () => {
                const savedData = JSON.parse(sessionStorage.getItem(dataType) || "{}");
                const uuid = savedData.uuid;
                const expiresAt = savedData.expiresAt;

                if (!uuid || !expiresAt || Date.now() > expiresAt) {
                    toast.error("Signup session expired. Please start over.");
                    sessionStorage.removeItem(dataType);
                    sessionStorage.removeItem("masked_email");
                    sessionStorage.removeItem("identifier_type");
                    router.replace("/signup");
                    return;
                }

                const providerName = dataType.replace("signup_", "");
                console.log("Fetching email for:", providerName, uuid);

                try {
                    const response = await axios.get(
                        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/auth/signup-data/${providerName}/${uuid}`
                    );

                    console.log("Fetched signup-data:", response.data);

                    const { email } = response.data;

                    if (email) {
                        const masked = hashInput(email);
                        setMaskedEmail(masked);
                        sessionStorage.setItem("masked_email", masked);
                        sessionStorage.setItem("identifier_type", "email");
                    } else {
                        throw new Error("Email not found in response");
                    }
                } catch (error) {
                    if (error.response) {
                        console.error("Fetch failed:", error.response.status, error.response.data);
                    } else {
                        console.error("Network or Axios error:", error.message);
                    }
                    toast.error("Failed to retrieve your email. Please start over.");
                    // Optionally redirect: router.replace("/signup");
                }
            };

            fetchMaskedEmail();
        }
    }, [isSessionValid, dataType]);



    useEffect(() => {
        const storedCooldown = localStorage.getItem("resend_cooldown");
        let intervalId;

        if (storedCooldown) {
            const remainingTime = Math.floor((+storedCooldown - Date.now()) / 1000);
            if (remainingTime > 0) {
                intervalId = startCooldown(remainingTime);
            } else {
                intervalId = startCooldown(60);
            }
        } else {
            intervalId = startCooldown(60);
        }

        return () => clearInterval(intervalId);
    }, []);

    const startCooldown = (duration = 60) => {
        setIsButtonDisabled(true);
        setCooldown(duration);
        localStorage.setItem("resend_cooldown", Date.now() + duration * 1000);

        const interval = setInterval(() => {
            setCooldown(prev => {
                if (prev <= 1) {
                    clearInterval(interval);
                    setIsButtonDisabled(false);
                    localStorage.removeItem("resend_cooldown");
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
        return interval;
    };

    const handleSubmit = async (values, { setSubmitting, setErrors }) => {
        // Handle email update verification
        if (isEmailUpdate) {
            if (!securitySessionId) {
                setErrors({ security_code: "Session expired. Please refresh the page." });
                return;
            }

            try {
                const response = await post('/account/email/verify', {
                    code: values.security_code,
                    session_id: securitySessionId
                });

                if (response.success) {
                    // Clear email update session data
                    sessionStorage.removeItem('email_update_session_id');
                    sessionStorage.removeItem('email_update_masked_email');
                    const newEmail = sessionStorage.getItem('email_update_new_email');
                    sessionStorage.removeItem('email_update_new_email');

                    // Update Redux store and localStorage with new email
                    if (newEmail && response.user) {
                        const updatedUser = { ...response.user, email: newEmail };
                        dispatch(setUser(updatedUser));
                        localStorage.setItem('user', JSON.stringify(updatedUser));
                    }

                    // Redirect back to email setup page with success
                    const redirectUrl = rawNextUrl || "/account/email/setup";
                    router.push(`${redirectUrl}?success=email-updated`);
                } else {
                    setErrors({ security_code: response.message || "Invalid verification code" });
                }
            } catch (error) {
                console.error('Email update verification error:', error);
                setErrors({ security_code: "Verification failed. Please try again." });
            } finally {
                setSubmitting(false);
            }
            return;
        }

        // Handle account security verification
        if (isAccountSecurity) {
            if (!securitySessionId) {
                setErrors({ security_code: "Session expired. Please refresh the page." });
                return;
            }

            try {
                const response = await post('/security-verification/verify-code', {
                    code: values.security_code,
                    session_id: securitySessionId,
                    next: nextUrl
                });


                if (response.success) {
                    let redirectUrl = response.redirect_url || nextUrl || "/account/overview";

                    console.log('Security verification successful!', {
                        backend_redirect_url: response.redirect_url,
                        frontend_next_url: nextUrl,
                        final_redirect_url: redirectUrl,
                        cookie_config: response.cookie_config
                    });

                    // If redirectUrl is a full URL, extract just the path to avoid port/domain issues
                    try {
                        if (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://')) {
                            const url = new URL(redirectUrl);
                            redirectUrl = url.pathname + url.search + url.hash;
                            console.log('Extracted path from full URL:', redirectUrl);
                        }
                    } catch (e) {
                        // If URL parsing fails, use the original redirectUrl
                        console.warn('Failed to parse redirect URL:', redirectUrl);
                    }

                    console.log('Verification successful! Final redirect URL:', redirectUrl);

                    // Backend should have set the security cookie, but set a fallback cookie
                    // to ensure the middleware recognizes the verification
                    const cookieConfig = response.cookie_config || {};
                    const cookieName = cookieConfig.name || 'security_verified';

                    // Set a simple fallback cookie that the middleware can recognize
                    // Use the session ID from the response as the cookie value
                    const cookieValue = securitySessionId || 'verified';
                    const expiresInMinutes = cookieConfig.expires_in_minutes || 10;
                    const expires = new Date(Date.now() + expiresInMinutes * 60 * 1000).toUTCString();

                    // Set the cookie with proper attributes
                    let cookieString = `${cookieName}=${cookieValue}`;
                    cookieString += `; expires=${expires}`;
                    cookieString += `; path=${cookieConfig.path || '/'}`;

                    if (cookieConfig.domain) {
                        cookieString += `; domain=${cookieConfig.domain}`;
                    }

                    if (cookieConfig.same_site) {
                        cookieString += `; SameSite=${cookieConfig.same_site}`;
                    }

                    // Note: Cannot set Secure or HttpOnly via JavaScript
                    document.cookie = cookieString;

                    console.log('Set fallback security cookie:', {
                        cookieName,
                        cookieValue,
                        cookieString
                    });

                    // Use router.replace with a longer delay to ensure cookie is properly set
                    // This gives time for the backend cookie to be processed by the browser
                    console.log('Redirecting to:', redirectUrl);

                    // Debug: Check if cookie was set after a short delay
                    setTimeout(() => {
                        const cookieName = response.cookie_config?.name || 'security_verified';
                        const cookieValue = getCookie(cookieName);
                        console.log('Cookie check before redirect:', {
                            cookieName,
                            cookieValue,
                            isValid: cookieValue ? isValidSecurityCookie(cookieValue) : false
                        });
                    }, 100);

                    setTimeout(() => {
                        console.log('Executing redirect to:', redirectUrl);

                        // Use window.location.href for production reliability
                        // router.replace() can be unreliable in production environments
                        window.location.href = redirectUrl;
                    }, 200); // Short delay to ensure cookie is set
                } else {
                    console.log('Verification failed:', response.message);
                    setErrors({ security_code: response.message || "Invalid verification code. Try again." });
                }
            } catch (error) {
                console.log('Verification error:', error);
                setErrors({ security_code: "Verification failed. Please try again." });
            }
            return;
        }

        // Original signup/reset password logic
        const savedData = JSON.parse(sessionStorage.getItem(dataType));

        if (!savedData || Date.now() > savedData.expiresAt) {
            setErrors({ security_code: "Token has expired. Please request a new one." });
            return;
        }

        const payload = {
            type: dataType,
            token: values.security_code,
            ...({ uuid: savedData.uuid })
        };

        const response = await verifyEmailToken(payload);

        if (response?.success) {
            const redirectTo = resetPassword
                ? "/change-password"
                : `/create-username${provider ? `?provider=${provider}` : ""}`;

            router.push(redirectTo);
        } else {
            setErrors({ security_code: response?.message || "Invalid verification code. Try again." });
        }
    };

    const handleResendClick = async (setErrors) => {
        if (isButtonDisabled) return;

        setIsRotating(true);
        startCooldown();

        // Handle account security verification resend
        if (isAccountSecurity) {
            if (!securitySessionId) {
                setErrors({ security_code: "Session expired. Please refresh the page." });
                setIsRotating(false);
                return;
            }

            try {
                const response = await post('/security-verification/resend-code', {
                    session_id: securitySessionId
                });

                if (response.success) {
                    setResendMessage(
                        "Verification code resent! Please check your email (including spam or junk folders) for the new code. If you don’t receive it within a few minutes, try resending or contact our support team for assistance."
                    );
                } else {
                    setErrors({ security_code: response.message || "Failed to resend code." });
                }
            } catch (error) {
                setErrors({ security_code: "Failed to resend code. Please try again." });
            } finally {
                setIsRotating(false);
            }
            return;
        }


        const existingData = JSON.parse(sessionStorage.getItem(dataType)) || {};
        const oldUuid = existingData.uuid || uuidv4();

        try {
            const payload = { type: dataType, uuid: oldUuid };
            const response = await resendVerificationCode(payload);

            if (response.status === 429) {
                router.push(resetPassword ? "/locate-account" : "/signup");
                return;
            }

            if (!response.success) {
                setErrors({ security_code: response.message || "Something went wrong." });
                return;
            }

            // ✅ Cache does not exist → resend success
            const expiresInMinutes = 15;
            const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;
            sessionStorage.setItem(dataType, JSON.stringify({ uuid: oldUuid, expiresAt }));

            setResendMessage(
                "Verification code resent! Please check your email (including spam or junk folders) for the new code. If you don’t receive it within a few minutes, try resending or contact our support team for assistance."
            );
        } catch (error) {
            console.error(error);
            setErrors({ security_code: "Something went wrong. Please try again." });
        } finally {
            setIsRotating(false);
        }
    };

    // Restore code handlers
    const handleUseRestoreCode = () => {
        setShowUseRestoreCodeModal(true);
    };

    const handleRestoreCodeSubmit = async (restoreCode) => {
        if (!securitySessionId) {
            toast.error("Session expired. Please refresh the page.");
            return;
        }

        setIsVerifyingRestoreCode(true);
        try {
            const response = await verifyRestoreCode(restoreCode, securitySessionId);

            if (response.success) {
                // Close the restore code modal
                setShowUseRestoreCodeModal(false);
                // Set the new restore code and show the new restore code modal
                setNewRestoreCode(response.data.new_restore_code);
                setShowNewRestoreCodeModal(true);
                // After showing the new restore code, proceed with the security verification
                // We'll handle the redirect after the user closes the new restore code modal
            } else {
                toast.error(response.message || "Invalid restore code. Please try again.");
            }
        } catch (error) {
            console.error('Restore code verification error:', error);
            toast.error("Verification failed. Please try again.");
        } finally {
            setIsVerifyingRestoreCode(false);
        }
    };

    const handleNewRestoreCodeClose = () => {
        setShowNewRestoreCodeModal(false);
        setNewRestoreCode("");

        // Complete the security verification flow by redirecting to the intended destination
        if (nextUrl) {
            let redirectUrl = nextUrl;
            try {
                if (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://')) {
                    const url = new URL(redirectUrl);
                    redirectUrl = url.pathname + url.search + url.hash;
                }
            } catch (e) {
                console.warn('Failed to parse redirect URL:', redirectUrl);
            }
            window.location.href = redirectUrl;
        } else {
            window.location.href = "/account/overview";
        }
    };

    const handleChange = (e, index) => {
        const value = e.target.value;
        if (/^[a-zA-Z0-9]$/.test(value)) {
            if (index < inputRefs.current.length - 1) {
                inputRefs.current[index + 1].focus();
            }
        } else {
            e.target.value = "";
        }
    };

    const handleKeyDown = (e, index) => {
        if (e.key === "Backspace" && !e.target.value && index > 0) {
            inputRefs.current[index - 1].focus();
        }
    };

    const metaArray = {
        noindex: true,
        title: "Security Check | Verify Your TradeReply Account",
        description: "Enter your authorization code to verify your identity and securely access your TradeReply account. Protect your trading data with an extra layer of security.",
        canonical_link: "https://www.tradereply.com/security-check",
        og_site_name: "TradeReply",
        og_title: "Security Check | Verify Your TradeReply Account",
        og_description: "Enter your authorization code to verify your identity and securely access your TradeReply account. Protect your trading data with an extra layer of security.",
        twitter_title: "Security Check | Verify Your TradeReply Account",
        twitter_description: "Enter your authorization code to verify your identity and securely access your TradeReply account. Protect your trading data with an extra layer of security.",
    };

    // Show proper loading while checking for existing security cookie
    if (isCheckingExistingCookie && isAccountSecurity) {
        return (
            <AuthLayout>
                <MetaHead props={metaArray} />
                <div className="loginCommon_rightSide security_check">
                    <div className="loginCommon_rightSide_inner">
                        <div className="loginCommon_rightSide_formBox">
                            <AuthLogo />
                            <div className="loginHeading">
                                <h1>Security Check</h1>
                            </div>
                            <div className="text-center py-5">
                                <div className="security-check-loading">
                                    <div className="loading-spinner mb-3">
                                        <div className="spinner-border text-primary" role="status">
                                            <span className="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <h5 className="mb-2">Verifying Security Check</h5>
                                    <p className="text-white">Please wait while we verify your security status...</p>
                                </div>
                            </div>
                        </div>
                        <div className="mt-4 mt-md-5">
                            <LoginFooter />
                        </div>
                    </div>
                </div>
                <style jsx>{`
                    .security-check-loading {
                        padding: 2rem 0;
                    }
                    .loading-spinner {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    .spinner-border {
                        width: 3rem;
                        height: 3rem;
                        border-width: 0.3em;
                    }
                    .text-primary {
                        color: #007bff !important;
                    }
                    .text-muted {
                        color: #6c757d !important;
                        font-size: 0.9rem;
                    }
                    .visually-hidden {
                        position: absolute !important;
                        width: 1px !important;
                        height: 1px !important;
                        padding: 0 !important;
                        margin: -1px !important;
                        overflow: hidden !important;
                        clip: rect(0, 0, 0, 0) !important;
                        white-space: nowrap !important;
                        border: 0 !important;
                    }
                `}</style>
            </AuthLayout>
        );
    }

    // Show professional loading screen for signup/reset password flows
    if (isSessionValid === null) {
        return (
            <AuthLayout>
                <MetaHead props={metaArray} />
                <div className="loginCommon_rightSide security_check">
                    <div className="loginCommon_rightSide_inner">
                        <div className="loginCommon_rightSide_formBox">
                            <AuthLogo />
                            <div className="loginHeading">
                                <h1>Security Check</h1>
                            </div>
                            <div className="text-center py-5">
                                <div className="security-check-loading">
                                    <div className="loading-spinner mb-3">
                                        <div className="spinner-border text-primary" role="status">
                                            <span className="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <h5 className="mb-2">Verifying Your Information</h5>
                                    <p className="text-white">
                                        {resetPassword
                                            ? "Please wait while we verify your account details..."
                                            : "Please wait while we verify your signup details..."
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="mt-4 mt-md-5">
                            <LoginFooter />
                        </div>
                    </div>
                </div>
                <style jsx>{`
                    .security-check-loading {
                        padding: 2rem 0;
                    }
                    .loading-spinner {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    .spinner-border {
                        width: 3rem;
                        height: 3rem;
                        border-width: 0.3em;
                    }
                    .text-primary {
                        color: #007bff !important;
                    }
                    .text-muted {
                        color: #6c757d !important;
                        font-size: 0.9rem;
                    }
                    .visually-hidden {
                        position: absolute !important;
                        width: 1px !important;
                        height: 1px !important;
                        padding: 0 !important;
                        margin: -1px !important;
                        overflow: hidden !important;
                        clip: rect(0, 0, 0, 0) !important;
                        white-space: nowrap !important;
                        border: 0 !important;
                    }
                `}</style>
            </AuthLayout>
        );
    }

    if (isSessionValid === false) {
        return null;
    }

    return (
        <AuthLayout>
            <MetaHead props={metaArray} />
            <div className="loginCommon_rightSide security_check">
                <div className="loginCommon_rightSide_inner">
                    <div className="loginCommon_rightSide_formBox">
                        <AuthLogo />
                        <div className="loginHeading">
                            <h1>Security Check</h1>
                        </div>
                        <div id="auth-code-label">
                            <div className="text-center pt-3">
                                <div className="text-center pt-3">
                                    {isEmailUpdate ? (
                                        <span className="font-semibold">
                                            We’ve sent a code to your new email address. Please enter it below to confirm you have access.
                                        </span>
                                    ) : isAccountSecurity ? (
                                        <span className="font-semibold">
                                            For your security, please enter the verification code sent to your email address to continue.
                                        </span>
                                    ) : dataType === "reset_password_data" ? (
                                        <>
                                            {type === "email" || !type ? (
                                                <span className="font-semibold">
                                                    {type === "email" || !type
                                                        ? "If an account with the entered email or username exists, a verification code has been sent."
                                                        : <>Please enter the verification code sent to <strong>{maskedEmail}</strong>.</>
                                                    }
                                                </span>
                                            ) : (
                                                <span className="font-semibold">
                                                    Please enter the verification code sent to the email associated with <strong>{maskedEmail}</strong>. If you don't receive an email, check your spam folder or contact support for assistance.
                                                </span>
                                            )}
                                        </>
                                    ) : (
                                        <span>Please enter the verification code sent to:</span>
                                    )}
                                </div>

                            </div>
                            <div className="text-center py-3 user_email">
                                <span>{maskedEmail}</span>
                            </div>
                        </div>
                        <div className="loginTabs">
                            <div className="loginForm">
                                <Formik
                                    initialValues={initialValues}
                                    validationSchema={securitySchema}
                                    onSubmit={handleSubmit}
                                >
                                    {({ isSubmitting }) => (
                                        <Form>
                                            <Field name="security_code">
                                                {({ field, form, meta }) => {
                                                    const handlePaste = (e) => {
                                                        e.preventDefault();
                                                        const pasteData = e.clipboardData.getData("Text");
                                                        const cleaned = pasteData.replace(/[^a-zA-Z0-9]/g, "");
                                                        const chars = cleaned.split("").slice(0, 6);
                                                        let newCode = new Array(6).fill("");
                                                        chars.forEach((char, i) => {
                                                            newCode[i] = char;
                                                            if (inputRefs.current[i]) {
                                                                inputRefs.current[i].value = char;
                                                            }
                                                        });
                                                        form.setFieldValue(field.name, newCode.join(""));
                                                        if (chars.length < inputRefs.current.length) {
                                                            inputRefs.current[chars.length].focus();
                                                        }
                                                    };

                                                    const handleResend = () => {
                                                        inputRefs.current.forEach((input) => {
                                                            if (input) input.value = "";
                                                        });

                                                        form.setFieldValue("security_code", "");
                                                        form.setFieldError("security_code", "");

                                                        handleResendClick({
                                                            security_code: () => {
                                                                form.setFieldError("security_code", "Failed to resend code.");
                                                            },
                                                        });

                                                        inputRefs.current[0]?.focus();
                                                    };

                                                    return (
                                                        <>
                                                            <div className="py-3 security_check_input">
                                                                <div role="group" aria-labelledby="auth-code-label">
                                                                    <div className="d-flex justify-content-between">
                                                                        {Array.from({ length: 6 }).map((_, index) => {
                                                                            const charValue = field.value ? field.value[index] || "" : "";
                                                                            return (
                                                                                <input
                                                                                    key={index}
                                                                                    type="text"
                                                                                    maxLength="1"
                                                                                    id={`auth-code-${index + 1}`}
                                                                                    inputMode="text"
                                                                                    aria-label={`${["First", "Second", "Third", "Fourth", "Fifth", "Sixth"][index]} character`}
                                                                                    value={charValue}
                                                                                    onChange={(e) => {
                                                                                        const val = e.target.value;
                                                                                        let newCode = field.value
                                                                                            ? field.value.split("")
                                                                                            : new Array(6).fill("");
                                                                                        newCode[index] = val;
                                                                                        form.setFieldValue(field.name, newCode.join(""));
                                                                                        handleChange(e, index);
                                                                                    }}
                                                                                    onKeyDown={(e) => handleKeyDown(e, index)}
                                                                                    onPaste={handlePaste}
                                                                                    ref={(el) => (inputRefs.current[index] = el)}
                                                                                    className={meta.touched && meta.error ? "error-field" : ""}
                                                                                />
                                                                            );
                                                                        })}
                                                                    </div>
                                                                    {meta.touched && meta.error ? (
                                                                        <InputError message={meta.error} />
                                                                    ) : null}
                                                                </div>
                                                            </div>

                                                            <div className="pb-3 d-flex justify-content-center">
                                                                <button
                                                                    type="button"
                                                                    className="security_check_resend_btn d-flex align-items-center gap-3"
                                                                    onClick={handleResend}
                                                                    disabled={isButtonDisabled}
                                                                >
                                                                    Resend Code
                                                                    <ResendCodeIcon isRotating={isRotating} />
                                                                    <span>{cooldown > 0 ? `${cooldown}s` : ""}</span>
                                                                </button>
                                                            </div>

                                                            {/* Use Restore Code Button - Only show for account security verification */}
                                                            {isAccountSecurity && (
                                                                <div className="pb-3 d-flex justify-content-center items-center gap-2 font-semibold">
                                                                    <span>Having trouble?</span>
                                                                    <button
                                                                        type="button"
                                                                        className="d-flex align-items-center gap-2 rounded-full"
                                                                        onClick={handleUseRestoreCode}
                                                                        style={{
                                                                            backgroundColor: '#2e5aac',
                                                                            border: 'none',
                                                                            color: 'white',
                                                                            fontSize: '14px',
                                                                            cursor: 'pointer',
                                                                            padding: '8px 24px',
                                                                            
                                                                            transition: 'background-color 0.2s ease'
                                                                        
                                                                        }}
                                                                        onMouseEnter={(e) => {
                                                                            e.target.style.backgroundColor = '#3e6bc1';
                                                                        }}
                                                                        onMouseLeave={(e) => {
                                                                            e.target.style.backgroundColor = '#2e5aac';
                                                                        }}
                                                                    >
                                                                        
                                                                        <span
                                                                            style={{
                                                                                
                                                                                fontWeight: '500'
                                                                            }}
                                                                        >
                                                                            Use Restore Code
                                                                        </span>
                                                                    </button>
                                                                </div>
                                                            )}
                                                        </>
                                                    );
                                                }}
                                            </Field>
                                            {resendMessage && (
                                                <div className="text-center text-success mt-3 mb-6 px-3">
                                                    <p>{resendMessage}</p>
                                                </div>
                                            )}
                                            <div className="w-100">
                                                <CommonButton
                                                    type="submit"
                                                    title={isSubmitting ? "Loading" : "Continue"}
                                                    fluid
                                                    disabled={isSubmitting}
                                                />
                                            </div>
                                            <Link href={isAccountSecurity ? "/account/details" : isEmailUpdate ? "/account/email/setup" :"/dashboard"} className="w-100 mt-3">
                                                <CommonButton title="Cancel" white20 />
                                            </Link>
                                            <div className="anAccount mt-3 d-flex justify-content-center">
                                            <a
                                              href="/help"
                                              target="_blank"
                                              rel="noopener"
                                              className="d-flex align-items-center gap-2"
                                            >
                                              <span className="font-bold">Contact Customer Support</span>
                                              <ContactCustomerSupport />
                                            </a>
                                            </div>
                                        </Form>
                                    )}
                                </Formik>
                            </div>
                        </div>
                    </div>
                    <div className="mt-4 mt-md-5">
                        <LoginFooter />
                    </div>
                </div>
            </div>

            {/* Restore Code Modals */}
            <UseRestoreCodeModal
                show={showUseRestoreCodeModal}
                handleClose={() => setShowUseRestoreCodeModal(false)}
                onSubmit={handleRestoreCodeSubmit}
                isSubmitting={isVerifyingRestoreCode}
            />

            <NewRestoreCodeModal
                show={showNewRestoreCodeModal}
                handleClose={handleNewRestoreCodeClose}
                restoreCode={newRestoreCode}
            />
        </AuthLayout>
    );
}
