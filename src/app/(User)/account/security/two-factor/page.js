import { Suspense } from "react";
import TwoFactorComponent from "./TwoFactorComponent";
import MetaHead from "@/Seo/Meta/MetaHead";

export default function TwoFactor() {
    const metaArray = {
        noindex: true,
        title: "Account Security | Two-Factor Setup | TradeReply",
        description:
            "Add an extra layer of security to your TradeReply account with Two-Factor Authentication (2FA). Enable or disable 2FA and manage trusted devices.",
        canonical_link: "https://www.tradereply.com/account/security/two-factor",
        og_site_name: "TradeReply",
        og_title: "Account Security | Two-Factor Setup | TradeReply",
        og_description:
            "Add an extra layer of security to your TradeReply account with Two-Factor Authentication (2FA). Enable or disable 2FA and manage trusted devices.",
        og_url: "https://www.tradereply.com/account/security/two-factor",
        og_type: "website",
        og_image:
            "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
        og_image_width: "1200",
        og_image_height: "630",
        og_locale: "en_US",
        twitter_card: "summary_large_image",
        twitter_title: "Account Security | Two-Factor Setup | TradeReply",
        twitter_description:
            "Add an extra layer of security to your TradeReply account with Two-Factor Authentication (2FA). Enable or disable 2FA and manage trusted devices.",
        twitter_image:
            "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
        twitter_site: "@JoinTradeReply",
        robots: "noindex, nofollow",
    };

    return (
        <Suspense fallback={<div>Loading...</div>}>
            <MetaHead props={metaArray} />
            <TwoFactorComponent />
        </Suspense>
    );
}
