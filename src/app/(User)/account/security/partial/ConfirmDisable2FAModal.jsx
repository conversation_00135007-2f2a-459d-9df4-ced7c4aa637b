'use client';
import { Modal, But<PERSON> } from 'react-bootstrap';
import React from 'react';
import '@/css/account/Security.scss';


const ConfirmDisable2FAModal = ({ show, handleConfirm, handleCancel }) => {
  return (
    <Modal
      show={show}
      onHide={handleCancel}
      centered
      contentClassName="custom-modal-content p-4 rounded-[15px]"
    >
      <div>
        <h5 className="text-[24px] font-bold text-white mb-3">
          Disable Two-Factor Authentication?
        </h5>

        <p className="text-white text-[18px]">
          Are you sure you want to disable two-factor authentication?
          <br />
          This may reduce your account's security.
        </p>

        <div className="mt-4 confirm-modal-btn d-flex justify-content-end gap-3">
          <Button
            className="bg-secondary text-white font-semibold rounded-md px-4 "
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            className="bg-danger text-white font-semibold rounded-md px-4"
            onClick={handleConfirm}
          >
            Yes, Disable
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmDisable2FAModal;
