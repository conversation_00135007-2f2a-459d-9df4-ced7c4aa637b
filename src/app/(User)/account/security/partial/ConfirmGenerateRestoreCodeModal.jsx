'use client';
import { Mo<PERSON>, But<PERSON> } from 'react-bootstrap';
import React from 'react';
import '@/css/account/Security.scss';

const ConfirmGenerateRestoreCodeModal = ({ show, handleConfirm, handleCancel }) => {
  return (
    <Modal
      show={show}
      onHide={handleCancel}
      centered
      contentClassName="custom-modal-content p-4 rounded-[15px]"
    >
      <div>
        <h5 className="text-[24px] font-bold text-white mb-3">
          Generate New Restoral Code?
        </h5>

        <p className="text-white text-[18px]">
          This will permanently overwrite your existing restoral code. You'll need to store the new one somewhere safe.
        </p>

        <div className="d-flex gap-3 mt-4">
          <Button
            onClick={handleCancel}
            className="!bg-[#B4B4B4] text-black font-semibold !rounded-md flex-1"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            className="bg-[#00b7ff] text-white font-semibold !rounded-md flex-1"
          >
            Generate New Code
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmGenerateRestoreCodeModal;
