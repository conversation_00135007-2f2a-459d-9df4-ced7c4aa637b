'use client';
import { Col, Row } from "react-bootstrap";
import React from 'react'
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { RightArrowIconSvg } from "@/assets/svgIcons/SvgIcon";
import CommonTable from "@/Components/UI/CommonTable";

export default function RecentPurchases() {
    const recentdata = [
        {
            date: "Apr 30, 2024",
            plan: "Essential Plan - 1 month",
            price: "$ 14.99",
        },
        {
            date: "Apr 30, 2024",
            plan: "Essential Plan - 1 month",
            price: "$ 14.99",
        },
        {
            date: "Apr 30, 2024",
            plan: "Essential Plan - 1 month",
            price: "$ 14.99",
        },
    ];
    return (
        <>
            <Col xs={12}>
                <CommonBlackCard
                    link="/account/transactions"
                    title="Recent Purchases"
                    Linktext="Transaction History"
                    Linkicon={<RightArrowIconSvg />}
                    className="account_card account_card_table"
                >
                    <div className="account_card_table">
                        <CommonTable fields={false} className="simple_table">
                            {recentdata?.map((item, index) => (
                                <tr key={index}>
                                    <td>{item?.date}</td>
                                    <td className="blue_text">{item?.plan}</td>
                                    <td>{item?.price}</td>
                                    <td>Complete</td>
                                </tr>
                            ))}
                        </CommonTable>
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    )
}
