'use client';
import { Col, Row } from "react-bootstrap";
import React from 'react'
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { RightArrowIconSvg } from "@/assets/svgIcons/SvgIcon";

export default function Subscriptions() {
    return (
        <>
            <Col xs={12} className="mb-3 mb-lg-4">
                <CommonBlackCard
                    link="/account/subscriptions"
                    title="Subscriptions"
                    Linktext="Subscriptions"
                    Linkicon={<RightArrowIconSvg />}
                    className="account_card"
                >
                    <div className="account_card_subscription">
                        <ul className="account_card_subscription_list">
                            <li>
                                <h6>
                                    Trade<span className="blue_text">Reply</span>
                                </h6>
                            </li>
                            <li>
                                <h6>
                                    <span className="blue_text">Essential Plan</span>
                                </h6>
                            </li>
                            <li>
                                <h6 className="yellow_text">SUSPENDED</h6>
                            </li>
                            <li>
                                <h6>MAY 3 , 2024</h6>
                            </li>
                            <li className="d-none d-md-block"></li>
                            <li className="d-none d-md-block"></li>
                            <li>
                                <p>Account Status</p>
                            </li>
                            <li>
                                <p>Renewal</p>
                            </li>
                        </ul>
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    )
}
