'use client';
import { Col } from "react-bootstrap";
import React, { useState, useEffect } from 'react';
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";

export default function RedeemCode() {
    const [promoCode, setPromoCode] = useState('');
    const [status, setStatus] = useState('idle');
    const [appliedCode, setAppliedCode] = useState('');

    const VALID_CODE = "ABC123";

    const handleRedeem = () => {
        const code = promoCode.trim().toUpperCase();

        if (code === VALID_CODE) {
            setStatus('success');
            setAppliedCode(code);

            setTimeout(() => {
                setStatus('idle');
                setAppliedCode('');
            }, 3000);
        } else {
            setStatus('error');
            setAppliedCode(code);

            setTimeout(() => {
                setStatus('idle');
                setAppliedCode('');
            }, 3000);
        }

        setPromoCode('');
    };

    return (
        <Col md={6} xs={12} className="mb-3 mb-lg-4">
            <CommonBlackCard title="Redeem a code" className="account_card">
                <div className="account_card_redeem">
                    <div className="d-flex align-items-center">
                        <input
                            type="text"
                            className="form-control text-white"
                            placeholder="Enter code"
                            value={promoCode}
                            onChange={(e) => setPromoCode(e.target.value)}
                        />
                        <button className="btn-style" type="button" onClick={handleRedeem}>
                            Redeem Code
                        </button>
                    </div>

                    <div className="mt-2 error-messages">
                        {status === 'error' && (
                            <p className="invalid">
                                Promo code <strong>{appliedCode || 'Enter code'}</strong> is invalid.
                            </p>
                        )}
                        {status === 'success' && (
                            <p className="success">
                                Promo code <strong>{appliedCode}</strong> has been applied to your account!
                            </p>
                        )}
                    </div>
                </div>
            </CommonBlackCard>
        </Col>
    );
}
