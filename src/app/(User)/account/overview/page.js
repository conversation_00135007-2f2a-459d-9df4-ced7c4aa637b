'use client';
import { Col, Row } from "react-bootstrap";
import React from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import MetaHead from "@/Seo/Meta/MetaHead";
import YourInformation from "./Partial/YourInformation";
import SecurityCheckup from "./Partial/SecurityCheckup";
import TradeReplyCredits from "./Partial/TradeReplyCredits";
import RedeemCode from "./Partial/RedeemCode";
import Subscriptions from "./Partial/Subscriptions";
import RecentPurchases from "./Partial/RecentPurchases";

export default function AcoountOverview() {
  const recentdata = [
    {
      date: "Apr 30, 2024",
      plan: "Essential Plan - 1 month",
      price: "$ 14.99",
    },
    {
      date: "Apr 30, 2024",
      plan: "Essential Plan - 1 month",
      price: "$ 14.99",
    },
    {
      date: "Apr 30, 2024",
      plan: "Essential Plan - 1 month",
      price: "$ 14.99",
    },
  ];

  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description: "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description: "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description: "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Account Overview" />
          <Row>
            <YourInformation />
            <SecurityCheckup />
            <TradeReplyCredits />
            <RedeemCode />
            <Subscriptions />
            <RecentPurchases />
          </Row>
        </div>
      </AccountLayout>
    </>
  )
}
