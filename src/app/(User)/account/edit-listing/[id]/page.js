"use client";
import React, { useState, useEffect, use } from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import {
  SolidInfoIcon,
  LightEyeIcon,
  LicenseIcon,
  DigitaLAssetIcon,
} from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import Form from "react-bootstrap/Form";
import CommonButton from "@/Components/UI/CommonButton";
import ImageUploader from "@/app/(User)/account/create-listing/Partials/ImageUploader";

export default function editListing({ params }) {
  const { id } = use(params);
  console.log(id);

  const [fileName, setFileName] = useState("File Upload");

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
    }
  };

  const getTruncatedFileName = (name, maxLength = 25) => {
    return name.length > maxLength
      ? name.slice(0, maxLength - 3) + "..."
      : name;
  };

  const getFile = (file) => {
    console.log(file);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    console.log(e, "ah");
  };

  const title =
    !id ? "Create New Listing" : "Edit Listing";

  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title={title} />
          <div className="account_card_create_listing">
            <div className="form-wrapper">
              <form
                onSubmit={(e) => {
                  handleSubmit(e);
                }}
              >
                <ImageUploader getFile={getFile} />
                <input
                  type="text"
                  required
                  placeholder="Title"
                  className="form-input"
                />
                <input
                  required
                  type="number"
                  placeholder="Price"
                  className="form-input"
                />
                <div className="checkbox-wrapper">
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id="selectCheck"
                    style={{ pointerEvents: "auto" }}
                  />
                  <label
                    className="name custom_checkbox_label ps-1"
                    htmlFor="selectCheck"
                  >
                    Stock Level Applies
                  </label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Manual entry lets you input."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <input
                  type="number"
                  placeholder="Enter available stock (total items on hand)"
                  className="form-input"
                />
                <Form.Select className="form-select">
                  <option selected>Category</option>
                  <option>Full refund</option>
                  <option>Partial refund</option>
                  <option>Replacement file or fixed version</option>
                </Form.Select>
                <div className="relative">
                  <textarea
                    className="form-textarea"
                    rows="4"
                    placeholder="Description"
                    maxLength={2000}
                  ></textarea>
                  <div className="character-count">
                    Characters Left: 2000/2000
                  </div>
                </div>
                <div className="checkbox-wrapper">
                  <DigitaLAssetIcon />
                  <label>Digital Asset (Upload or Link)</label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Manual entry lets you input."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <div className="checkbox-wrapper">
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id="selectCheck"
                    style={{ pointerEvents: "auto" }}
                  />
                  <label
                    className="name custom_checkbox_label ps-1"
                    htmlFor="selectCheck"
                  >
                    Upload Digital File
                  </label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Manual entry lets you input."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <label className="file-upload-wrapper">
                  {getTruncatedFileName(fileName)}
                  <input type="file" name="file" onChange={handleFileChange} />
                </label>
                <div className="checkbox-wrapper">
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id="selectCheck"
                    style={{ pointerEvents: "auto" }}
                  />
                  <label
                    className="name custom_checkbox_label ps-1"
                    htmlFor="selectCheck"
                  >
                    External Download Link
                  </label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Manual entry lets you input."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <input
                  type="text"
                  placeholder="Enter URL to digital content (.e.g private video or file link) "
                  className="form-input"
                />
                <div className="checkbox-wrapper">
                  <LightEyeIcon />
                  <label>Tags</label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Manual entry lets you input."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <input
                  type="text"
                  placeholder="Add tags to help users find your product "
                  className="form-input"
                />
                <div className="checkbox-wrapper">
                  <LicenseIcon />
                  <label>License/Usage Terms</label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Manual entry lets you input."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <div className="relative">
                  <textarea
                    className="form-textarea"
                    rows="4"
                    placeholder="Describe any usage or redistribution restrictions "
                    maxLength={500}
                  ></textarea>
                  <div className="character-count">
                    Characters Left: 500/500
                  </div>
                </div>
                <CommonButton
                  title="Publish"
                  type="submit"
                  className="view_res_btn w-100"
                />
              </form>
            </div>
          </div>
        </div>
      </AccountLayout>
    </>
  );
}
