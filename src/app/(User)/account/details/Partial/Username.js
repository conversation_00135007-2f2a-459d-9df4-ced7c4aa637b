'use client';
import React, { useState, useEffect } from 'react';
import { Col } from 'react-bootstrap';
import { EditIconSvg, SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';
import Link from 'next/link';
import CommonTooltip from '@/Components/UI/CommonTooltip';
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';
import { get } from '@/utils/apiUtils';

export default function Username() {
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const reduxUser = useSelector((state) => state?.auth?.user || null);
    const dispatch = useDispatch();

    // Fetch user data from API
    const fetchUserData = async () => {
        try {
            setLoading(true);
            setError(null);

            const controller = new AbortController();
            const response = await get('/account', {}, { signal: controller.signal });

            if (response.success && response.data) {
                setUserData(response.data);
                // Update Redux store with fresh user data
                dispatch(setUser(response.data));
                // Also update localStorage to ensure consistency
                localStorage.setItem('user', JSON.stringify(response.data));
            } else {
                throw new Error(response.message || 'Failed to fetch user data');
            }
        } catch (err) {
            console.error('Error fetching user data:', err);
            setError(err.message || 'Failed to load user information');

            // Fallback to Redux user data if API fails
            if (reduxUser) {
                setUserData(reduxUser);
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchUserData();
    }, []);

    const getDisplayUsername = () => {
        if (!userData) return 'Loading...';
        if (!userData.username) return 'Not set';

        // Display username as static text
        return userData.username;
    };

    const getChangesRemaining = () => {
        if (!userData) return 2;
        const changesUsed = userData.username_change_count || 0;
        return Math.max(0, 2 - changesUsed);
    };

    const hasChangesRemaining = () => {
        return getChangesRemaining() > 0;
    };

    const getDescriptionText = () => {
        const remaining = getChangesRemaining();
        if (remaining === 2) {
            return "Your public username may be changed two times.";
        } else if (remaining === 1) {
            return "Your public username may be changed one more time.";
        } else {
            return "You have used all available username changes.";
        }
    };

    return (
        <>
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Username</h6>
                            <p>{getDescriptionText()}</p>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            {hasChangesRemaining() ? (
                                <Link href={`/account/username/setup?from=${encodeURIComponent('/account/details')}`} prefetch={true}>
                                    <button className="d-flex align-items-center">
                                        <EditIconSvg />
                                        <span className="ms-2">Update</span>
                                    </button>
                                </Link>
                            ) : (
                                <CommonTooltip
                                    className="CustomTooltip"
                                    content={
                                        <>
                                            <p>Username changes are limited to 2 per account to protect identity and prevent abuse. You've reached that limit.</p>
                                            <p>
                                                If you need another change, you can request one through our <Link href="/help" prefetch={true}>support team.</Link>
                                            </p>
                                        </>
                                    }
                                    position="top-right"
                                >
                                    <SolidInfoIcon />
                                </CommonTooltip>
                            )}
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            {loading ? (
                                <div className="text-center py-3">
                                    <span>Loading username information...</span>
                                </div>
                            ) : error ? (
                                <div className="text-center py-3">
                                    <span className="text-danger">Failed to load username information</span>
                                    <br />
                                    <button
                                        className="btn btn-sm btn-link"
                                        onClick={fetchUserData}
                                        style={{ color: '#007bff', textDecoration: 'underline' }}
                                    >
                                        Retry
                                    </button>
                                </div>
                            ) : (
                                <ul>
                                    <li>
                                        <Col xs={12} md={3}>
                                            <span>Username </span>
                                        </Col>
                                        <Col xs={12} md={9}>
                                            <span>{getDisplayUsername()}</span>
                                        </Col>
                                    </li>
                                </ul>
                            )}
                        </div>
                    </div>
                </div>
            </Col >
        </>
    )
}
