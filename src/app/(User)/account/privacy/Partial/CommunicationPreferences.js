'use client';
import { Col } from "react-bootstrap";
import React, { useState } from 'react';
import { CheckIcon, EditIconSvg, GreyCrossIcon } from "@/assets/svgIcons/SvgIcon";
import Link from "next/link";

export default function CommunicationPreferences() {
    const [isEditing, setIsEditing] = useState(false);
    const [phone, setPhone] = useState(false);
    const hasPhoneNumber = !!phone;
    const [smsEnabled, setSmsEnabled] = useState(false);
    const [tempSmsEnabled, setTempSmsEnabled] = useState(false);
    const [emailEnabled, setEmailEnabled] = useState(true);
    const [tempEmailEnabled, setTempEmailEnabled] = useState(true);
    const [newsEnabled, setNewsEnabled] = useState(true);
    const [tempNewsEnabled, setTempNewsEnabled] = useState(true);

    const handleSave = () => {
        setEmailEnabled(tempEmailEnabled);
        setNewsEnabled(tempNewsEnabled);
        setSmsEnabled(hasPhoneNumber ? tempSmsEnabled : false);
        setIsEditing(false);
    };

    const handleCancel = () => {
        setTempEmailEnabled(emailEnabled);
        setTempNewsEnabled(newsEnabled);
        setTempSmsEnabled(smsEnabled);
        setIsEditing(false);
    }

    const maskPhone = (num) => {
        if (!num || num.length < 2) return '';
        return '*'.repeat(num.length - 2) + num.slice(-2);
    };

    return (
        <Col lg={12} xs={12} className="mb-3 mb-lg-4">
            <div className="common_blackcard account_card">
                <div className="common_blackcard_innerheader">
                    <div className="common_blackcard_innerheader_content">
                        <h6>Communication Preferences</h6>
                    </div>
                    {!isEditing && (
                        <div className="common_blackcard_innerheader_icon">
                            <button onClick={() => setIsEditing(true)} className="d-flex align-items-center gap-2">
                                <EditIconSvg />
                                Update
                            </button>
                        </div>
                    )}
                </div>

                <div className="common_blackcard_innerbody">
                    <div className="account_card_list">

                        {/* Email Notifications */}
                        <div className="d-flex align-items-center">
                            <Col xs={11}>
                                <div className="step_verification_content">
                                    <h6>Email Notifications</h6>
                                    <p className="mt-1">
                                        Receive email updates related to your account activity, like trade reminders, platform tips, and product updates.
                                    </p>
                                </div>
                            </Col>
                            <Col xs={1}>
                                <div className="d-flex align-items-center gap-2 justify-content-end">
                                    {isEditing ? (
                                        <label className="switch">
                                            <input
                                                type="checkbox"
                                                checked={tempEmailEnabled}
                                                onChange={(e) => setTempEmailEnabled(e.target.checked)}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                    ) : (
                                        <>
                                            {emailEnabled ? <CheckIcon /> : <GreyCrossIcon />}
                                            <span className={emailEnabled ? 'green_text' : 'gray_text'}>
                                                {emailEnabled ? 'Enabled' : 'Disabled'}
                                            </span>
                                        </>
                                    )}
                                </div>
                            </Col>
                        </div>

                        {/* SMS Section */}
                        <div className="d-flex align-items-center mt-3 border-bottom pb-3">
                            <Col xs={11}>
                                <div className="step_verification_content">
                                    <h6>SMS Product Updates & Offers</h6>
                                    <p className="mt-1">
                                        Receive text messages with feature announcements, product updates, and occasional offers from TradeReply. By enabling this, you agree to receive recurring marketing texts at the number provided. Consent is not a condition of purchase. Message and data rates may apply. Text STOP to opt out, or HELP for help.
                                    </p>

                                    <div className="sms-update mt-2">
                                        {!hasPhoneNumber ? (
                                            isEditing ? (
                                                <>
                                                    <h6>Add a phone number first to enable SMS Updates</h6>
                                                    <Link href="/security-check" prefetch={true}>
                                                        <button className="btn-style">
                                                            Add Phone Number
                                                        </button>
                                                    </Link>
                                                </>
                                            ) : (
                                                <Link href="/security-check" prefetch={true}>
                                                    <p className="add-number">Add a phone number first to enable SMS updates</p>
                                                </Link>
                                            )
                                        ) : (
                                            <>
                                                <p>Phone Number for SMS: {maskPhone(phone)}</p>
                                                {isEditing && (
                                                    <button className="btn-style">
                                                        Update Phone Number
                                                    </button>
                                                )}
                                            </>
                                        )}

                                    </div>
                                </div>
                            </Col>
                            <Col xs={1}>
                                <div className="d-flex align-items-center gap-2 justify-content-end">
                                    {isEditing ? (
                                        <label className="switch">
                                            <input
                                                type="checkbox"
                                                checked={tempSmsEnabled}
                                                disabled={!hasPhoneNumber}
                                                onChange={(e) => setTempSmsEnabled(e.target.checked)}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                    ) : (
                                        <>
                                            {smsEnabled ? <CheckIcon /> : <GreyCrossIcon />}
                                            <span className={smsEnabled ? 'green_text' : 'gray_text'}>
                                                {smsEnabled ? 'Enabled' : 'Disabled'}
                                            </span>
                                        </>
                                    )}
                                </div>
                            </Col>
                        </div>

                        {/* Latest News */}
                        <div className="d-flex align-items-center mt-3">
                            <Col xs={11}>
                                <div className="step_verification_content">
                                    <h6>Latest News, Special Offers, Betas, and More</h6>
                                    <p className="mt-1">
                                        Get emails with early feature previews, beta invites, product announcements, and exclusive offers from TradeReply.
                                    </p>
                                </div>
                            </Col>
                            <Col xs={1}>
                                <div className="d-flex align-items-center gap-2 justify-content-end">
                                    {isEditing ? (
                                        <label className="switch">
                                            <input
                                                type="checkbox"
                                                checked={tempNewsEnabled}
                                                onChange={(e) => setTempNewsEnabled(e.target.checked)}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                    ) : (
                                        <>
                                            {newsEnabled ? <CheckIcon /> : <GreyCrossIcon />}
                                            <span className={newsEnabled ? 'green_text' : 'gray_text'}>
                                                {newsEnabled ? 'Enabled' : 'Disabled'}
                                            </span>
                                        </>
                                    )}
                                </div>
                            </Col>
                        </div>

                        {isEditing && (
                            <div className="account_card_list_btns mt-3">
                                <button className="btn-style white-btn" onClick={handleCancel}>
                                    Cancel
                                </button>
                                <button className="btn-style" onClick={handleSave}>
                                    Save
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Col>
    );
}
