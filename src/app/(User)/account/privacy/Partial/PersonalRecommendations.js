'use client';
import { Col } from "react-bootstrap";
import React, { useState } from 'react';
import { CheckIcon, EditIconSvg, GreyCrossIcon } from "@/assets/svgIcons/SvgIcon";

export default function PersonalRecommendations() {
    const [isEditing, setIsEditing] = useState(false);
    const [isEnabled, setIsEnabled] = useState(true);
    const [tempEnabled, setTempEnabled] = useState(true);

    const updateRecommendation = () => {
        setIsEditing(true);
        setTempEnabled(isEnabled);
    };

    const cancelEditing = () => {
        setIsEditing(false);
        setTempEnabled(isEnabled);
    };

    const saveEditing = () => {
        setIsEnabled(tempEnabled);
        setIsEditing(false);
    };

    return (
        <Col lg={12} xs={12} className="mb-3 mb-lg-4">
            <div className="common_blackcard account_card">
                <div className="common_blackcard_innerheader">
                    <div className="common_blackcard_innerheader_content">
                        <h6>Personal Recommendations</h6>
                    </div>
                    {!isEditing && (
                        <div className="common_blackcard_innerheader_icon">
                            <button className="d-flex align-items-center gap-2" onClick={updateRecommendation}>
                                <EditIconSvg />
                                Update
                            </button>
                        </div>
                    )}
                </div>
                <div className="common_blackcard_innerbody">
                    <div className="account_card_list">
                        <div className="d-flex align-items-center">
                            <Col xs={11}>
                                <div className="step_verification_content">
                                    <h6>TradeReply Product Recommendations</h6>
                                    <p className="mt-1">
                                        Allow tailored products and offers recommended to you. Recommendations are based on your trade performance, strategy behavior, and platform usage.
                                    </p>
                                </div>
                            </Col>
                            <Col xs={1}>
                                <div className="d-flex align-items-center gap-2 justify-content-end">
                                    {isEditing ? (
                                        <label className="switch">
                                            <input
                                                type="checkbox"
                                                checked={tempEnabled}
                                                onChange={(e) => setTempEnabled(e.target.checked)}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                    ) : (
                                        <>
                                            {isEnabled ? <CheckIcon /> : <GreyCrossIcon />}
                                            <span className={isEnabled ? 'green_text' : 'gray_text'}>
                                                {isEnabled ? 'Enabled' : 'Disabled'}
                                            </span>
                                        </>
                                    )}
                                </div>
                            </Col>
                        </div>

                        {isEditing && (
                            <div className="account_card_list_btns mt-3">
                                <button className="btn-style white-btn" onClick={cancelEditing}>
                                    Cancel
                                </button>
                                <button className="btn-style" onClick={saveEditing}>
                                    Save
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Col>
    );
}
