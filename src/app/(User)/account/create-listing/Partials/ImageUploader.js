import React, { useState, useRef } from 'react';
import { AddBlueIcon } from "@/assets/svgIcons/SvgIcon";
export default function ImageUploader({ getFile }) {
  const [image, setImage] = useState(null);
  const inputRef = useRef();

  const handleDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    handleFile(file);
  };

  const handleFile = (file) => {
    if (!file.type.startsWith('image/')) return;
    const reader = new FileReader();
    reader.onload = () => setImage(reader.result);
    reader.readAsDataURL(file);
    getFile(file)
  };

  const handleChange = (e) => {
    const file = e.target.files[0];
    handleFile(file);
  };

  const handleClick = () => {
    inputRef.current.click();
  };

  const removeImage = () => {
    setImage(null);
    inputRef.current.value = null;
  };

  return (
    <div className="upload-container">
      <p className="image-count">{image ? '1/1 Image' : '0/1 Images'}</p>

      <div
        className="upload-box"
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
        onClick={handleClick}
      >
        {image ? (
          <div className="preview">
            <img src={image} alt="Preview" />
            <button className="remove-btn" onClick={(e) => { e.stopPropagation(); removeImage() }}>✕</button>
          </div>
        ) : (
          <div className="upload-placeholder">
            <AddBlueIcon />
            <p>Add Photo</p>
            <p className="sub-text">or drag and drop</p>
          </div>
        )}
        <input
          type="file"
          accept="image/*"
          ref={inputRef}
          onChange={handleChange}
          hidden
        />
      </div>
    </div>
  );
}
