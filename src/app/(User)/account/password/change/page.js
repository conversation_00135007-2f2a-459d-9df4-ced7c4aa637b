'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import "@/css/account/AccountDetails.scss";

export default function ChangePassword() {
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');

    const metaArray = {
        noindex: true,
        title: "Change Password | TradeReply",
        description: "Change your account password on TradeReply.com for enhanced security.",
        canonical_link: "https://www.tradereply.com/account/password/change",
        og_site_name: "TradeReply",
        og_title: "Change Password | TradeReply",
        og_description: "Change your account password on TradeReply.com for enhanced security.",
        twitter_title: "Change Password | TradeReply",
        twitter_description: "Change your account password on TradeReply.com for enhanced security.",
    };

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_change_password">
                    <SidebarHeading title="Change Password" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <h6>Password</h6>
                                    <p>For your security, we highly recommend choosing a unique password that is not used elsewhere.</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <div className="col-lg-5 col-md-8 col-12 mb-3">
                                        <label className="form-label">Current Password</label>
                                        <TextInput
                                            type="password"
                                            placeholder="Enter your current password"
                                            value={currentPassword}
                                            onChange={(e) => setCurrentPassword(e.target.value)}
                                        />
                                    </div>
                                    <div className="col-lg-5 col-md-8 col-12 mb-3">
                                        <label className="form-label">New Password</label>
                                        <TextInput
                                            type="password"
                                            placeholder="Enter your new password"
                                            value={newPassword}
                                            onChange={(e) => setNewPassword(e.target.value)}
                                        />
                                    </div>
                                    <div className="col-lg-5 col-md-8 col-12 mb-3">
                                        <label className="form-label">Confirm New Password</label>
                                        <TextInput
                                            type="password"
                                            placeholder="Confirm your new password"
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button className="btn-style white-btn">
                                Cancel
                            </button>
                            <button className="btn-style">
                                Change Password
                            </button>
                        </div>
                    </div>
                </div>
            </AccountLayout>
        </>
    )
}
