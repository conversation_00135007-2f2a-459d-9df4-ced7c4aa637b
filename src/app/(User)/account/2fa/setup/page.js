'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import "@/css/account/AccountDetails.scss";

export default function Setup2FA() {
    const [isEnabled, setIsEnabled] = useState(false);

    const metaArray = {
        noindex: true,
        title: "Two-Factor Authentication Setup | TradeReply",
        description: "Set up two-factor authentication for enhanced account security on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/2fa/setup",
        og_site_name: "TradeReply",
        og_title: "Two-Factor Authentication Setup | TradeReply",
        og_description: "Set up two-factor authentication for enhanced account security on TradeReply.com.",
        twitter_title: "Two-Factor Authentication Setup | TradeReply",
        twitter_description: "Set up two-factor authentication for enhanced account security on TradeReply.com.",
    };

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_2fa">
                    <SidebarHeading title="Two-Factor Authentication" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <h6>Two-Factor Authentication (2FA)</h6>
                                    <p>Add an extra layer of security to your account by enabling two-factor authentication. You'll need to enter a code from your authenticator app each time you sign in.</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <div className="col-12">
                                        <div className="d-flex align-items-center justify-content-between">
                                            <div>
                                                <h6>Status: {isEnabled ? 'Enabled' : 'Disabled'}</h6>
                                                <p className="mb-0">
                                                    {isEnabled 
                                                        ? 'Two-factor authentication is currently enabled for your account.'
                                                        : 'Two-factor authentication is not enabled. Enable it now for better security.'
                                                    }
                                                </p>
                                            </div>
                                            <div>
                                                <button 
                                                    className={`btn-style ${isEnabled ? 'white-btn' : ''}`}
                                                    onClick={() => setIsEnabled(!isEnabled)}
                                                >
                                                    {isEnabled ? 'Disable 2FA' : 'Enable 2FA'}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button className="btn-style white-btn">
                                Cancel
                            </button>
                            <button className="btn-style">
                                Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            </AccountLayout>
        </>
    )
}
