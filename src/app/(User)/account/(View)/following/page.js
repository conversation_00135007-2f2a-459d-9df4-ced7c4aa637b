"use client";
import React from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import FollowingList from "@/Components/common/Account/PublicProfile/FollowingList";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";

export default function Following() {
  const metaArray = {
    noindex: true,
    title: "You’re Following | TradeReply",
    description:
      "See the traders and sellers you follow on TradeReply. Stay updated on their latest product listings and activity.",
    canonical_link: "https://www.tradereply.com/account/following",
    og_site_name: "TradeReply",
    og_title: "You’re Following | TradeReply",
    og_description:
      "See the traders and sellers you follow on TradeReply. Stay updated on their latest product listings and activity.",
    og_url: "https://www.tradereply.com/account/following",
    og_type: "website",
    og_image:
      "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    og_image_width: "1200",
    og_image_height: "630",
    og_locale: "en_US",
    twitter_card: "summary_large_image",
    twitter_title: "You’re Following | TradeReply",
    twitter_description:
      "See the traders and sellers you follow on TradeReply. Stay updated on their latest product listings and activity.",
    twitter_image:
      "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    twitter_site: "@JoinTradeReply",
    robots: "noindex, nofollow",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Users You Follow" />
          <FollowingList />
        </div>
      </AccountLayout>
    </>
  );
}
