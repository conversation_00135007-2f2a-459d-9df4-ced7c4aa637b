"use client";
import React from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import FollowersList from "@/Components/common/Account/PublicProfile/FollowersList";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";

export default function Followers() {
  const metaArray = {
    noindex: true,
    title: "Your Followers | TradeReply",
    description:
      "View users who follow your public profile on TradeReply. Track your audience and gauge interest in your marketplace presence.",
    canonical_link: "https://www.tradereply.com/account/followers",
    og_site_name: "TradeReply",
    og_title: "Your Followers | TradeReply",
    og_description:
      "View users who follow your public profile on TradeReply. Track your audience and gauge interest in your marketplace presence.",
    og_url: "https://www.tradereply.com/account/followers",
    og_type: "website",
    og_image:
      "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    og_image_width: "1200",
    og_image_height: "630",
    og_locale: "en_US",
    twitter_card: "summary_large_image",
    twitter_title: "Your Followers | TradeReply",
    twitter_description:
      "View users who follow your public profile on TradeReply. Track your audience and gauge interest in your marketplace presence.",
    twitter_image:
      "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    twitter_site: "@JoinTradeReply",
    robots: "noindex, nofollow",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Your Followers" />
          <FollowersList />
        </div>
      </AccountLayout>
    </>
  );
}
