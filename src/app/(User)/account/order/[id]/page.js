"use client";
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { SolidInfoIcon, BulletPointIcon } from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";

export default function AcoountOverview() {
  const insignArray = [
    {
      title: "[Apr 1, 2025 - 3:42 PM]",
      array: [
        " Seller updated external link {url}",
        "Buyer resolved the Dispute",
        "Buyer submitted a Dispute",
      ],
    },
    {
      title: "[Mar 25, 2025-3:42 PM)]",
      array: [
        "Buyer accessed Invoice",
        "Buyer accessed external link {url}",
        "Buyer accessed file example.jpg",
      ],
    },
    {
      title: "[Apr 1, 2025 - 3:42 PM]",
      array: ["Buyer completed Order ID 29311"],
    },
  ];
  const disputeArray = [
    {
      orderId: "29311",
      product: "[Auto-Filled]",
      status: "Complete",
      dispute: "$2900",
      resolved: "Mar 24, 2025",
      opened: "Mar 24, 2025",
      respondBy: "[Auto-Filled]",
      seller: "@aron",
      buyer: "@wakos",
      accessed: [
        "example_num1.pdf - Last Accessed: April 1, 2025 - 11:42 AM",
        "https://drive.com/stocks - Last Accessed: April 1, 2025 - 11:42 AM",
        "external link - Last Accessed: April 1, 2025 - 11:40 AM",
      ],
    },
  ];

  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Order Details" />
          <Row className="mb-4 mb-lg-4">
            {/* Followers */}
            <Col>
              <CommonWhiteCard title="Order Summary" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Order ID</div>
                          </th>
                          <th>
                            <div className="th-inner">Purchase Date</div>
                          </th>
                          <th>
                            <div className="th-inner">Product Title</div>
                          </th>
                          <th>
                            <div className="th-inner">Price Paid</div>
                          </th>
                          <th>
                            <div className="th-inner">Seller</div>
                          </th>
                          <th>
                            <div className="th-inner">Buyer</div>
                          </th>
                          <th>
                            <div className="th-inner">Status</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.orderId}</td>
                            <td>{item.opened}</td>
                            <td>
                              <Link href="/marketplace/details">
                                {item.product}
                              </Link>
                            </td>
                            <td>{item.dispute}</td>
                            <td>{item.seller}</td>
                            <td>{item.buyer}</td>
                            <td>{item.status}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={4}>Order Id</Col>
                            <Col xs={4}>Purchase Date</Col>
                            <Col xs={4}>Product Title</Col>
                          </div>

                          <Col xs={4} className="colunm_value">
                            {item.orderId}
                          </Col>
                          <Col xs={4} className="colunm_value">
                            {item.opened}
                          </Col>
                          <Col xs={4} className="colunm_value">
                            <Link className="w-100" href="/marketplace/details">
                              {item.product}
                            </Link>
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={3}>Price Paid</Col>
                            <Col xs={3}>Seller</Col>
                            <Col xs={3}>Buyer</Col>
                            <Col xs={3}>Status</Col>
                          </div>

                          <Col xs={3} className="colunm_value">
                            {item.dispute}
                          </Col>
                          <Col xs={3} className="colunm_value">
                            {item.seller}
                          </Col>
                          <Col xs={3} className="colunm_value">
                            {item.buyer}
                          </Col>
                          <Col xs={3} className="colunm_value border-start border-secondary-subtle">
                            {item.status}
                          </Col>
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Access Details" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Invoice</div>
                          </th>
                          <th>
                            <div className="th-inner">
                              External Link
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </div>
                          </th>
                          <th>
                            <div className="th-inner">
                              Attachments
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </div>
                          </th>
                          <th>
                            <div className="th-inner">Last Accessed</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <CommonButton
                                title="Download Invoice (PDF)"
                                className="view_res_btn"
                              />
                            </td>
                            <td>{item.product}</td>
                            <td>
                              <CommonButton
                                title="example.jpg"
                                className="view_res_btn"
                              />
                              <CommonButton
                                title="example_num1.pdf"
                                className="view_res_btn w-auto mt-2"
                              />
                            </td>
                            <td>
                              {item.accessed.map((entry, i) => {
                                return (
                                  <div key={i} className="bullet-points">
                                    <BulletPointIcon />
                                      {entry}
                                  </div>
                                );
                              })}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Invoice</Col>
                          </div>

                          <Col xs={12} className="colunm_value">
                            <CommonButton
                              title="Download Invoice (PDF)"
                              className="view_res_btn w-100"
                            />
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              External Link
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </Col>
                          </div>
                          <td>{item.product}</td>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Attachments
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            <CommonButton
                              title="example.jpg"
                              className="view_res_btn w-100"
                            />
                            <CommonButton
                              title="example_n.pdf"
                              className="view_res_btn w-100 mt-2"
                            />
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Last Accessed</Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            {item.accessed.map((entry, i) => {
                              return (
                                <div key={i} className="bullet-points">
                                  <BulletPointIcon />
                                  {entry}
                                </div>
                              );
                            })}
                          </Col>
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Dispute" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Status</div>
                          </th>
                          <th>
                            <div className="th-inner">Summary</div>
                          </th>
                          <th>
                            <div className="th-inner">Action</div>
                          </th>
                          <th>
                            <div className="th-inner">Resolution Outcome</div>
                          </th>
                          <th>
                            <div className="th-inner">Refund Amount</div>
                          </th>
                          <th>
                            <div className="th-inner">Date Resolved</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.buyer}</td>
                            <td>{item.product}</td>
                            <td>
                              <CommonButton
                                title="View Dispute"
                                className="view_res_btn"
                              />
                            </td>
                            <td>{item.product}</td>
                            <td>{item.dispute}</td>
                            <td>{item.opened}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={6}>Status</Col>
                            <Col xs={6}>Summary</Col>
                          </div>

                          <Col xs={6} className="colunm_value">
                            {item.buyer}
                          </Col>
                          <Col xs={6} className="colunm_value">
                            {item.product}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Action
                            </Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            <CommonButton
                              title="View Dispute"
                              className="view_res_btn w-100"
                            />
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Resolution Outcome
                            </Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            {item.respondBy}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Refund Amount
                            </Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            {item.dispute}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Date Resolved
                            </Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            {item.opened}
                          </Col>
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            {/* Followers */}
            <Col>
              <CommonWhiteCard
                title="Order Timeline Log"
                className="account_card"
              >
                <div className="account_card_timeline">
                  <div>
                    {insignArray.map((item, index) => (
                      <div key={index}>
                        <h2>{item.title}</h2>
                        <ul>
                          {item.array.map((item, index) => (
                            <li key={index}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}
