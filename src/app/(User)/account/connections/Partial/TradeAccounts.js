'use client';

import { Col, Row } from "react-bootstrap";
import React, { useEffect, useState } from 'react'
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import CommonTable from "@/Components/UI/CommonTable";
import { RemoveIconSvg, PlusIconSvg } from "@/assets/svgIcons/SvgIcon";


export default function TradeAccounts() {
    const tradedata = [
        {
            name: "Trade-123  ",
            status: "Connected January 2, 2016",
        },
        {
            name: "Primary Trading",
            status: "Connected January 16, 2016",
        },
    ];
    return (
        <>
            <Col xs={12} className="mb-4 mb-lg-4 connetionTable">
                <CommonBlackCard
                    title="Trade Accounts"
                    text="Manage the accounts you trade with on TradeReply.com"
                    Linktext="Add a new Trade Account"
                    editicon={<PlusIconSvg color="svg-baseblue" />}
                    className="account_card"
                >
                    <div className="account_card_table">
                        <CommonTable fields={false} className="simple_table">
                            {tradedata?.map((item, index) => (
                                <tr key={index}>
                                    <td>{item?.name}</td>
                                    <td>{item?.status}</td>
                                    <td >
                                        <div className="flex justify-end">
                                            {item?.Connected === "Connected" ? (
                                                <button className="blue_text_btn d-flex align-items-center">
                                                    <PlusIconSvg color="svg-baseblue" /> Connect
                                                </button>
                                            ) : (
                                                <button className="blue_text_btn d-flex align-items-center">
                                                    <RemoveIconSvg color="svg-baseblue" /> Remove
                                                </button>
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </CommonTable>
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    )
}
