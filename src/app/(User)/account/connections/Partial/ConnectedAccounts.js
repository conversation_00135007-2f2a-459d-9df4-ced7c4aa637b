'use client';

import { Col, Row } from "react-bootstrap";
import React, { useEffect, useState } from 'react'
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import CommonTable from "@/Components/UI/CommonTable";
import { RemoveIconSvg, PlusIconSvg } from "@/assets/svgIcons/SvgIcon";

export default function ConnectedAccounts() {
    const recentdata = [
        {
            socialname: "Apple ",
            status: "Not Connected",
        },
        {
            socialname: "Facebook ",
            status: "Not Connected",
        },
        {
            socialname: "Facebook ",
            status: "Not Connected",
        },
    ];
    return (
        <>
            <Col xs={12} className="mb-4 mb-lg-4 connetionTable">
                <CommonBlackCard
                    title="Connected Accounts"
                    text="Connect your other accounts to access additional features, including logging in to your TradeReply.com account with those other account credentials."
                    className="account_card"
                >
                    <div className="account_card_table">
                        <CommonTable fields={false} className="simple_table">
                            {recentdata?.map((item, index) => (
                                <tr key={index}>
                                    <td >{item?.socialname}</td>
                                    <td>{item?.status}</td>
                                    <td>
                                        <div className="flex justify-end">
                                            {item?.status === "Not Connected" ? (
                                                <button className="blue_text_btn d-flex align-items-center">
                                                    <PlusIconSvg color="svg-baseblue" /> Connect
                                                </button>
                                            ) : (
                                                <button className="blue_text_btn d-flex align-items-center">
                                                    <RemoveIconSvg color="svg-baseblue" /> Remove
                                                </button>
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </CommonTable>
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    )
}
