'use client';
import { Col } from "react-bootstrap";
import React, { useState, useRef, useEffect } from 'react'; 'react'
import TextInput from "@/Components/UI/TextInput";
import CustomDropdown from "@/Components/common/CustumDropdown";
import { LocationIconSvg, PlusIconSvg, RemoveIconSvg } from "@/assets/svgIcons/SvgIcon";
import countries from 'world-countries';

export default function EditAddressDetails(props = {}) {
    const {
        newAddress = {},
        setNewAddress = () => { },
        selectedAddress = null,
        setSelectedAddress = () => { },
        isSaveDisabled = false,
        onSubmit = () => { },
        onCancel = () => { },
    } = props;
    const [showAddressList, setShowAddressList] = useState(false);
    const [showForm, setShowForm] = useState(false);
    const addressContainerRef = useRef(null);

    const selectCountry = (country) => {
        console.log('Selected:', country);
        setNewAddress({ ...newAddress, country: country.name.common });

    };
    const addresses = [
        {
            id: '01',
            first_name: '<PERSON>',
            last_name: 'McCloud',
            address: '1234 Maplewood Lane',
            address_2: '',
            city: 'Springfield',
            state: 'ZZ',
            zip_code: '99999',
            country: 'United States'
        },
        {
            id: '02',
            first_name: 'Emily',
            last_name: 'Walker',
            address: '5678 Oakridge Drive',
            address_2: 'Apt 4B',
            city: 'Denver',
            state: 'CO',
            zip_code: '80202',
            country: 'United States'
        },
        {
            id: '03',
            first_name: 'David',
            last_name: 'Nguyen',
            address: '9101 Pine Street',
            address_2: 'Suite 300',
            city: 'Seattle',
            state: 'WA',
            zip_code: '98101',
            country: 'United States'
        }
    ];

    const handleInputChange = (e) => {
        setNewAddress({
            ...newAddress,
            [e.target.name]: e.target.value,
        });
    };

    const handleSelect = (address) => {
        setSelectedAddress(address);
        setShowAddressList(false);
    };
    const handleRemove = () => {
        setSelectedAddress(null);
    };
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (
                addressContainerRef.current &&
                !addressContainerRef.current.contains(event.target)
            ) {
                setShowAddressList(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
    return (
        <>
            <Col xs={12} lg={7} className="address-details">
                <p className="font-18 fw-500 text-white mb-2">Billing address</p>
                <div className="relative" ref={addressContainerRef}>
                    <p className="choose-billing mb-2" onClick={() => setShowAddressList(!showAddressList)}>
                        Choose or add a billing address
                    </p>

                    {showAddressList && !selectedAddress && (
                        <div className="show-save-address">
                            <ul>
                                {addresses.map((add) => (
                                    <li key={add.id} onClick={() => handleSelect(add)}>
                                        <LocationIconSvg />
                                        <span>
                                            {add.address}, {add.city}, {add.state} {add.zip_code}, {add.country}
                                        </span>
                                    </li>
                                ))}
                            </ul>
                            <button onClick={() => {
                                setShowForm(true);
                                setShowAddressList(false);
                            }}>
                                <PlusIconSvg />
                                <span>Add a New Address</span>
                            </button>
                        </div>
                    )}

                    {selectedAddress && (
                        <div className="show-selected-address mb-2">
                            <div className="d-flex align-items-center gap-2">
                                <LocationIconSvg />
                                <span>
                                    {selectedAddress.address}, {selectedAddress.city}, {selectedAddress.state}{' '}
                                    {selectedAddress.zip_code}, {selectedAddress.country}
                                </span>
                            </div>
                            <button onClick={handleRemove}>
                                <RemoveIconSvg />
                            </button>
                        </div>
                    )}
                </div>
                {showForm && !selectedAddress && (
                    <div className="add-payment-address-form row mt-3">
                        <Col xs={12} className="mb-3">
                            <CustomDropdown
                                options={countries.map((c) => ({ label: c.name.common, ...c }))}
                                defaultValue="Select Country"
                                onSelect={selectCountry}
                            />
                        </Col>
                        <Col xs={6}>
                            <TextInput
                                name="first_name"
                                type="text"
                                placeholder="First name"
                                value={newAddress.first_name}
                                onChange={handleInputChange}
                            />
                        </Col>
                        <Col xs={6}>
                            <TextInput
                                name="last_name"
                                type="text"
                                placeholder="Last name"
                                value={newAddress.last_name}
                                onChange={handleInputChange}
                            />
                        </Col>
                        <Col xs={12}>
                            <TextInput
                                name="address"
                                type="text"
                                placeholder="Address"
                                value={newAddress.address}
                                onChange={handleInputChange}
                            />
                        </Col>
                        <Col xs={12}>
                            <TextInput
                                name="address_2"
                                type="text"
                                placeholder="Address Line 2 (Optional)"
                                value={newAddress.address_2}
                                onChange={handleInputChange}
                            />
                        </Col>
                        <Col xs={12}>
                            <TextInput
                                name="city"
                                type="text"
                                placeholder="City"
                                value={newAddress.city}
                                onChange={handleInputChange}
                            />
                        </Col>
                        <Col xs={12}>
                            <TextInput
                                name="state"
                                type="text"
                                placeholder="State or Region"
                                value={newAddress.state}
                                onChange={handleInputChange}
                            />
                        </Col>
                        <Col xs={12}>
                            <TextInput
                                name="zip_code"
                                type="text"
                                placeholder="Zip or Postal Code"
                                value={newAddress.zip_code}
                                onChange={handleInputChange}
                            />
                        </Col>
                    </div>
                )}
                <div className={`bottom-area ${showForm && !selectedAddress ? 'position-relative' : ''}`}>
                    <p>By clicking “Save”, you agree to <a href="/terms" target="_blank">TradeReply’s Terms of Service</a> and <a href="/privacy" target="_blank">Privacy Policy</a> .</p>
                    <div className="d-flex align-items-center gap-3 mt-3">
                        <button onClick={onSubmit} className="btn-style w-50" disabled={isSaveDisabled}>
                            Save
                        </button>
                        <button className="btn-style gray-btn w-50" onClick={onCancel}>
                            Cancel
                        </button>
                    </div>
                </div>
            </Col>
        </>
    )
}
