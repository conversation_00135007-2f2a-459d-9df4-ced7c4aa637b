'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react';
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import "@/css/account/PaymentMethods.scss";
import { CheckIcon, RemoveIconSvg, PlusIconSvg, EditIconSvg } from "@/assets/svgIcons/SvgIcon";
import CommonTable from "@/Components/UI/CommonTable";
import Link from "next/link";
import InputLabel from "@/Components/UI/InputLabel";
import TextInput from "@/Components/UI/TextInput";
import CustomDropdown from "@/Components/common/CustumDropdown";
import EditAddressDetails from "./EditAddressDetails";

export default function AddNewPaymentMethod() {
    const [cardName, setCardName] = useState('');
    const [cardMonth, setCardMonth] = useState('');
    const [cardYear, setCardYear] = useState('');
    const [newAddress, setNewAddress] = useState({
        first_name: '',
        last_name: '',
        address: '',
        address_2: '',
        city: '',
        state: '',
        zip_code: '',
        country: ''
    });
    const [paymentMethods, setPaymentMethods] = useState([
        {
            id: 1,
            card_type: 'Visa',
            card_number: '1234 3344 5566 7788',
            text: 'Payment Method',
            isDefault: true,
        },
        {
            id: 2,
            card_type: 'Master',
            card_number: '5678 3344 5566 7788',
            text: 'Payment Method',
            isDefault: false,
        },
        {
            id: 3,
            card_type: 'Master',
            card_number: '9876 3344 5566 7788',
            text: 'Payment Method',
            isDefault: false,
        },
    ]);
    const [selectedAddress, setSelectedAddress] = useState(null);
    const [editCardId, setEditCardId] = useState(null);
    const [removeCardId, setRemoveCardId] = useState(null);

    const isAddressValid = () => {
        return (
            newAddress.first_name.trim() !== '' &&
            newAddress.last_name.trim() !== '' &&
            newAddress.address.trim() !== '' &&
            newAddress.city.trim() !== '' &&
            newAddress.state.trim() !== '' &&
            newAddress.zip_code.trim() !== '' &&
            newAddress.country.trim() !== ''
        );
    };

    const isCardValid = () =>
        cardName.trim() !== '' &&
        cardMonth !== '' &&
        cardYear !== '';

    const isSaveDisabled = !(
        isCardValid() && (selectedAddress || isAddressValid())
    );

    const handleSubmit = () => {
        if (isSaveDisabled) {
            console.log("Submit blocked: form is invalid");
            return;
        }

        console.log("✅ Form submitted successfully!");
        console.log("Card Details:", { cardName, cardMonth, cardYear });
        console.log("Selected Address:", selectedAddress);
        console.log("New Address:", newAddress);
        setRemoveCardId(null);

    };

    const months = [...Array(12)].map((_, i) => {
        const val = (i + 1).toString().padStart(2, '0');
        return { label: val, code: val };
    });

    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 10 }, (_, i) => {
        const year = currentYear + i;
        return { label: year.toString(), code: year.toString() };
    });

    const selectCardMonth = (month) => {
        setCardMonth(month.code);
    };

    const selectCardYear = (year) => {
        setCardYear(year.code);
    };

    const handleRemove = (id) => {
        console.log("Removing payment method with id:", id);
        setPaymentMethods(prev => prev.filter(method => method.id !== id));
        setRemoveCardId(null);
    };

    const metaArray = {
        noindex: true,
        title: "Manage Payment Method | Update Payment | TradeReply",
        description: "Manage your payment methods on TradeReply.com. Update, add, or remove credit cards and other payment options for seamless transactions.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Manage Payment Method | Update Payment | TradeReply",
        og_description: "Manage your payment methods on TradeReply.com. Update, add, or remove credit cards and other payment options for seamless transactions.",
        twitter_title: "Manage Payment Method | Update Payment | TradeReply",
        twitter_description: "Manage your payment methods on TradeReply.com. Update, add, or remove credit cards and other payment options for seamless transactions.",
    };

    return (
        <AccountLayout>
            <MetaHead props={metaArray} />
            <div className="add_payment_method">
                <SidebarHeading title="Manage Payment Method" />
                <div className="mb-3 mb-lg-4">
                    <div className="common_blackcard account_card">
                        <div className="common_blackcard_innerheader">
                            <div className="common_blackcard_innerheader_content">
                                <h6>Manage Payment Method</h6>
                            </div>
                            <div className="common_blackcard_innerheader_icon">
                                <Link href='/account/payments/setup' prefetch={true}>
                                    <button className="d-flex align-items-center">
                                        <span className="me-2"><PlusIconSvg /></span>
                                        Add a new Payment Method
                                    </button>
                                </Link>
                            </div>
                        </div>
                        <div className="common_blackcard_innerbody p-0 manage-payment-method">
                            <div className="account_card_table">
                                <CommonTable fields={false} className="simple_table">
                                    {[...paymentMethods]
                                        .sort((a, b) => (b.isDefault === true) - (a.isDefault === true))
                                        .map((payment) => (
                                            <tr key={payment.id}>
                                                <td>
                                                    {payment.isDefault ? (
                                                        <div className="d-flex align-items-center gap-2">
                                                            <CheckIcon />
                                                            <span className="green_text">Default</span>
                                                        </div>
                                                    ) : (
                                                        <button>
                                                            <span className='text_00ADEF'>Set as Default</span>
                                                        </button>
                                                    )}
                                                </td>

                                                {editCardId === payment.id ? (
                                                    <>
                                                        <td colSpan={3} className="edit-details">
                                                            <Row>
                                                                <Col xs={12} md={5} className="card-details">
                                                                    <h6 className="mb-3">{payment?.card_type}
                                                                        <span className="ps-2">{payment?.card_number?.slice(-4)}</span>
                                                                    </h6>
                                                                    <div className="mb-3">
                                                                        <InputLabel value="Name on card" />
                                                                        <TextInput
                                                                            placeholder="Full Name as shown on card"
                                                                            value={cardName}
                                                                            onChange={(e) => setCardName(e.target.value)}
                                                                        />
                                                                    </div>
                                                                    <div>
                                                                        <InputLabel value="Expiration date" />
                                                                        <Row>
                                                                            <Col xs={6}>
                                                                                <CustomDropdown
                                                                                    showSearch={false}
                                                                                    options={months}
                                                                                    defaultValue="MM"
                                                                                    onSelect={selectCardMonth}
                                                                                />
                                                                            </Col>
                                                                            <Col xs={6}>
                                                                                <CustomDropdown
                                                                                    showSearch={false}
                                                                                    options={years}
                                                                                    defaultValue="YYYY"
                                                                                    onSelect={selectCardYear}
                                                                                />
                                                                            </Col>
                                                                        </Row>
                                                                    </div>
                                                                </Col>
                                                                <EditAddressDetails
                                                                    newAddress={newAddress}
                                                                    setNewAddress={setNewAddress}
                                                                    selectedAddress={selectedAddress}
                                                                    setSelectedAddress={setSelectedAddress}
                                                                    isSaveDisabled={isSaveDisabled}
                                                                    onSubmit={handleSubmit}
                                                                    onCancel={() => setEditCardId(null)}
                                                                />
                                                            </Row>
                                                        </td>
                                                    </>
                                                ) : removeCardId === payment.id ? (
                                                    <>
                                                        <td>
                                                            <h6>{payment?.card_type}
                                                                <span className="ps-2">{payment?.card_number?.slice(-4)}</span>
                                                            </h6>
                                                            <p className="mt-2">{payment?.text}</p>
                                                            {removeCardId === payment.id && (
                                                                <div className="remove-payment-confirmation mt-2">
                                                                    <p>Are you sure you want to remove this address?</p>
                                                                    <div className="btns d-flex gap-2">
                                                                        <button
                                                                            className="btn-style gray-btn"
                                                                            onClick={() => setRemoveCardId(null)}
                                                                        >
                                                                            Cancel
                                                                        </button>
                                                                        <button className="btn-style red-btn" onClick={() => handleRemove(payment.id)}>
                                                                            Remove
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            )}

                                                        </td>
                                                        <td colSpan={2}></td>
                                                    </>
                                                ) : (
                                                    <>
                                                        <td>
                                                            <h6>{payment?.card_type}
                                                                <span className="ps-2">{payment?.card_number?.slice(-4)}</span>
                                                            </h6>
                                                            <p className="mt-2">{payment?.text}</p>
                                                        </td>
                                                        <td></td>
                                                        <td>
                                                            <div className="flex justify-end gap-4">
                                                                {!payment.isDefault && (
                                                                    <button
                                                                        className="blue_text_btn d-flex align-items-center"
                                                                        onClick={() => setRemoveCardId(payment.id)}
                                                                    >
                                                                        <RemoveIconSvg /> Remove
                                                                    </button>
                                                                )}
                                                                <button
                                                                    className="blue_text_btn d-flex align-items-center"
                                                                    onClick={() => setEditCardId(payment.id)}
                                                                >
                                                                    <EditIconSvg /> Edit
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </>
                                                )}
                                            </tr>
                                        ))}
                                </CommonTable>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AccountLayout>
    );
}
