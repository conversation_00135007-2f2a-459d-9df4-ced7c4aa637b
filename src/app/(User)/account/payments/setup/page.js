'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useRef, useEffect } from 'react';
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import "@/css/account/PaymentMethods.scss";
import CardDetails from "./CardDetails";
import AddressDetails from "./AddressDetails";

export default function AddNewPaymentMethod() {
    const [cardNumber, setCardNumber] = useState('');
    const [cardName, setCardName] = useState('');
    const [cardMonth, setCardMonth] = useState('');
    const [cardYear, setCardYear] = useState('');
    const [cardCVC, setCardCVC] = useState('');
    const [newAddress, setNewAddress] = useState({
        first_name: '',
        last_name: '',
        address: '',
        address_2: '',
        city: '',
        state: '',
        zip_code: '',
        country: ''
    });
    const [selectedAddress, setSelectedAddress] = useState(null);

    const isAddressValid = () => {
        return (
            newAddress.first_name.trim() !== '' &&
            newAddress.last_name.trim() !== '' &&
            newAddress.address.trim() !== '' &&
            newAddress.city.trim() !== '' &&
            newAddress.state.trim() !== '' &&
            newAddress.zip_code.trim() !== '' &&
            newAddress.country.trim() !== ''
        );
    };

    const isCardValid = () =>
        cardNumber.replace(/\s/g, '').length === 16 &&
        cardName.trim() !== '' &&
        cardMonth !== '' &&
        cardYear !== '' &&
        (cardCVC.length === 3 || cardCVC.length === 4);

    const isSaveDisabled = !(
        isCardValid() && (selectedAddress || isAddressValid())
    );
    const handleSubmit = () => {
        if (isSaveDisabled) {
            console.log("Submit blocked: form is invalid");
            return;
        }

        console.log("✅ Form submitted successfully!");
        console.log("Card Details:", { cardNumber, cardName, cardMonth, cardYear, cardCVC });
        console.log("Selected Address:", selectedAddress);
        console.log("New Address:", newAddress);

    };
    const metaArray = {
        noindex: true,
        title: "Add Payment Method | Update Payment | TradeReply",
        description: "Add new payment method on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Add Payment Method | Update Payment | TradeReply",
        og_description: "Add new payment method on TradeReply.com.",
        twitter_title: "Add Payment Method | Update Payment | TradeReply",
        twitter_description: "Add new payment method on TradeReply.com.",
    };

    return (

        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="add_payment_method">
                    <SidebarHeading title="Add Payment Method" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <h6>New Payment Method</h6>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list row">
                                    <CardDetails
                                        cardNumber={cardNumber}
                                        setCardNumber={setCardNumber}
                                        cardName={cardName}
                                        setCardName={setCardName}
                                        cardMonth={cardMonth}
                                        setCardMonth={setCardMonth}
                                        cardYear={cardYear}
                                        setCardYear={setCardYear}
                                        cardCVC={cardCVC}
                                        setCardCVC={setCardCVC}
                                    />
                                    <AddressDetails
                                        newAddress={newAddress}
                                        setNewAddress={setNewAddress}
                                        selectedAddress={selectedAddress}
                                        setSelectedAddress={setSelectedAddress}
                                        isSaveDisabled={isSaveDisabled}
                                        onSubmit={handleSubmit}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AccountLayout>
        </>
    )
}
