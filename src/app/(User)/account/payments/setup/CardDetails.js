'use client';
import { Col, Row } from "react-bootstrap";
import React from 'react';
import TextInput from "@/Components/UI/TextInput";
import InputLabel from "@/Components/UI/InputLabel";
import "@/css/account/PaymentMethods.scss";
import CustomDropdown from "@/Components/common/CustumDropdown";
import CommonTooltip from '@/Components/UI/CommonTooltip';
import { SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';


export default function CardDetails({
    cardNumber,
    setCardNumber,
    cardName,
    setCardName,
    cardMonth,
    setCardMonth,
    cardYear,
    setCardYear,
    cardCVC,
    setCardCVC
}) {
    const handleCardNumberChange = (e) => {
        const input = e.target.value.replace(/\D/g, '').substring(0, 16);
        const formatted = input.match(/.{1,4}/g)?.join(' ') || '';
        setCardNumber(formatted);
    };

    const handleCVCChange = (e) => {
        const input = e.target.value.replace(/\D/g, '').substring(0, 4);
        setCardCVC(input);
    };

    const months = [...Array(12)].map((_, i) => {
        const val = (i + 1).toString().padStart(2, '0');
        return { label: val, code: val };
    });

    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 10 }, (_, i) => {
        const year = currentYear + i;
        return { label: year.toString(), code: year.toString() };
    });

    const selectCardMonth = (month) => {
        setCardMonth(month.code);
    }

    const selectCardYear = (year) => {
        setCardYear(year.code);
    }

    return (
        <Col xs={12} lg={6} className="card-details">
            <Row>
                <Col xs={12}>
                    <InputLabel value="Card Number" />
                    <TextInput
                        type="text"
                        placeholder="1234 1234 1234 1234"
                        value={cardNumber}
                        onChange={handleCardNumberChange}
                    />
                </Col>

                <Col xs={12}>
                    <InputLabel value="Name on card" />
                    <TextInput
                        type="text"
                        placeholder="Full Name as shown on card"
                        value={cardName}
                        onChange={(e) => setCardName(e.target.value)}
                    />
                </Col>

                <Col xs={6}>
                    <InputLabel value="Expiration date" />
                    <div className="row">
                        <Col xs={6}>
                            <CustomDropdown
                                showSearch={false}
                                options={months}
                                defaultValue="MM"
                                onSelect={selectCardMonth}
                            />
                        </Col>
                        <Col xs={6}>
                            <CustomDropdown
                                showSearch={false}
                                options={years}
                                defaultValue="YYYY"
                                onSelect={selectCardYear}
                            />
                        </Col>
                    </div>
                </Col>

                <Col xs={6}>
                    <div className='d-flex align-items-center gap-1 mb-2'>
                        <InputLabel value="CVC" className="mb-0" />
                        <CommonTooltip
                            className="CustomTooltip"
                            content={
                                <>
                                    <p className='mb-2'>
                                        CVC (also called CVV)
                                    </p>
                                    <p className='mb-2'>
                                        A 3- or 4-digit security code used to verify your card.
                                    </p>
                                    <p className='mb-1'>– Found on the back of most cards (Visa, Mastercard)</p>
                                    <p className='mb-1'>– On the front of Amex cards</p>
                                </>
                            }
                            position="top-right"
                        >
                            <SolidInfoIcon />
                        </CommonTooltip>
                    </div>
                    <TextInput
                        type="text"
                        placeholder="3-4 Digit Code"
                        value={cardCVC}
                        onChange={handleCVCChange}
                    />
                </Col>
            </Row>

            <div className="custom_checkbox border-bottom pb-3 mb-2">
                <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id="defaultPayment"
                />
                <label className="custom_checkbox_label mb-0" htmlFor="defaultPayment">
                    Set as default payment method
                </label>
            </div>

            <p className="font-18 fw-500 text-white mb-2">TradeReply accepts major credit and debit cards.</p>
            <div className="payment-method-images mb-3">
                <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-visa.png" alt="" />
                <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-mastercard.png" alt="" />
                <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-american-express.png" alt="" />
                <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-diners-club.png" alt="" />
            </div>
            <div className="payment-method-images mb-0">
                <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-discover.png" alt="" />
                <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-jcb.png" alt="" />
                <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-union-pay.png" alt="" />
                <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-visa-electron.png" alt="" />
            </div>
        </Col>
    );
}
