'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { CheckIcon, RemoveIconSvg } from "@/assets/svgIcons/SvgIcon";
import CommonTable from "@/Components/UI/CommonTable";
import "@/css/account/PaymentMethods.scss";
import Credits from "./Partial/Credits";
import PaymentMethods from "./Partial/PaymentMethods";

export default function Payments() {
    const metaArray = {
        noindex: true,
        title: "Account Payments | Manage Payment Methods | TradeReply",
        description: "Manage your payment methods on TradeReply.com. Update, add, or remove credit cards and other payment options for seamless transactions.",
        canonical_link: "https://www.tradereply.com/account/payments",
        og_site_name: "TradeReply",
        og_title: "Manage Payment Methods | TradeReply",
        og_description: "Manage your payment methods on TradeReply. Update, add, or remove credit cards and other payment options for seamless transactions.",
        twitter_title: "Manage Payment Methods | TradeReply",
        twitter_description: "Manage your payment methods on TradeReply. Update, add, or remove credit cards and other payment options for seamless transactions.",
    };

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="payment_methods">
                    <SidebarHeading title="Payment Methods" />
                    <Row>
                        <Credits />
                        <PaymentMethods />
                    </Row>
                </div>
            </AccountLayout>
        </>
    )
}
