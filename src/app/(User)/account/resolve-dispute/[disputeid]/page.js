"use client";
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect, useRef } from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import CustomWhiteDropdown from "@/Components/common/CustomWhiteDropdown";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";

export default function ResolveDispute() {
  const [fileName, setFileName] = useState("File Upload");
  const [sellerResponse, setSellerResponse] = useState("");
  const [externalLink, setExternalLink] = useState("");

  const MAX_Response_LENGTH = 500;
  const MAX_External_LENGTH = 1000;

  const responseRefDesktop = useRef(null);
  const externalRefDesktop = useRef(null);
  const responseRefMobile = useRef(null);
  const externalRefMobile = useRef(null);

  const disputeArray = [
    {
      buyer: "@Wakas",
      orderId: "29311",
      product: "[Auto-filled]",
      dispute: "3432",
      status: "Open",
      openedOn: "Mar 24, 2025",
    },
  ];
  const resolOptions = [
    { name: "Full refund" },
    { name: "Partial refund" },
    { name: "Replacement file or fixed version" },
    { name: "Corrected external link" },
    { name: "Communication from seller" },
    { name: "Dispute is invalid - buyer already received the correct product" },
  ];

  const autoResize = (ref) => {
    if (ref.current) {
      ref.current.style.height = "auto";
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  };
  useEffect(() => {
    autoResize(responseRefDesktop);
    autoResize(responseRefMobile);
  }, [sellerResponse]);

  useEffect(() => {
    autoResize(externalRefDesktop);
    autoResize(externalRefMobile);
  }, [externalLink]);

  useEffect(() => {
    const handleResize = () => {
      autoResize(responseRefDesktop);
      autoResize(responseRefMobile);
      autoResize(externalRefDesktop);
      autoResize(externalRefMobile);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
    }
  };

  const getTruncatedFileName = (name, maxLength = 18) => {
    return name.length > maxLength
      ? name.slice(0, maxLength - 3) + "..."
      : name;
  };
  const selectResolution = (country) => {
    console.log("Selected:", country);
  };
  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Resolve Dispute" />
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Dispute Summary" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Order ID</div>
                          </th>
                          <th>
                            <div className="th-inner">Product Title</div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute ID</div>
                          </th>
                          <th>
                            <div className="th-inner">Status</div>
                          </th>
                          <th>
                            <div className="th-inner">Date Opened</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.orderId}</td>
                            <td>
                              <Link href="/marketplace/details">
                                {item.product}
                              </Link>
                            </td>
                            <td>{item.dispute}</td>
                            <td>{item.status}</td>
                            <td>{item.openedOn}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={4}>Order ID</Col>
                            <Col xs={4}>Product Title</Col>
                            <Col xs={4}>Status</Col>
                          </div>

                          <Col xs={4} className="colunm_value">
                            {item.orderId}
                          </Col>
                          <Col xs={4} className="colunm_value">
                            <Link href="/marketplace/details">
                              {item.product}
                            </Link>
                          </Col>
                          <Col xs={4} className="colunm_value">
                            {item.dispute}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={6}>Dispute ID</Col>
                            <Col xs={6}>Date Opened</Col>
                          </div>
                          <Col xs={6} className="colunm_value">
                            {item.status}
                          </Col>
                          <Col xs={6} className="colunm_value">
                            {item.openedOn}
                          </Col>
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Buyer Details" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Buyer</div>
                          </th>
                          <th>
                            <div className="th-inner">Buyer Message</div>
                          </th>
                          <th>
                            <div className="th-inner">
                              Attachments
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute Category</div>
                          </th>
                          <th>
                            <div className="th-inner">Preferred Outcome</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.buyer}</td>
                            <td>{item.product}</td>
                            <td>
                              <CommonButton
                                title=" View / Download"
                                className="view_res_btn"
                              />
                            </td>
                            <td>{item.product}</td>
                            <td>{item.product}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={6}>Buyer</Col>
                            <Col xs={6}>Buyer Message</Col>
                          </div>

                          <Col xs={6} className="colunm_value">
                            {item.buyer}
                          </Col>
                          <Col xs={6} className="colunm_value">
                            {item.product}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={6}>Dispute Category</Col>
                            <Col xs={6}>Preferred Outcome</Col>
                          </div>

                          <Col xs={6} className="colunm_value">
                            {item.product}
                          </Col>
                          <Col xs={6} className="colunm_value">
                            {item.product}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Attachments
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </Col>
                          </div>
                          <CommonButton
                            title="example.jpg"
                            className="view_res_btn w-auto"
                          />
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Seller Response" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Response</div>
                          </th>
                          <th>
                            <div className="th-inner">
                              Add Attachments
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </div>
                          </th>
                          <th>
                            <div className="th-inner">
                              Corrected External Link
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </div>
                          </th>
                          <th>
                            <div className="th-inner">Resolution Chosen</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <div className="customInput_inner">
                                <textarea
                                  ref={responseRefDesktop}
                                  rows="2"
                                  maxLength={MAX_Response_LENGTH}
                                  className="form-control table_form_textarea w-full resize-none overflow-hidden"
                                  value={sellerResponse}
                                  onChange={(e) =>
                                    setSellerResponse(e.target.value)
                                  }
                                />
                                <p className="character-count">
                                  Characters: {sellerResponse.length}/
                                  {MAX_Response_LENGTH}
                                </p>
                              </div>
                            </td>
                            <td>
                              <label className="file-upload-wrapper">
                                {getTruncatedFileName(fileName)}
                                <input
                                  type="file"
                                  name="file"
                                  onChange={handleFileChange}
                                />
                              </label>
                            </td>
                            <td>
                              <div className="customInput_inner">
                                <textarea
                                  ref={externalRefDesktop}
                                  rows="2"
                                  maxLength={MAX_External_LENGTH}
                                  className="form-control table_form_textarea w-full resize-none overflow-hidden"
                                  value={externalLink}
                                  onChange={(e) =>
                                    setExternalLink(e.target.value)
                                  }
                                />
                                <p className="character-count">
                                  Characters: {externalLink.length}/
                                  {MAX_External_LENGTH}
                                </p>
                              </div>
                            </td>
                            <td>
                              <CustomWhiteDropdown
                                options={resolOptions.map((c) => ({
                                  label: c.name,
                                  ...c,
                                }))}
                                defaultValue="Select your resolution"
                                onSelect={selectResolution}
                                className="align-right"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Response</Col>
                          </div>
                          <Col xs={12} className="colunm_value px-0">
                            <div className="customInput_inner">
                              <textarea
                                ref={responseRefMobile}
                                rows="2"
                                maxLength={MAX_Response_LENGTH}
                                className="form-control table_form_textarea w-full resize-none overflow-hidden"
                                value={sellerResponse}
                                onChange={(e) =>
                                  setSellerResponse(e.target.value)
                                }
                              />
                              <p className="character-count">
                                Characters: {sellerResponse.length}/
                                {MAX_Response_LENGTH}
                              </p>
                            </div>
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Add Attachments
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </Col>
                          </div>
                          <Col xs={12} className="colunm_value px-0">
                            <label className="file-upload-wrapper">
                              {getTruncatedFileName(fileName)}
                              <input
                                type="file"
                                name="file"
                                onChange={handleFileChange}
                              />
                            </label>
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Corrected External Link
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </Col>
                          </div>
                          <Col xs={12} className="colunm_value px-0">
                            <div className="customInput_inner">
                              <textarea
                                ref={externalRefMobile}
                                rows="2"
                                maxLength={MAX_External_LENGTH}
                                className="form-control table_form_textarea w-full resize-none overflow-hidden"
                                value={externalLink}
                                onChange={(e) =>
                                  setExternalLink(e.target.value)
                                }
                              />
                              <p className="character-count">
                                Characters: {externalLink.length}/
                                {MAX_External_LENGTH}
                              </p>
                            </div>
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Resolution Chosen</Col>
                          </div>
                          <Col xs={12} className="colunm_value px-0">
                           <CustomWhiteDropdown
                                options={resolOptions.map((c) => ({
                                  label: c.name,
                                  ...c,
                                }))}
                                defaultValue="Select your resolution"
                                onSelect={selectResolution}
                                className="align-right"
                              />
                          </Col>
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
              <CommonButton
                title="Submit Response"
                className="view_res_btn mt-4 w-100"
              />
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}
