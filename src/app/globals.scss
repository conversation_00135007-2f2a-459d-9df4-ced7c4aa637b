@import '../css/app.scss';
@import '../assets/fonts/fonts.scss';
@import '../css/theme/_var.scss';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground: #ffffff;
  --background: #011132;
  --font-gilroy: '<PERSON>roy', sans-serif;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #011132;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-gilroy);
}

.osano-cm-widget {
  display: none;
}

.osano-cm-dialog {
  border: 1px solid #00719d;
}

.arrow-right {
  width: 50px !important;
  height: 50px !important;
  border-radius: 10rem;
  background-color: #00adef !important;
  z-index: 2;
}

.slick-prev,
.slick-next {
  position: relative !important;
  left: 0px !important;
  right: 0px !important;
}

.popup {
  position: absolute;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.nextjs-toast-errors-parent {
  display: none;
}

.text-sec {
  color: #ffffff
}

.font-14 {
  font-size: 14px;
  ;
}

.font-18 {
  font-size: 18px;
}

.popup-container {
  position: fixed;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
}

.min-h-500 {
  height: 600px !important;
  min-height: 600px !important;
  min-width: 350px !important;
}

.caret {
  animation: blink 1s step-end infinite;
}

.scroll-lock {
  position: fixed;
  width: 100%;
  overflow: hidden;
}

@keyframes blink {
  50% {
    opacity: 0;
  }
}

.new-link {
  transition: all .3s ease-in-out;
  color: #00adef !important;
}

.text-md-nowrap {
  white-space: nowrap;
}

@media (max-width: 700px) {
  .text-md-nowrap {
    white-space: unset;
  }

}

.scroll-table {
  max-height: 270px;
  overflow-y: scroll;
}

.bg-trans>* {
  background-color: transparent;
}

/* Styling for Tiptap editor */
.jodit-container {
  background: white !important;
  /* color: black !important; */
}

.jodit-container .jodit-wysiwyg {
  background: rgb(139, 124, 124) !important;
  color: black !important;
  min-height: 300px;
  padding: 10px;
  font-size: 16px;
}

.jodit-container .jodit-toolbar {
  background: #f8f9fa !important;
  border-bottom: 1px solid #ddd;
}

.cart_button {
  border-radius: 10px !important;
  width: 70%;
}

.cart_select {
  padding: 0px 20px;
  border-radius: 10px;
  width: 25%;
  background-color: white;
  color: black;
  border: 1px solid transparent;
}

.bb-blue {
  border-bottom: 3px solid #00ADEF4D;
}

.ws-normal {
  white-space: normal !important;
}

.txt-blue {
  color: #00adef;
}

.search-highlight {
  color: #04498C;
  font-weight: 600 !important;
}

.scroll-hidden {
  overflow-y: auto;
  max-height: 100vh;
  /* or any height you want */
}

.scroll-hidden::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari */
}


a,
a:hover {
  text-decoration: none;
  transition: all ease-in-out 0.3s;
  color: #00adef;
}

h1,
.h1 {
  font-size: 3rem;
  font-weight: 800;
}

@media (max-width: 1199px) {

  h1,
  .h1 {
    font-size: 2.5rem;
  }
}

@media (max-width: 767px) {

  h1,
  .h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 390px) {

  h1,
  .h1 {
    font-size: 1.3rem;
  }
}

h2,
.h2 {
  font-size: 5rem;
  font-weight: 800;
}

@media (max-width: 1269px) {

  h2,
  .h2 {
    font-size: 3rem;
  }
}

@media (max-width: 767px) {

  h2,
  .h2 {
    font-size: 1.363rem;
  }
}

h3,
.h3 {
  font-size: 2.8rem;
  font-weight: 800;
}

@media (max-width: 1199px) {

  h3,
  .h3 {
    font-size: 1.688rem;
  }
}

@media (max-width: 767px) {

  h3,
  .h3 {
    font-size: 1.25rem;
  }
}

h4,
.h4 {
  font-size: 1.65rem;
  line-height: 35px;
  font-weight: 600;
}

@media (max-width: 767px) {

  h4,
  .h4 {
    font-size: 1.15rem;
    line-height: 25px;
  }
}

h5,
.h5 {
  font-size: 1.25rem;
  line-height: 30px;
  font-weight: 600;
}

@media (max-width: 767px) {

  h5,
  .h5 {
    font-size: 1rem;
    line-height: 25px;
  }
}

h6,
.h6 {
  font-size: 1.125rem;
  font-weight: 600;
}

@media (max-width: 767px) {

  h6,
  .h6 {
    font-size: 1rem;
  }
}

p {
  font-size: 1rem;
  font-weight: 400;
}

@media (max-width: 767px) {
  p {
    font-size: 0.875rem;
  }
}

.content-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom_checkbox {
  margin-bottom: 1.25rem;
  display: flex;
  align-items: center;
  gap: 10px;

  &_input {
    height: 20px !important;
    width: 20px !important;
    margin-top: 0 !important;
    background-color: transparent !important;
    border: 1px solid #00ADEF !important;

    &:focus {
      box-shadow: none !important;
    }

    &:checked {
      background-color: #00ADEF !important;
      border: 1px solid #00ADEF !important;
    }
  }

  &_label {
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
  }

}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-label {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;

  @media (width<=550px) {
    font-size: 16px;
  }

  @media (width<=350px) {
    font-size: 14px;
  }
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background-color: #9c9a9f;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked+.slider {
  background-color: #0099d1;
}

input:checked+.slider:before {
  transform: translateX(22px);
  background-color: white;
}