import Home from "@/Components/common/Home";
import HomeLayout from "@/Layouts/HomeLayout";
import "../css/Home/home.scss";
import {
  generateOrganizationSchema,
  generateWebsiteSchema,
} from "@/Seo/Schema/JsonLdSchema";

export default async function Welcome() {
  let featuredArticle = null;

  const schemas = [generateOrganizationSchema(), generateWebsiteSchema()];
  // Only fetch during runtime, not during build
  if (
    process.env.NODE_ENV !== "production" ||
    process.env.NEXT_PHASE !== "phase-production-build"
  ) {
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/featured-resource`,
        {
          next: { revalidate: 3600 }, // Cache for 1 hour instead of no-store
        }
      );
      if (res.ok) {
        const json = await res.json();
        featuredArticle = json?.data;
      } else {
        console.error("Failed to fetch featured resource:", res.status);
      }
    } catch (err) {
      console.error("Error while fetching featured resource:", err);
      // Gracefully handle API unavailability during build
    }
  }

  const metaProps = {
    title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
    description:
      "Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.",
    canonical_link: "https://www.tradereply.com/",
    og_title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
    og_description:
      "Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.",
    og_site_name: "TradeReply",
    twitter_title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
    twitter_description:
      "Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.",
  };

  // Generate JSON-LD schemas for homepage

  return (
    <>
        <HomeLayout>
          <Home featuredArticle={featuredArticle} />
        </HomeLayout>
    </>
  );
}
