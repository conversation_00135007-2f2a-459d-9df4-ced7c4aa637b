"use client";
import React from "react";
import { Col, Container, Row } from "react-bootstrap";
import MetaHead from "@/Seo/Meta/MetaHead";
import DashboardLayout from "@/Layouts/DashboardLayout";
import "@/css/dashboard/TradeCalculators.scss";
import "@/css/dashboard/layout.scss";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import RiskRewardCalculator from "@/Components/common/TradingCalculator/RiskRewardCalculator";
import TradeCalculator from "@/Components/common/TradingCalculator/TradeCalculator";
import PercentageIncreaseCalculator from "@/Components/common/TradingCalculator/PercentageIncreaseCalculator";
import CompoundInterestCalculation from "@/Components/common/TradingCalculator/CompoundInterestCalculation";

const TradeCalculators = () => {

  const metaArray = {
    noindex: true,
    title: "Trading Calculator | Financial Tools for Traders | TradeReply",
    description: "Use TradeReply's Trading Calculator to access financial tools designed to assist traders. Perform essential calculations for better trading decisions.",
    canonical_link: "https://www.tradereply.com/dashboard/trading-calculator",
    og_site_name: "TradeReply",
    og_title: "Trading Calculator | Financial Tools for Traders | TradeReply",
    og_description: "Use TradeReply's Trading Calculator to access financial tools designed to assist traders. Perform essential calculations for better trading decisions.",
    twitter_title: "Trading Calculator | Financial Tools for Traders | TradeReply",
    twitter_description: "Use TradeReply's Trading Calculator to access financial tools designed to assist traders. Perform essential calculations for better trading decisions."
  };

  return (
    <>
      <DashboardLayout>
        <MetaHead props={metaArray} />
        <main className="trade_calculators">
          <CommonHead />
          <Container>
            <div className="trade_head justify-content-center mb-0">
              <AdminHeading heading="Trading Calculators" centered />
            </div>
            <Row>
              <Col xs={12} lg={7}>
                <RiskRewardCalculator />
                <TradeCalculator />
              </Col>
              <Col xs={12} lg={5} className="mt-4 mt-lg-0">
                <PercentageIncreaseCalculator />
                <CompoundInterestCalculation />
              </Col>
            </Row>
          </Container>
        </main>
      </DashboardLayout>
    </>
  );
};

export default TradeCalculators;