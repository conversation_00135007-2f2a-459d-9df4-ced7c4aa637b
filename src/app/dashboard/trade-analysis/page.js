import MetaHead from "@/Seo/Meta/MetaHead";
import TradeAnalysisClient from "./TradeAnalysisClient";

const metaArray = {
  title: "Trade Analysis | Build Reports & Analyze Metrics | TradeReply",
  robots: "noindex, follow",
  description: "Use TradeReply's Trade Analysis tool to build custom reports and analyze trade metrics and dimensions by date for in-depth insights.",
  canonical_link: "https://www.tradereply.com/dashboard/trade-analysis",
  og_site_name: "TradeReply",
  og_title: "Trade Analysis | Build Reports & Analyze Metrics | TradeReply",
  og_description:
    "Use TradeReply's Trade Analysis tool to build custom reports and analyze trade metrics and dimensions by date for in-depth insights.",
  twitter_title: "Trade Analysis | Build Reports & Analyze Metrics | TradeReply",
  twitter_description:
    "Use TradeReply's Trade Analysis tool to build custom reports and analyze trade metrics and dimensions by date for in-depth insights.",

};

export default function page() {
  return (
    <>
      <MetaHead props={metaArray} />
      <TradeAnalysisClient />
    </>

  )

}
