import DashboardLayout from "@/Layouts/DashboardLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
const metaArray = {
    title: "Trade Replay | Visualize Trade Entries & Exits | TradeReply",
    robots: "noindex, nofollow",
    description:
        "Replay and analyze your trades with TradeReply's Trade Replay tool. Visualize your trade entries, exits, and core KPIs to optimize your strategy.",
    canonical_link: "https://www.tradereply.com/dashboard/trade-replay",
    og_site_name: "TradeReply",
    og_title: "Trade Replay | Visualize Trade Entries & Exits | TradeReply",
    og_description:
        "Replay and analyze your trades with TradeReply's Trade Replay tool. Visualize your trade entries, exits, and core KPIs to optimize your strategy.",
    twitter_title: "Trade Replay | Visualize Trade Entries & Exits | TradeReply",
    twitter_description:
        "Replay and analyze your trades with TradeReply's Trade Replay tool. Visualize your trade entries, exits, and core KPIs to optimize your strategy.",

};


export default function page() {
    return (
        <>
            <MetaHead props={metaArray} />
            <DashboardLayout>
            </DashboardLayout>
        </>

    )

}
