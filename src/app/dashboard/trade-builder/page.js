"use client";

import { Col, Container, Row } from "react-bootstrap";
import { useRouter, usePathname } from "next/navigation";
import { PlusIcon, SolidRedArrowIcon } from "@/assets/svgIcons/SvgIcon";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import CommonButton from "@/Components/UI/CommonButton";
import DashboardLayout from "@/Layouts/DashboardLayout";
import "@/css/dashboard/TradeBuilder.scss";
import TradeBuilderEntry from "@/Components/common/TradeBuilder/TradeBuilderEntry";
import TradeBuilderExit from "@/Components/common/TradeBuilder/TradeBuilderExit";
import MetaHead from "@/Seo/Meta/MetaHead";
import { get, post } from "@/utils/apiUtils";
import { useEffect, useState, useRef, Suspense } from "react";
import { useSearchParams } from "next/navigation";

const SimpleLoading = () => (
  <div className="text-center py-5">
    <div className="spinner-border" role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  </div>
);

const TradeBuilderLoading = () => <SimpleLoading />;

const TradeBuilder = () => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const tradeIdParam = searchParams.get("tradeId");
  const tradeAccountIdParam = searchParams.get("trade_account_id");
  const [tradeForms, setTradeForms] = useState([{ id: 1, type: "entry", formId: 1 }]);
  const [databasePayload, setDatabasePayload] = useState({});
  const [tradeData, setTradeData] = useState({
    entry: { overview: [], projection: [], outcome: [] },
    exit: { overview: [], projection: [], outcome: [] },
  });
  const [formData, setFormData] = useState({});
  const [transactionFields, setTransactionFields] = useState([]);
  const [tradeFields, setTradeFields] = useState([]);
  const [portfolioFields, setPortfolioFields] = useState([]);
  const [tradeId, setTradeId] = useState(null);
  const [tradeAccounts, setTradeAccounts] = useState([]);
  const [selectedTradeAccountId, setSelectedTradeAccountId] = useState(null);
  const [tradePublishStatus, setTradePublishStatus] = useState("draft");
  const [formKeyMap, setFormKeyMap] = useState({ entry_1: null });
  const [hasPublishedEntry, setHasPublishedEntry] = useState(false);
  const [entryForms, setEntryForms] = useState([]);
  const [saveStatus, setSaveStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [limitPerSubscription, setLimitPerSubscription] = useState(0);
  const [planName, setPlanName] = useState('');
  const [isError, setIsError] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchTradeAccounts = async () => {
      try {
        const response = await get("/trade-accounts");
        if (response.success) {
          setTradeAccounts(response.data || []);
          setSelectedTradeAccountId(tradeAccountIdParam || response.data[0]?.id);
        }
      } catch (err) {
        console.error("Fetch Trade Accounts Error:", err.response?.data || err.message);
      }
    };
    fetchTradeAccounts();
  }, []);

  const init = async () => {
    if (!selectedTradeAccountId) return;
    setIsLoading(true);
    setIsError(false);
    setTradeForms([]);
    setDatabasePayload({});
    setTradeData({
      entry: { overview: [], projection: [], outcome: [] },
      exit: { overview: [], projection: [], outcome: [] },
    });
    setFormData({});
    setFormKeyMap({});
    setTradePublishStatus("draft");
    setHasPublishedEntry(false);
    setEntryForms([]);
    setSaveStatus(null);
    setTradeId(null);

    let localTransactionFields = transactionFields;
    let localTradeFields = tradeFields;
    let localPortfolioFields = portfolioFields;
    if (transactionFields.length === 0 && tradeFields.length === 0 && portfolioFields.length === 0) {
      try {
        const res = await get("/trade/fetch/fields");
        localTransactionFields = res.transactions || [];
        localTradeFields = res.trades || [];
        localPortfolioFields = res.portfolios || [];
        setTransactionFields(res.transactions);
        setTradeFields(res.trades || []);
        setPortfolioFields(res.portfolios || []);
      } catch (err) {
        console.error("Fetch Fields Error in init:", err.response?.data || err.message);
        setIsError(true);
        setIsLoading(false);
        return;
      }
    }

    const localTradeIdParam = tradeIdParam;
    const localAccountId = selectedTradeAccountId;

    try {
      let newTradeId, formKeyMapTemp = {}, tradeFormsTemp = [];

      if (localTradeIdParam) {
        let tradeRes;
        try {
          tradeRes = await get(`/trade/${localTradeIdParam}/data?trade_account_id=${localAccountId}`);
        } catch (err) {
          router.push("/dashboard/trade-manager");
          return;
        }
        if (!tradeRes.success) {
          router.push("/dashboard/trade-manager");
          return;
        }
        newTradeId = tradeRes.trade.trade_id;
        tradeRes.forms.forEach((form, idx) => {
          const formKey = `${form.type}_${form.index}`;
          formKeyMapTemp[formKey] = form.form_key;
          tradeFormsTemp.push({ id: form.form_key, type: form.type, index: form.index, is_published: form.is_published, formId: form.form_key });
        });
        setTradePublishStatus(tradeRes.trade.is_published ? "published" : "draft");
        setLimitPerSubscription(tradeRes.limitPerSubscription);
        setPlanName(tradeRes.plan_name);
      } else {
        const initRes = await post("/trade/initialize", { 
          type: "entry", 
          index: 1, 
          trade_id: newTradeId ? newTradeId : null,
          trade_account_id: localAccountId,
        });
        if (!initRes.success) {
          throw new Error("Failed to initialize trade");
        }
        newTradeId = initRes.trade_id;
        formKeyMapTemp = { entry_1: initRes.form_key };
        tradeFormsTemp = [{ id: initRes.form_key, type: "entry", index: initRes.index, formId: initRes.form_key }];
        setLimitPerSubscription(initRes.limitPerSubscription);
        setPlanName(initRes.plan_name);

        const params = new URLSearchParams(searchParams.toString());
        params.set('tradeId', newTradeId);
        params.set('trade_account_id', localAccountId);
        router.replace(`${pathname}?${params.toString()}`, { scroll: false });
      }

      if (localTradeIdParam !== tradeIdParam || localAccountId !== selectedTradeAccountId) return; // Stale response, ignore

      setTradeId(newTradeId);
      setFormKeyMap(formKeyMapTemp);
      setTradeForms(tradeFormsTemp);

      const tradeRes = await get("/trade");
      const transform = (list) =>
        Array.isArray(list)
          ? list.map((item) => ({
            title: item.field_name || "",
            tooltip: item.summary || "",
            input: item.database_field || "",
            account_field: item.account_field || "",
            portfolioValue: item.portfolioValue || 0,
            is_editable: item.is_editable ?? true,
            datatype: item.datatype || "",
            metric_dimension: item.metric_dimension || "",
            expected_values: Array.isArray(item.expected_values) ? item.expected_values : [],
            has_formula: item.has_formula || false,
            is_extra: false,
            show_icon: false,
            field_behavior: item.field_behavior || {}, // Added field_behavior
          }))
          : [];

      const entryOverview = transform(tradeRes.entry_overview);
      const entryProjection = transform(tradeRes.entry_projection);
      const entryOutcome = transform(tradeRes.entry_outcome);
      const exitOverview = transform(tradeRes.exit_overview);
      const exitProjection = transform(tradeRes.exit_projection);
      const exitOutcome = transform(tradeRes.exit_outcome);

      const newTradeData = {
        entry: { overview: entryOverview, projection: entryProjection, outcome: entryOutcome },
        exit: { overview: exitOverview, projection: exitProjection, outcome: exitOutcome },
      };

      let savedFormData = {};
      if (localTradeIdParam && localAccountId) {
        const savedRes = await get(`/trade/${localTradeIdParam}/data?trade_account_id=${localAccountId}`);
        if (!savedRes.success) {
          router.push("/dashboard/trade-manager");
          return;
        }
        setLimitPerSubscription(savedRes.limitPerSubscription);
        setPlanName(savedRes.plan_name);
        savedRes.forms.forEach((form) => {
          const formType = form.type;
          const formKey = form.form_key;
          const formDataForKey = {
            overview: [...newTradeData[formType].overview],
            projection: [...newTradeData[formType].projection],
            outcome: [...newTradeData[formType].outcome],
          };
          form.sections.forEach((section) => {
            const sectionName = section.section;
            if (section.data) {
              Object.values(section.data).forEach(({ input, value, is_extra = false }) => {
                const fieldIndex = formDataForKey[sectionName].findIndex((field) => field.input === input);
                if (fieldIndex !== -1) {
                  formDataForKey[sectionName][fieldIndex] = {
                    ...formDataForKey[sectionName][fieldIndex],
                    portfolioValue: value,
                    is_extra,
                    show_icon: is_extra,
                  };
                } else if (input === "linked_entry" && formType === "exit" && sectionName === "overview") {
                  formDataForKey.overview.push({ input: "linked_entry", portfolioValue: value, is_editable: true });
                } else if (input === "transaction_comments") {
                  formDataForKey.overview.push({ input: "transaction_comments", portfolioValue: value, is_editable: true });
                } else if (input === "transaction_last_exit_price") {
                  formDataForKey.overview.push({ input: "transaction_last_exit_price", portfolioValue: value, is_editable: false });
                } else {
                  const allFields = [...localTransactionFields, ...localTradeFields, ...localPortfolioFields];
                  const fieldInfo = allFields.find(f => f.database_field === input);
                  if (fieldInfo) {
                    formDataForKey[sectionName].push({
                      title: fieldInfo.field_name || "",
                      tooltip: fieldInfo.summary || "",
                      input,
                      account_field: fieldInfo.account_field || "",
                      portfolioValue: form.is_published ? value : (fieldInfo?.portfolioValue ? fieldInfo?.portfolioValue : value),
                      is_editable: fieldInfo.is_editable ?? true,
                      datatype: fieldInfo.datatype || "",
                      metric_dimension: fieldInfo.metric_dimension || "",
                      expected_values: Array.isArray(fieldInfo.expected_values) ? fieldInfo.expected_values : [],
                      has_formula: fieldInfo.has_formula || false,
                      is_extra: true,
                      show_icon: true,
                      field_behavior: fieldInfo.field_behavior || {}, // Added field_behavior
                    });
                  }
                }
              });
            }
            if (section.formula_data) {
              formDataForKey.formulaModeInputs = section.formula_data;
            }
          });
          savedFormData[formKey] = formDataForKey;
        });
      } else {
        savedFormData = Object.keys(formKeyMapTemp).reduce((acc, formKey) => {
          const formType = formKey.split("_")[0];
          acc[formKeyMapTemp[formKey]] = {
            overview:
              formType === "entry" && newTradeData.entry.overview.length
                ? newTradeData.entry.overview
                : [{ input: "transaction_risk_percentage", title: "Risk", portfolioValue: "" }],
            projection: newTradeData[formType].projection,
            outcome: newTradeData[formType].outcome,
          };
          return acc;
        }, {});
      }

      if (localTradeIdParam !== tradeIdParam || localAccountId !== selectedTradeAccountId) return; // Stale response, ignore

      setTradeData(newTradeData);
      setFormData(savedFormData);
    } catch (err) {
      console.error("Initialize Trade Error:", err.response?.data || err.message);
      if (localTradeIdParam === tradeIdParam && localAccountId === selectedTradeAccountId) {
        setIsError(true);
      }
    } finally {
      if (localTradeIdParam === tradeIdParam && localAccountId === selectedTradeAccountId) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    if (selectedTradeAccountId) {
      init();
    }
  }, [tradeIdParam, selectedTradeAccountId]);

  useEffect(() => {
    const fetchFields = async () => {
      try {
        const res = await get("/trade/fetch/fields");
        setTransactionFields(res.transactions || []);
        setTradeFields(res.trades || []);
        setPortfolioFields(res.portfolios || []);
      } catch (err) {
        console.error("Fetch Fields Error:", err.response?.data || err.message);
      }
    };
    fetchFields();
  }, []);

  useEffect(() => {
    const entryFormsTemp = tradeForms
      .filter((form) => form.type === "entry")
      .map((form) => ({ formKey: formKeyMap[`${form.type}_${form.index}`], index: form.index, isPublished: form?.is_published }));
    setEntryForms(entryFormsTemp);
  }, [tradeForms, formKeyMap]);

  const addEntryForm = async () => {
    if (!selectedTradeAccountId) {
      alert("Please select a trade account.");
      return;
    }
    const count = tradeForms.filter((f) => f.type === "entry").length + 1;
    try {
      const res = await post("/trade/initialize", { type: "entry", index: count, trade_id: tradeId, trade_account_id: selectedTradeAccountId });
      if (res.success) {
        const newFormKey = `entry_${res.index}`;
        setFormKeyMap((prev) => ({ ...prev, [newFormKey]: res.form_key }));
        setTradeForms((prev) => [...prev, { id: res.form_key, type: "entry", index: res.index, formId: res.form_key }]);
        setFormData((prev) => ({ ...prev, [res.form_key]: { ...tradeData.entry } }));
      } else {
        alert("Failed to add entry form.");
      }
    } catch (err) {
      console.error("Add Entry Form Error:", err.response?.data || err.message);
      alert("An error occurred while adding the entry form.");
    }
  };

  const addExitForm = async () => {
    if (!selectedTradeAccountId) {
      alert("Please select a trade account.");
      return;
    }
    const count = tradeForms.filter((f) => f.type === "exit").length + 1;
    try {
      const res = await post("/trade/initialize", { type: "exit", index: count, trade_id: tradeId, trade_account_id: selectedTradeAccountId });
      if (res.success) {
        const newFormKey = `exit_${res.index}`;
        setFormKeyMap((prev) => ({ ...prev, [newFormKey]: res.form_key }));
        setTradeForms((prev) => [...prev, { id: res.form_key, type: "exit", index: res.index, formId: res.form_key }]);
        setFormData((prev) => ({ ...prev, [res.form_key]: { ...tradeData.exit } }));
      } else {
        alert("Failed to add exit form.");
      }
    } catch (err) {
      console.error("Add Exit Form Error:", err.response?.data || err.message);
      alert("An error occurred while adding the exit form.");
    }
  };

  const updateDatabasePayload = (formKey, updatedFields) => {
    setDatabasePayload(prev => ({
      ...prev,
      [formKey]: {
        ...prev[formKey],
        section: updatedFields.section,
        data: {
          ...prev[formKey]?.data,
          ...updatedFields.data,
        },
      },
    }));
  };

  const isButtonDisabled = !tradeForms.some(f => f.type === 'entry' && f.is_published);
  const handlePublishTrade = async () => {
    if (!selectedTradeAccountId) {
      alert("Please select a trade account.");
      return;
    }
    try {
      const res = await post("/trade/publish-trade", {
        tradeId: tradeId,
        trade_account_id: selectedTradeAccountId,
      });
      if (res.success) {
        setTradePublishStatus("published");
      }
    } catch (err) {
      console.error("Publish Trade Error:", err.response?.data || err.message);
      alert("Failed to publish trade.");
    }
  };

  const handleButtonClick = () => {
    if (isButtonDisabled) {
      alert('Add an entry first.\nYou must publish at least one entry form before publishing this trade.');
    } else {
      handlePublishTrade();
    }
  };

  const handleDeleteTrade = async () => {
    if (!selectedTradeAccountId) {
      alert("Please select a trade account.");
      return;
    }
    try {
      const res = await post(`/trade/${tradeId}`, {
        trade_account_id: selectedTradeAccountId,
      });
      if (res.success) {
        router.push('/dashboard/trade-manager');
      } else {
        alert(res.message || 'Failed to delete trade');
      }
    } catch (err) {
      console.error('Delete trade error', err);
      alert('Failed to delete trade');
    }
  };

  const onDeleteForm = (deletedFormKey) => {
    const deletedForm = tradeForms.find(f => formKeyMap[`${f.type}_${f.index}`] === deletedFormKey);
    if (!deletedForm) return;

    const keysToRemove = new Set();
    keysToRemove.add(deletedFormKey);

    let linkedEntryIndex = null;

    if (deletedForm.type === 'entry') {
      const entryIndex = deletedForm.index;
      tradeForms.forEach(f => {
        if (f.type === 'exit') {
          const exitFormKey = formKeyMap[`${f.type}_${f.index}`];
          const overview = formData[exitFormKey]?.overview || [];
          const hasLink = overview.some(field =>
            field.input === 'linked_entry' &&
            String(field.portfolioValue) === String(entryIndex)
          );
          if (hasLink) {
            keysToRemove.add(exitFormKey);
          }
        }
      });
    }

    if (deletedForm.type === 'exit') {
      const overview = formData[deletedFormKey]?.overview || [];
      const linkField = overview.find(f => f.input === 'linked_entry');
      if (linkField?.portfolioValue !== undefined) {
        linkedEntryIndex = linkField.portfolioValue;
      }
    }
    
  setTradeForms(prev =>
    prev.filter(f => !keysToRemove.has(formKeyMap[`${f.type}_${f.index}`]))
  );

  setFormData(prev => {
    const next = { ...prev };

    keysToRemove.forEach(key => {
      delete next[key];
    });

    if (deletedForm.type === 'exit' && linkedEntryIndex !== null) {
      const remainingExits = Object.entries(next)
        .map(([formId, formDataObj]) => {
          const formMeta = tradeForms.find(f => String(f.formId) === String(formId));
          if (
            formMeta?.type === 'exit' &&
            formDataObj.overview?.some(field =>
              field.input === 'linked_entry' &&
              String(field.portfolioValue) === String(linkedEntryIndex)
            )
          ) {
            return {
              id: formId,
              index: formMeta.index,
              price: formDataObj.overview.find(f => f.input === 'transaction_exit_price')?.portfolioValue || null
            };
          }
          return null;
        })
        .filter(f => f !== null);

      const latest = remainingExits.sort((a, b) => b.index - a.index)[0];

      const entryForm = tradeForms.find(f => f.type === 'entry' && String(f.index) === String(linkedEntryIndex));
      const entryKey = entryForm ? String(entryForm.formId) : null;

      if (entryKey && next[entryKey]) {
        next[entryKey].overview = next[entryKey].overview.map(field => {
          if (field.input === 'transaction_last_exit_price') {
            return {
              ...field,
              portfolioValue: latest?.price ?? null,
              isFormula: true
            };
          }
          return field;
        });
      }
    }

    return next;
  });

    setFormKeyMap(prev => {
      const next = { ...prev };
      Object.entries(prev).forEach(([k, v]) => {
        if (keysToRemove.has(v)) {
          delete next[k];
        }
      });
      return next;
    });
  };

  const onSaveStatusChange = (status) => {
    setSaveStatus(status);
    if (status === "success") {
      const timeoutId = setTimeout(() => {
        setSaveStatus(null);
      }, 2000);
      return () => clearTimeout(timeoutId);
    }
  };

  const handleFormPublish = (numericFormKey) => {
    setTradeForms(prev =>
      prev.map(f =>
        formKeyMap[`${f.type}_${f.index}`] === numericFormKey
          ? { ...f, is_published: true }
          : f
      )
    );
  };

  const entrydata = [
    {
      tarde: "EXIT 2",
      entrydate: "Entry Date",
      ticker: "Ticker",
      entryprice: "Entry Price",
      exitprice: "Exit Price",
      position: "Position",
      pl: "P&L$",
    },
    {
      tarde: "EXIT 1",
      entrydate: "Entry Date",
      ticker: "Ticker",
      entryprice: "Entry Price",
      exitprice: "Exit Price",
      position: "Position",
      pl: "P&L$",
    },
  ];

  const metaArray = {
    noindex: true,
    title: "Trade Builder | Manually Build Your Trades | TradeReply",
    description: "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    canonical_link: "https://www.tradereply.com/dashboard/trade-builder",
    og_site_name: "TradeReply",
    og_title: "Trade Builder | Manually Build Your Trades | TradeReply",
    og_description: "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    twitter_title: "Trade Builder | Manually Build Your Trades | TradeReply",
    twitter_description: "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
  };

  if (isLoading) {
    return <SimpleLoading />;
  }

  if (isError) {
    return (
      <div className="text-center py-5">
        <p>Failed to load trade. Please try again.</p>
        <CommonButton title="Retry" className="green-btn" onClick={init} />
      </div>
    );
  }

  return (
    <DashboardLayout>
      <MetaHead props={metaArray} />
      <div className="trade_manager trade_builder">
        <CommonHead
          isShowCalender="false"
          isDefault={saveStatus === "default"}
          isSaving={saveStatus === "loading"}
          isSuccess={saveStatus === "success"}
          isError={saveStatus === "error"}
          tradeAccounts={tradeAccounts}
          selectedTradeAccountId={selectedTradeAccountId}
          setSelectedTradeAccountId={setSelectedTradeAccountId}
        />
        <Container>
          <Row className="trade_head  pt-4 pb-6">
            <Col md={4}>
              <div className="trade_head_title">
                <h4 className="me-0 mb-0">{tradePublishStatus === "published" ? "Published" : `Trade ${tradeId}`}</h4>
              </div>
            </Col>
            <Col md={4}><AdminHeading heading="Trade Builder" centered /></Col>
            <Col md={4}>
              <div className="trade_head_btns d-sm-flex">
                <CommonButton
                  title="Delete"
                  className="red-btn me-2"
                  onClick={handleDeleteTrade}
                />
                <CommonButton title={tradePublishStatus === 'published' ? 'Published' : 'Publish'} 
                    className={
                      tradePublishStatus === "published"
                        ? "gray-btn"
                        : "green-btn"
                    }
                    onClick={handleButtonClick}
                    disabled={tradePublishStatus === 'published'}
                />
              </div>
            </Col>
          </Row>

          {tradeForms.map((form, index) => {
            console.log('index');
            console.log(form.index);
            console.log(form.type);
            const formKey = `${form.type}_${form.index}`;
            const numericFormKey = formKeyMap[formKey] || formKey;
            const statusClass = form.is_published ? "published" : "";

            return (
              <div key={form.formId}
                className={`trade_manager_trade_entry mb-4  ${statusClass}`}>
                {form.type === "entry" ? (
                  <TradeBuilderEntry
                    formKey={numericFormKey}
                    formData={formData}
                    index={form.index}
                    setFormData={setFormData}
                    transactionFields={transactionFields}
                    tradeFields={tradeFields}
                    portfolioFields={portfolioFields}
                    updateDatabasePayload={updateDatabasePayload}
                    tradeId={tradeId}
                    tradePublishStatus={tradePublishStatus}
                    onDeleteForm={onDeleteForm}
                    onSaveStatusChange={onSaveStatusChange}
                    onFormPublish={handleFormPublish}
                    selectedTradeAccountId={selectedTradeAccountId}
                    limitPerSubscription={limitPerSubscription}
                    planName={planName}
                    formId={form.formId}
                    formKeyMap={formKeyMap}
                  />
                ) : (
                  <TradeBuilderExit
                    formKey={numericFormKey}
                    formData={formData}
                    index={form.index}
                    setFormData={setFormData}
                    transactionFields={transactionFields}
                    tradeFields={tradeFields}
                    portfolioFields={portfolioFields}
                    updateDatabasePayload={updateDatabasePayload}
                    tradeId={tradeId}
                    tradePublishStatus={tradePublishStatus}
                    onDeleteForm={onDeleteForm}
                    entryForms={entryForms}
                    onSaveStatusChange={onSaveStatusChange}
                    onFormPublish={handleFormPublish}
                    selectedTradeAccountId={selectedTradeAccountId}
                    limitPerSubscription={limitPerSubscription}
                    planName={planName}
                    tradeForms={tradeForms}
                    formKeyMap={formKeyMap}
                    formId={form.formId}
                  />
                )}
              </div>
            );
          })}

          <div className="trade_manager_btns my-30">
            <Row>
              <Col lg={6}><CommonButton title="Add Entry" onlyIcon={<PlusIcon />} className="w-100" onClick={addEntryForm} /></Col>
              <Col lg={6}><CommonButton title="Add Exit" onlyIcon={<PlusIcon />} className="w-100" onClick={addExitForm} /></Col>
            </Row>
          </div>

          <div className="trade_manager_trade_entry mt-30">
            {entrydata.map((item, index) => (
              <div key={index} className={`trade_manager_trade_entry_box Redgrandient ${index === 1 ? "greengrandient" : ""}`}>
                <span className="solidArrow red_arrow me-3"><SolidRedArrowIcon /></span>
                <div className="d-flex trade_manager_trade_entry_box_headtext align-items-center w-100 justify-content-between">
                  <h5>{item.tarde}</h5>
                  <h5>{item.entrydate}</h5>
                  <h5>{item.ticker}</h5>
                  <h5>{item.entryprice}</h5>
                  <h5>{item.exitprice}</h5>
                  <h5>{item.position}</h5>
                  <h5>{item.pl}</h5>
                </div>
                <span className="solidArrow red_arrow endArrow ms-3"><SolidRedArrowIcon /></span>
              </div>
            ))}
          </div>
        </Container>
      </div>
    </DashboardLayout>
  );
};

const TradeBuilderPage = () => {
  return (
    <Suspense fallback={<TradeBuilderLoading />}>
      <TradeBuilder />
    </Suspense>
  );
};

export default TradeBuilderPage;