"use client";
import React from 'react'
import { useState, useRef, useEffect } from 'react'
import DashboardLayout from '@/Layouts/DashboardLayout'
import MetaHead from '@/Seo/Meta/MetaHead'
import "@/css/dashboard/StrategyBuilder.scss";
import { Row, Col, Container } from "react-bootstrap";
import { PlusIcon } from "@/assets/svgIcons/SvgIcon"
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import CommonButton from "@/Components/UI/CommonButton";
import InputLabel from '@/Components/UI/InputLabel';
import IncludeStrategy from '@/Components/common/StrategyBuilder/IncludeStrategy';
import ExcludeStrategy from '@/Components/common/StrategyBuilder/ExcludeStrategy';

export default function StrategyBuilder() {
    const [includeStrategies, setIncludeStrategies] = useState([{}]);
    const [excludeStrategies, setExcludeStrategies] = useState([]);
    const [strategyName, setStrategyName] = useState('');
    const [description, setDescription] = useState('');

    const MAX_NAME_LENGTH = 80;
    const MAX_DESC_LENGTH = 180;

    const nameRef = useRef(null);
    const descRef = useRef(null);

    const autoResize = (ref) => {
        if (ref.current) {
            ref.current.style.height = 'auto';
            ref.current.style.height = `${ref.current.scrollHeight}px`;
        }
    };

    useEffect(() => {
        autoResize(nameRef);
    }, [strategyName]);

    useEffect(() => {
        autoResize(descRef);
    }, [description]);

    useEffect(() => {
        const handleResize = () => {
            autoResize(nameRef);
            autoResize(descRef);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const addIncludeStrategy = () => {
        setIncludeStrategies(prev => [...prev, {}]);
    };
    const deleteIncludeStrategy = (indexToDelete) => {
        setIncludeStrategies(prev => prev.filter((_, index) => index !== indexToDelete));
    };
    const addExcludeStrategy = () => {
        setExcludeStrategies(prev => [...prev, {}]);
    };
    const deleteExcludeStrategy = (indexToDelete) => {
        setExcludeStrategies(prev => prev.filter((_, index) => index !== indexToDelete));
    };


    const metaArray = {
        noindex: true,
        title: "Strategy Builder | Manage Your Trading Strategies | TradeReply",
        description: "Manage and refine your trading strategies with TradeReply's Strategy Manager. Keep your strategies organized and effective.",
        canonical_link: "https://www.tradereply.com/dashboard/strategy-manager",
        og_site_name: "TradeReply",
        og_title: "Strategy Manager | Manage Your Trading Strategies | TradeReply",
        og_description: "Manage and refine your trading strategies with TradeReply's Strategy Manager. Keep your strategies organized and effective.",
        twitter_title: "Strategy Manager | Manage Your Trading Strategies | TradeReply",
        twitter_description: "Manage and refine your trading strategies with TradeReply's Strategy Manager. Keep your strategies organized and effective."
    };
    return (
        <>
            <DashboardLayout>
                <MetaHead props={metaArray} />
                <div className="strategy_builder">
                    <CommonHead />
                    <Container>
                        <Row className="trade_head mb-0 justify-content-between">
                            <Col md={4} xs={12}>
                                <div className="trade_head_title">
                                    <h4>Strategy 2</h4>
                                </div>
                            </Col>
                            <Col md={5} xs={12}>
                                <AdminHeading heading="Strategy Builder" centered />
                            </Col>
                            <Col md={3} xs={12}>
                                <div className="trade_head_btns">
                                    <CommonButton title="Delete" className="red-btn me-2" />
                                </div>
                            </Col>
                        </Row>
                        <div className="strategy_builder_form">
                            <div className="customInput mb-3">
                                <label className="mb-2 block">Strategy Name</label>
                                <div className="customInput_inner">
                                    <textarea
                                        ref={nameRef}
                                        rows={1}
                                        maxLength={MAX_NAME_LENGTH}
                                        className="form-control w-full resize-none overflow-hidden"
                                        placeholder=">50% D/P"
                                        value={strategyName}
                                        onChange={(e) => setStrategyName(e.target.value)}
                                    />
                                    <p className="character-count">
                                        Characters: {strategyName.length}/{MAX_NAME_LENGTH}
                                    </p>
                                </div>
                            </div>

                            <div className="customInput mb-3">
                                <label className="mb-2 block">Provide a short description of your strategy</label>
                                <div className="customInput_inner">
                                    <textarea
                                        ref={descRef}
                                        rows={2}
                                        maxLength={MAX_DESC_LENGTH}
                                        className="form-control w-full resize-none overflow-hidden"
                                        placeholder="Trades greater than 50% deviation and 50% P&L"
                                        value={description}
                                        onChange={(e) => setDescription(e.target.value)}
                                    />
                                    <p className="character-count">
                                        Characters: {description.length}/{MAX_DESC_LENGTH}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className='strategy_builder_portfolio'>
                            <div className='d-flex gap-3 align-items-center mb-2'>
                                <div className='greenCircle'></div>
                                <span>Includes 351 Transactions Within 157 Trades [15% of Portfolio]</span>
                            </div>
                            <div className='d-flex gap-3 align-items-center'>
                                <div className='redCircle'></div>
                                <span>Excludes 1990 Transactions Within 890 Trades [85% of Portfolio]</span>
                            </div>
                        </div>
                        {includeStrategies.map((_, index) => (
                            <div key={index}>
                                {index > 0 && (
                                    <div className="sectionDivider">
                                        <span>AND</span>
                                        <div className="center-line"></div>
                                    </div>
                                )}
                                <IncludeStrategy onDelete={() => deleteIncludeStrategy(index)} />
                            </div>
                        ))}

                        <div className='strategy_builder_conditionGroup'>
                            <div className="include" onClick={addIncludeStrategy}>
                                <button>
                                    <PlusIcon />
                                    <span>Add Condition Group to Include</span>
                                </button>
                            </div>
                        </div>
                        <div className="sectionDivider">
                            <div className="center-line"></div>
                        </div>
                        {excludeStrategies.map((_, index) => (
                            <div key={index}>
                                {index > 0 && (
                                    <div className="sectionDivider">
                                        <span>AND</span>
                                        <div className="center-line"></div>
                                    </div>
                                )}
                                <ExcludeStrategy onDelete={() => deleteExcludeStrategy(index)} />
                            </div>
                        ))}
                        <div className='strategy_builder_conditionGroup'>
                            <div className="exclude" onClick={addExcludeStrategy}>
                                <button >
                                    <PlusIcon />
                                    <span>Add Condition Group to Exclude</span>
                                </button>
                            </div>
                        </div>
                    </Container>
                </div>
            </DashboardLayout>
        </>
    )
}