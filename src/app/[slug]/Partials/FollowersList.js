"use client";
import React, { useState } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import { ProfileUserDarkIcon, RightArrowIconSvg } from "@/assets/svgIcons/SvgIcon";
import FollowersListPopup from "./FollowersListPopup";

export default function FollowersList({ followers = [], user }) {
  const [isFollowersListModal, setFollowersListModal] = useState(false);

  const showFollowersListModal = () => {
    setFollowersListModal(true);
  }
  const hideFollowersListModal = () => {
    setFollowersListModal(false);
  }
  const linkProps =
  {
    Linktext: "View all",
    Linkicon: <RightArrowIconSvg />,
    onClick: showFollowersListModal,
  }

  return (
    <>
      <CommonWhiteCard
        title={`Followers (${followers.length})`}
        {...linkProps}
        className="account_card"
      >
        <div className="account_card_followers">
          {followers.slice(0, 3).map((follower, index) => (
            <div className="main_inform" key={index}>
              <div className="profile_photo">
                <ProfileUserDarkIcon />
              </div>
              <a
                href={`/@${follower.username || follower.name}`}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: "#00adef",
                }}
              >
                <h6>{follower.name}</h6>
              </a>
            </div>
          ))}
        </div>
      </CommonWhiteCard>
      {isFollowersListModal && (
        <FollowersListPopup
          title={`${user.fullName} Followers`}
          followers={followers}
          closeModal={hideFollowersListModal}
        />
      )}
    </>
  );
}
