"use client";
import React, { useState } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import {
  ThumbDownIcon,
  ThumbUpIcon,
  ProfileUserDarkIcon,
  RatingStarIcon,
  RightArrowIconSvg
} from "@/assets/svgIcons/SvgIcon";
import MarketFeedListPopup from "./MarketFeedListPopUp";

export default function MarketFeedBack({ feedbackList = [] }) {
  const [isMarketListModal, setMarketListModal] = useState(false);

  const showMarketListModal = () => {
    setMarketListModal(true);
  }
  const hideMarketListModal = () => {
    setMarketListModal(false);
  }

  const renderStars = (rating) => {
    const maxStars = 5;
    const filledStars = Math.round(rating);

    return Array.from({ length: maxStars }, (_, index) =>
      index < filledStars ? (
        <RatingStarIcon key={index} />
      ) : (
        <span key={index} style={{ opacity: 0.2 }}>
          <RatingStarIcon />
        </span>
      )
    );
  };

  const linkProps =
  {
    Linktext: "View all",
    Linkicon: <RightArrowIconSvg />,
    onClick: showMarketListModal,
  }

  return (
    <>
      <CommonWhiteCard
        title="Marketplace Seller Feedback"
        {...linkProps}
        className="account_card"
      >
        <div className="account_card_marketplace">
          <div className="main_inform">
            <div className="d-flex gap-1 align-items-center">
              {renderStars(feedbackList.length)}
              <span>{feedbackList.length}</span>
            </div>
            <span className="most_recent text-end mt-2 mt-sm-0">Most recent reviews</span>
          </div>

          {feedbackList.slice(0, 3).map((feedback, index) => (
            <div className="mini_card" key={index}>
              <div className="main_inform">
                <div className="profile_photo">
                  <ProfileUserDarkIcon />
                </div>
                <div className="main_inform justify-between w-100">
                  <div>
                    <p className="small_tag mt-2 mt-sm-0">{feedback.reviewer}</p>
                    <h6>Purchased courses & webinars totalling {feedback.amount}</h6>
                  </div>
                  <div>
                    <div className="d-flex gap-1">
                      {renderStars(feedback.stars)}
                    </div>
                    <p className="time">{feedback.timeAgo}</p>
                  </div>
                </div>
              </div>
              <h6 className="mini_sc_title">
                Purchased courses & webinars totalling {feedback.amount}
              </h6>

              <div className="main_inform mt-3">
                {feedback.thumbs === "up" ? <ThumbUpIcon /> : <ThumbDownIcon />}
                <p className="thumbs_text">{feedback.message}</p>
              </div>
            </div>
          ))}
        </div>
      </CommonWhiteCard>
      {isMarketListModal && (
        <MarketFeedListPopup
          title="Marketplace Seller Feedback"
          feedbackList={feedbackList}
          closeModal={hideMarketListModal}
        />
      )}
    </>
  );
}
