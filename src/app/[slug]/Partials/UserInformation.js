"use client";
import { useRef, useState } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import { ProfileUserDarkIcon, BlueLocationIcon, } from "@/assets/svgIcons/SvgIcon";

export default function UserInformation({ user }) {

  const profileImage = user.profileImage && user.profileImage.trim() !== ""
    ? user.profileImage
    : "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-dark.svg";

  return (
    <CommonWhiteCard title="User Information" className="account_card">
      <div className="account_card_information">
        <div className="main_inform">
          <div>
            <div className="profile">
              <div className="profile_photo">
                <img
                  src={profileImage}
                  alt="Profile"
                />
              </div>
            </div>
            <button className="round-bluefill-btn mt-2" type="button">
              Follow
            </button>
          </div>
          <div className="mt-2 mt-sm-0">
            <h4>{user.fullName}</h4>
            <p className="mail">@{user.username}</p>
            <p className="location">
              <BlueLocationIcon />
              {user.location}
            </p>
            <p className="transaction">{user.transactionSummary}</p>
            <p className="active_sign">
              <span></span>Active {user.lastActive}
            </p>
            <p className="since">Member since: {user.joinedDate}</p>
          </div>
        </div>
      </div>
    </CommonWhiteCard>
  );
}
