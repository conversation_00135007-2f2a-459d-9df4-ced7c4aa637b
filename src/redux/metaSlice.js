import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  title: "Default Title",
  description: "Default description",
  keywords: "default, keywords",
};

const metaSlice = createSlice({
  name: "meta",
  initialState,
  reducers: {
    setMeta: (state, action) => {
      return { ...state, ...action.payload };
    },
  },
});

export const { setMeta } = metaSlice.actions;
export default metaSlice.reducer;
