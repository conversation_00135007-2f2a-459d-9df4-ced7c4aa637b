'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { get } from '@/utils/apiUtils';

/**
 * Custom hook to monitor authentication status with periodic heartbeat
 * Automatically detects when user is logged out from other devices
 */
export default function useAuthHeartbeat() {
  const router = useRouter();
  const intervalRef = useRef(null);
  const isActiveRef = useRef(true);
  const lastHeartbeatRef = useRef(Date.now());

  // Configuration
  const HEARTBEAT_INTERVAL = 1000; // 1 second for near-instant detection
  const MAX_RETRY_ATTEMPTS = 3;
  const RETRY_DELAY = 5000; // 5 seconds

  /**
   * Perform heartbeat check
   */
  const performHeartbeat = async () => {
    try {
      // Only perform heartbeat if user has auth token
      const token = Cookies.get("authToken");
      if (!token) {
        stopHeartbeat();
        return;
      }

      // Only perform heartbeat if tab is active and user is active
      if (!isActiveRef.current || document.hidden) {
        return;
      }

      // Make lightweight API call to verify token
      await get('auth/heartbeat');

      // Update last successful heartbeat
      lastHeartbeatRef.current = Date.now();

    } catch (error) {
      // 401 errors are handled by axios interceptor
      // Other errors we can log but don't need to handle specially
      if (error?.response?.status !== 401) {
        console.warn('Heartbeat failed:', error.message);
      }
    }
  };

  /**
   * Start heartbeat monitoring
   */
  const startHeartbeat = () => {
    // Don't start if already running
    if (intervalRef.current) {
      return;
    }

    // Only start if user has auth token
    const token = Cookies.get("authToken");
    if (!token) {
      return;
    }

    // Heartbeat monitoring started

    // Perform initial heartbeat
    performHeartbeat();

    // Set up interval
    intervalRef.current = setInterval(performHeartbeat, HEARTBEAT_INTERVAL);
  };

  /**
   * Stop heartbeat monitoring
   */
  const stopHeartbeat = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  /**
   * Handle visibility change (tab focus/blur)
   */
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // Tab is hidden, reduce activity
      isActiveRef.current = false;
    } else {
      // Tab is visible, resume activity
      isActiveRef.current = true;
      
      // If it's been a while since last heartbeat, perform one immediately
      const timeSinceLastHeartbeat = Date.now() - lastHeartbeatRef.current;
      if (timeSinceLastHeartbeat > HEARTBEAT_INTERVAL) {
        performHeartbeat();
      }
    }
  };

  /**
   * Handle user activity (mouse, keyboard, etc.)
   * Trigger immediate heartbeat on user activity for faster detection
   */
  const handleUserActivity = () => {
    isActiveRef.current = true;
    localStorage.setItem("lastActivity", Date.now().toString());

    // Trigger immediate heartbeat on user activity for faster logout detection
    const timeSinceLastHeartbeat = Date.now() - lastHeartbeatRef.current;
    if (timeSinceLastHeartbeat > 500) { // Throttle to max 2 per second
      performHeartbeat();
    }
  };

  // Set up effect
  useEffect(() => {
    // Start heartbeat monitoring
    startHeartbeat();

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Listen for window focus (when user switches back to tab)
    window.addEventListener('focus', performHeartbeat);

    // Listen for user activity (more events for faster detection)
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    activityEvents.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    // Cleanup on unmount
    return () => {
      stopHeartbeat();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', performHeartbeat);

      activityEvents.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
    };
  }, []);

  // Return control functions (optional, for manual control)
  return {
    startHeartbeat,
    stopHeartbeat,
    performHeartbeat
  };
}
