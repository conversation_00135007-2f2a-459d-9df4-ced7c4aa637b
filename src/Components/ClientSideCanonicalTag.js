'use client';
import { usePathname, useSearchParams } from 'next/navigation';
import Cookies from 'js-cookie';


const ClientSideCanonicalTag = () => {
  
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const keyValue = searchParams?.get('key');
  const isSearchPage = keyValue !== null && keyValue.trim() !== '';

  const hasKeyParam = keyValue !== null && keyValue.trim() !== '';


  // const baseUrl = environment === 'dev' ? 'https://dev.tradereply.com' : 'https://www.tradereply.com';

  // const pageParam = searchParams?.get('page');
  // const currentPage = pageParam ? parseInt(pageParam, 10) : 1;

  // const canonicalUrl = `${baseUrl}${pathname}`

  // const prevPage = currentPage > 1 ? `${baseUrl}${pathname}?page=${currentPage - 1}` : null;
  // const nextPage = `${baseUrl}${pathname}?page=${currentPage + 1}`;

  const baseUrl =
    environment === 'dev'
      ? 'https://dev.tradereply.com'
      : 'https://www.tradereply.com';


  // Extract current page from pathname (e.g. /education/page/3)
  const pathMatch = pathname?.match(/\/page\/(\d+)$/);
  const currentPage = pathMatch ? parseInt(pathMatch[1], 10) : 1;

  // Base path without /page/X
  const basePath = pathname.replace(/\/page\/\d+$/, '');

  // Canonical
  const canonicalUrl =
    currentPage === 1
      ? `${baseUrl}${basePath}`
      : `${baseUrl}${basePath}/page/${currentPage}`;

  // Prev
  const prevPage =
    currentPage > 1
      ? currentPage === 2
        ? `${baseUrl}${basePath}`
        : `${baseUrl}${basePath}/page/${currentPage - 1}`
      : null;

  // Next (you can conditionally hide this if you know totalPages)
  const nextPage = `${baseUrl}${basePath}/page/${currentPage + 1}`;
  


  const isCategoryPage = pathname?.startsWith('/category');
 
  return (
    <>
      {/* {hasKeyParam ? (
        <meta name="robots" content="noindex" />
      ) : (
        <>
          {!isCategoryPage && <link rel="canonical" href={canonicalUrl} />}
          <meta property="og:url" 
          content={canonicalUrl}
           />
          {prevPage && <link rel="prev" href={prevPage} />}
          <link rel="next" href={nextPage} />
        </>
      )} */}
      {isSearchPage ? (
        <meta name="robots" content="noindex" />
      ) : (
        <>
          {/* <link rel="canonical" href={canonicalUrl} /> */}
          <meta property="og:url" content={canonicalUrl} />
          {prevPage && <link rel="prev" href={prevPage} />}
          {/* <link rel="next" href={nextPage} /> */}
        </>
      )}
    </>
  );
};

export default ClientSideCanonicalTag;
