import "../../css/common/CommonButton.scss";

/**COMMON BUTTON WITH DYNAMIC PROPS */
/** COMMON BUTTON WITH DYNAMIC PROPS */
const CommonButton = (props) => {

  return (
    <button
      
      onClick={props?.onClick}
      type={props?.type}
      className={`btn-style ${props.className} ${props.fluid ? "w-100" : ""} ${props.transparent ? "transparent" : ""} ${props.white20 ? "white20" : ""} ${props.whiteBtn ? "white-btn" : ""}`}
      disabled={props?.disabled}
    >
      {props.onlyIcon && <span className="onlyIcon">{props.onlyIcon}</span>}

      <div className="d-flex flex-column align-items-center text-center">
        <span>{props.title}</span>
        <span className="d-block">{props.trial}</span>
        <span className="d-block">{props.subtitle}</span>
        {props.innerText && (
          <span style={{ fontSize: "0.70em", lineHeight: "1" }}>{props.innerText}</span>
        )}
      </div>

      {props.btnIcon && (
        <img
          src={props.btnIcon}
          alt={props?.title ? `${props.title} icon` : "Button icon"}
          className=""
        />
      )}
    </button>
  );
};

export default CommonButton;