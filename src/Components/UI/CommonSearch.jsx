'use client';

import { useState, useEffect } from "react";
import { SearchIcons } from "@/assets/svgIcons/SvgIcon";
import "../../css/common/CommonSearch.scss";

const CommonSearch = (props) => {
  const [inputValue, setInputValue] = useState("");

  // Keep in sync with external value when it changes
  useEffect(() => {
    if (typeof props.value === "string") {
      setInputValue(props.value);
    }
  }, [props.value, props.reset]);

  return (
    <>
      {props.label && (
        <label className="form-label" htmlFor={props.name}>
          {props.label}
        </label>
      )}
      <div className={`commonSearch ${props.className}`}>
        {props?.icon && (
          <span className="onlyIcon">
            <SearchIcons />
          </span>
        )}
        <input
          type="text"
          color="white"
          id={`${props.name}-id`}
          name={props.name}
          placeholder={props?.placeholder}
          className={`form-control ${props.searcClass}`}
          value={inputValue} // 👈 always use local state
          // onChange={(e) => {
          //   setInputValue(e.target.value);      // instant update for UX
          //   props.onChange?.(e);                // notify parent (debounced)
           
          // }}
          onChange={(e) => {
            const val = e.target.value;          // 👈 assign val here
            setInputValue(val);                   // instant update for UX
            props.onChange?.(e);                 // notify parent (debounced)
            if (val === "") {
              props.onClear?.();                 // call onClear when input is emptied
            }
          }}
          maxLength={props?.maxLength}
        />
        {props?.btnicon && (
          <span
            className="btnIcon"
            onClick={() => {
              setInputValue("");                  // reset local
              props.onChange?.({ target: { value: "" } }); // reset parent
              

            }}
          >
            <SearchIcons />
          </span>
        )}
      </div>
    </>
  );
};

export default CommonSearch;
