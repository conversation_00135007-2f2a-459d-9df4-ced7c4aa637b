import React, { useState, useEffect } from "react";
import { FaEdit, FaTrash } from "react-icons/fa";

const ListingTable = ({ array, onUpdate, onDelete, categoryFlag = false }) => {
  const [sortedArray, setSortedArray] = useState([]);
  const [sortField, setSortField] = useState(null);
  const [sortOrder, setSortOrder] = useState("asc");

  useEffect(() => {
    setSortedArray([...array]);
  }, [array]);

  const handleSort = (field) => {
    const newOrder = sortOrder === "asc" ? "desc" : "asc";
    setSortField(field);
    setSortOrder(newOrder);

    const sortedData = [...sortedArray].sort((a, b) => {
      const aValue =
        field === "primary_category.title"
          ? a?.primary_category?.title || ""
          : a[field] || "";
      const bValue =
        field === "primary_category.title"
          ? b?.primary_category?.title || ""
          : b[field] || "";

      return newOrder === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    });

    setSortedArray(sortedData);
  };

  const limitText = (text, limit = 80) => {
    if (!text) return "";
    if (text.length <= limit) return text;

    const trimmedText = text.substring(0, limit);
    const lastSpaceIndex = trimmedText.lastIndexOf(" ");

    return lastSpaceIndex > 0
      ? trimmedText.substring(0, lastSpaceIndex) + "..."
      : trimmedText + "...";
  };

  return (
    <table className="trade_manager_table">
      <thead>
        <tr>
          <th>
            <input type="checkbox" />
          </th>
          {!categoryFlag && sortedArray[0]?.primary_category && (
            <th onClick={() => handleSort("primary_category.title")}>
              Category ⬍
            </th>
          )}
          <th onClick={() => handleSort("title")}>Name ⬍</th>
          <th>Description</th>
          {sortedArray[0]?.count != null && (
            <th onClick={() => handleSort("count")}>Count ⬍</th>
          )}
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
        {sortedArray.map((item, index) => (
          <tr key={index}>
            <td>
              <input type="checkbox" />
            </td>
            {!categoryFlag && (
              <td>
                {item?.primary_category ? item.primary_category.title : "N/A"}
              </td>
            )}
            <td>{item?.title}</td>
            <td>
              <span>
                {item?.count != null
                  ? limitText(item?.content)
                  : limitText(item?.summary)}
              </span>
            </td>
            {item?.count != null && <td>{item.count}</td>}
            {/* {item?.count != null && <td>{item.count}</td>} */}
            <td>
              <button
                onClick={() => onUpdate(item)}
                style={{ marginRight: "10px", cursor: "pointer" }}
              >
                <FaEdit color="blue" size={16} />
              </button>
              <button
                onClick={() => onDelete(item?.id, item?.slug)}
                style={{ cursor: "pointer" }}
              >
                <FaTrash color="red" size={16} />
              </button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default ListingTable;
