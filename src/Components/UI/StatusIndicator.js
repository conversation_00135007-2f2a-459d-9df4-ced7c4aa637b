'use client';
import React from 'react';
import { CheckIcon, RedCircleCrossIcon, GreyCheckIcon } from '@/assets/svgIcons/SvgIcon';

/**
 * StatusIndicator Component
 * 
 * A reusable component for displaying save status in account detail forms
 * 
 * @param {Object} props
 * @param {string} props.saveStatus - Current save status: 'loading', 'success', 'error', or null/undefined for default
 * @param {string} props.error - Error message to display when saveStatus is 'error'
 * @param {string} props.loadingText - Custom text for loading state (default: "Saving...")
 * @param {string} props.successText - Custom text for success state (default: "Auto-saved")
 * @param {string} props.defaultText - Custom text for default state (default: "All changes saved")
 * @param {string} props.errorText - Custom text for error state (default: uses error prop or "Unable to save")
 * @param {string} props.className - Additional CSS classes
 */
export default function StatusIndicator({
    saveStatus,
    error,
    loadingText = "Saving...",
    successText = "Auto-saved",
    defaultText = "All changes saved",
    errorText,
    className = ""
}) {
    const getStatusContent = () => {
        switch (saveStatus) {
            case 'loading':
                return {
                    className: 'status_indicator status-loading',
                    icon: (
                        <img
                            src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-saving.svg"
                            alt="Saving Icon"
                        />
                    ),
                    text: loadingText
                };

            case 'success':
                return {
                    className: 'status_indicator status-success',
                    icon: <CheckIcon />,
                    text: successText
                };

            case 'error':
                return {
                    className: 'status_indicator status-error',
                    icon: <RedCircleCrossIcon />,
                    text: errorText || error || 'Unable to save'
                };

            default:
                return {
                    className: 'status_indicator status-default',
                    icon: <GreyCheckIcon />,
                    text: defaultText
                };
        }
    };

    const { className: statusClassName, icon, text } = getStatusContent();
    const finalClassName = `${statusClassName} ${className}`.trim();

    return (
        <div className={finalClassName}>
            {icon}
            <span>{text}</span>
        </div>
    );
}
