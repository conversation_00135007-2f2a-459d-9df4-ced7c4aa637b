"use client";

import { Col, Container, Row } from "react-bootstrap";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Cookies from "js-cookie";
// import { Logo } from "@/assets/svgIcons/SvgIcon";
import "../../css/common/Footer.scss";
import { Image } from 'next/image';
import { useEffect, useState } from "react";

const Footer = () => {
  const url = usePathname();
  const [loginToken, setLoginToken] = useState(null);
  const [isFreeUser, setIsFreeUser] = useState(false);

  useEffect(() => {
    const tokens = Cookies.get("authToken");
    setLoginToken(tokens);


    try {
      const user = JSON.parse(localStorage.getItem("user"));
      if (user?.subscription_id === 1) {
        setIsFreeUser(true);
      }
    } catch (e) {
      console.warn("Invalid user in localStorage");
    }

  }, []);

  return (
    <>
      <div className="site_footer">
        <div className="site_footer_inner">
          <Container>
            <Row className="gx-xl-5">
              <Col md={4} sm={12} xs={12}>
                <div className="site_footer_content">
                  <div className="site_footer_logo">
                    <Link prefetch={true} href="/">
                      <img src="https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg" alt="Brand Logo" />
                    </Link>
                  </div>
                  <p>
                    TradeReply is an advanced analytics suite designed for
                    crypto and stock traders to input historical trading data
                    and leverage powerful visuals, graphs, and metrics to
                    optimize and develop effective trade strategies with
                    real-time insights.
                  </p>
                </div>
              </Col>
              <Col md={8} sm={12} xs={12}>
                <Row>
                  <Col md={4} sm={4} xs={6}>
                    <div className="site_footer_links">
                      <h4>Company</h4>
                      <ul>
                        <li>
                          <Link prefetch={true} href="/help/hc/en-us/requests/new" className={url == '/help/hc/en-us/requests/new' ? 'new-link' : ''}>Contact</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/brand-assets" className={url == '/brand-assets' ? 'new-link' : ''}>Brand Assets</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/accessibility" className={url == '/accessibility' ? 'new-link' : ''}>Accessibility</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/privacy" className={url == '/privacy' ? 'new-link' : ''}>
                            Privacy Policy
                          </Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/cookies" className={url == '/cookies' ? 'new-link' : ''}>
                            Cookies Policy
                          </Link>
                        </li>
                        <li>
                          <a
                            href="#"
                            onClick={(e) => {
                              e.preventDefault(); // prevents scroll to top
                              if (typeof Osano !== "undefined" && Osano.cm) {
                                Osano.cm.showDrawer("osano-cm-dom-info-dialog-open");
                              }
                            }}
                          >
                            Cookie Settings
                          </a>
                        </li>
                        <li>
                          <Link prefetch={true} href="/terms" className={url == '/terms' ? 'new-link' : ''}>
                            Terms & Conditions
                          </Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/disclaimer" className={url == '/disclaimer' ? 'new-link' : ''}>
                            Disclaimer
                          </Link>
                        </li>
                      </ul>
                    </div>
                  </Col>

                  <Col md={4} sm={4} xs={6}>
                    <div className="site_footer_links">
                      <h4>Partners</h4>
                      <ul>
                        <li>
                          <Link prefetch={true} href="/refer-a-friend" className={url == '/refer-a-friend' ? 'new-link' : ''}>Refer a Friend</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/partner" className={url == '/partner' ? 'new-link' : ''}>Partner Program</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/advertising" className={url == '/advertising' ? 'new-link' : ''}>Advertising</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/features" className={url == '/features' ? 'new-link' : ''}>Features</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/education" className={url == '/education' ? 'new-link' : ''}>Education</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/brokers" className={url == '/brokers' ? 'new-link' : ''}>Brokers</Link>
                        </li>
                      </ul>
                    </div>
                  </Col>
                  <Col md={4} sm={4} xs={6}>
                    <div className="site_footer_links">
                      <h4>Community</h4>
                      <ul>
                        <li>
                          <Link prefetch={true} href="/help" className={url == '/helpcenter' ? 'new-link' : ''}>Help Center</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/sitemap" className={url == '/sitemap' ? 'new-link' : ''}>Sitemap</Link>
                        </li>
                        <li>
                          {/* <Link prefetch={true}
                            href={
                              typeof window !== "undefined" && sessionStorage.getItem("plan") === "Free"
                                ? "/pricing?source=free_footer_menu_pricing&feature=buy_trial"
                                : "/pricing?source=member_footer_menu_pricing&feature=buy_trial"
                            } className={url == '/pricing' ? 'new-link' : ''}>Pricing</Link> */}

                          {loginToken ? (
                           <Link
                             prefetch={true}
                             href={
                               loginToken
                                 ? isFreeUser
                                   ? "/pricing?source=free_footer_menu_pricing&feature=buy_trial"
                                   : "/pricing?source=member_footer_menu_pricing&feature=buy_trial"
                                 : "/pricing"
                             }
                             className={url == "/pricing" ? "new-link" : ""}
                           >
                             Pricing
                           </Link>

                          ) : (
                            <Link prefetch={true}
                              href="/pricing"
                              className={url == '/pricing' ? 'new-link' : ''}>Pricing</Link>
                          )}


                        </li>
                        <li>
                          <Link prefetch={true} href="/blog" className={url == '/blog' ? 'new-link' : ''}>Blog</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/status" className={url == '/status' ? 'new-link' : ''}>Status</Link>
                        </li>
                        <li>
                          <Link prefetch={true} href="/help//hc/en-us/requests/new?ticket_form_id=37293785519643" className={url == '/help//hc/en-us/requests/new?ticket_form_id=37293785519643' ? 'new-link' : ''}>Feedback/Bugs</Link>
                        </li>
                      </ul>
                    </div>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Container>
        </div>
        <div className="site_footer_copyright">
          <Container>
            <p>Copyright © 2025 TradeReply. All Rights Reserved.</p>
          </Container>
        </div>
      </div>
    </>
  );
};

export default Footer;
