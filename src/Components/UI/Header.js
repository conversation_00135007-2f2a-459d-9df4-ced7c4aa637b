"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Container, Navbar, Dropdown } from "react-bootstrap";
import { useLanguage } from "@/context/LanguageContext";
import {
  DashboardIcon,
  HelpIcon,
  PartnershipIcon,
  ReferIcon,
  SettingIcon,
  SignoutIcon,
  UserBlackIcon,
  GlobalIcons,
  UserBluekIcon
} from "@/assets/svgIcons/SvgIcon";

import CommonButton from "./CommonButton";
import NavLink from "./NavLink";
import "../../css/common/Header.scss";
import { isEmpty } from "lodash";
import Link from "next/link";
import Cookies from "js-cookie";
import { usePathname } from "next/navigation";
import { logout } from "@/utils/auth";


const Header = () => {
  const [loginToken, setLoginToken] = useState(null);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();
  const [isFreeUser, setIsFreeUser] = useState(false);
  const [isPricingPage, setIsPricingPage] = useState(false);
  const isHomePage = pathname === "/";
  // const props = usePage();
  const user = {};
  const { language, changeLanguage } = useLanguage();
  // const user = usePage().props.auth.user;
  const signIn = !isEmpty(loginToken);
  const [isActive, setIsActive] = useState(false);
  const [isProductOpen, setIsProductOpen] = useState(false);
  const [isLangOpen, setIsLangOpen] = useState(false);
  const [isOpenLanguage, setIsOpenLanguage] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);


  const ref = useRef();

  const toggleClass = () => setIsActive((prev) => !prev);

  useEffect(() => {
    const handleResize = () => {
      setIsDesktop(window.innerWidth >= 1200);
    };

    handleResize();

    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleProductDropdown = () => {
    setIsProductOpen((prev) => !prev);
  };

  const handleProductMouseEnter = () => {
    if (isDesktop) {
      setIsProductOpen(true);
    }
  };

  const handleProductMouseLeave = () => {
    if (isDesktop) {
      setIsProductOpen(false);
    }
  };

  const toggleMobileLangDropdown = () => {
    setIsLangOpen((prev) => !prev);
  };

  const toggleMobileLangEnter = () => {
    if (isDesktop) {
      setIsLangOpen(true);
    }
  }
  const toggleMobileLangLeave = () => {
    if (isDesktop) {
      setIsLangOpen(false);
    }
  }

  const toggleLanguageDropdown = () => {
    setIsOpenLanguage((prev) => !prev);
  };

  const handleLanguageMouseEnter = () => setIsOpenLanguage(true);
  const handleLanguageMouseLeave = () => setIsOpenLanguage(false);

  const handleNavClick = () => {
    if (ref.current && document.body.clientWidth < 1220) {
      ref.current.click();
    }
  };

  const logoutUser = async () => {
    // First clear all local auth data
    Cookies.remove("authToken");
    sessionStorage.clear();
    localStorage.clear();
    
    // Then call the API to logout on server
    const success = await logout();
    
    // Always redirect to login page
    router.push("/login");
  };



  const renderUserDropdown = () => <UserDropdown signIn={loginToken} />;

  const lang = [
    {
      lang: "en",
      title: "English"
    },
    {
      lang: "fr",
      title: "French"
    },
    {
      lang: "es",
      title: "Español"
    }
  ];
  const changeLang = (lang) => {
    changeLanguage(lang);
  };

  useEffect(() => {
    const tokens = Cookies.get("authToken");
    setLoginToken(tokens);


    setIsPricingPage(pathname === "/pricing");


    try {
      const user = JSON.parse(localStorage.getItem("user"));
      if (user?.subscription_id === 1) {
        setIsFreeUser(true);
      }
    } catch (e) {
      console.warn("Invalid user in localStorage");
    }

    setLoading(false);
  }, []);

  useEffect(() => {
    if (isActive) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  
    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isActive]);
  return (
    <header className={`${isHomePage ? "home-page" : ""}`}>
      <div className={`siteHeader ${isActive ? "openmenu" : ""}`}>
        <Navbar expand="xl">
          <Container>
            <div className="d-flex align-items-center ">
              <Navbar.Toggle ref={ref} onClick={toggleClass} />
              <NavLink href="/" className="brandLogo">
                {/* <Logo /> */}
                <img src="https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg" alt="Brand Logo" />
              </NavLink>
            </div>
            <Navbar.Collapse className="justify-content-center">
              <div className="d-flex justify-content-center align-items-center openmenuSidebar">
                <NavLink
                  onClick={handleNavClick}
                  href="/"
                  className="brandLogo d-block d-xl-none"
                >
                  {/* <Logo /> */}
                  <img src="https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg" alt="Brand Logo" />
                </NavLink>
                <Navbar.Toggle ref={ref} onClick={toggleClass} />
              </div>

              <div className="navMenu d-xl-flex">
                <NavLink
                  onClick={handleNavClick}
                  href="/marketplace"
                  className="nav-link"
                >
                  Marketplace
                </NavLink>

                {/* Product Dropdown */}
                <div
                  className={`nav-item common_dropdown dropdown ${isProductOpen ? "show" : ""}`}
                  onMouseEnter={handleProductMouseEnter}
                  onMouseLeave={handleProductMouseLeave}
                  onClick={toggleProductDropdown}
                >
                  <NavLink
                    className="nav-link dropdown-toggle"
                    href="#"
                    id="navbarDropdown"
                    role="button"
                    aria-haspopup="true"
                    aria-expanded={isProductOpen ? "true" : "false"}
                  >
                    Products
                  </NavLink>

                  <div
                    className={`dropdown-menu ${isProductOpen ? "show" : ""}`}
                    aria-labelledby="navbarDropdown"
                  >
                    {!isEmpty(loginToken) && (
                      <NavLink onClick={handleNavClick} href="/dashboard/trading-calculator" className="nav-link">
                        Trading Calculator
                      </NavLink>
                    )}
                    {isEmpty(loginToken) && (
                      <NavLink onClick={handleNavClick} href="/trading-calculator" className="nav-link">
                        Trading Calculators
                      </NavLink>
                    )}
                    <NavLink onClick={handleNavClick} href="/features" className="nav-link">
                      Features
                    </NavLink>
                    {loginToken ? (
                      // <Link
                      //   onClick={handleNavClick}
                      //   href={
                      //     isFreeUser ? "/pricing?source=free_header_menu_pricing&feature=buy_trial"
                      //       : "/pricing?source=member_header_menu_pricing&feature=buy_trial"
                      //   }
                      //   className="nav-link"

                      // >
                      //   Pricing
                      // </Link>
                      <a
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          handleNavClick(); // keep this if you want the sidebar to close
                          const user = JSON.parse(localStorage.getItem("user"));
                          const isFree = user?.subscription_id === 1;
                          console.log("user?.subscription_iddddddddd",user?.subscription_id);
                          
                          const targetUrl = isFree
                            ? "/pricing?source=free_header_menu_pricing&feature=buy_trial"
                            : "/pricing?source=member_header_menu_pricing&feature=buy_trial";
                          window.location.href = targetUrl;
                        }}
                        className="nav-link"
                      >
                        Pricing
                      </a>

                    ) : (
                      <NavLink onClick={handleNavClick} href="/pricing"
                        className="nav-link"
                      >
                        Pricing
                      </NavLink>
                    )}


                  </div>
                </div>

                <NavLink
                  onClick={handleNavClick}
                  href="/education"
                  className="nav-link"
                >
                  Education
                </NavLink>
                <NavLink
                  onClick={handleNavClick}
                  href="/blog"
                  className="nav-link"
                >
                  Blog
                </NavLink>

                {/* Language Dropdown */}
                <div
                  className={`nav-item common_dropdown dropdown d-block d-xl-none ${isLangOpen ? "show" : ""}`}
                  onMouseEnter={toggleMobileLangEnter}
                  onMouseLeave={toggleMobileLangLeave}
                  onClick={toggleMobileLangDropdown}
                >
                  <NavLink
                    className="nav-link dropdown-toggle"
                    href="#"
                    id="navbarDropdown"
                    role="button"
                    aria-haspopup="true"
                    aria-expanded={isLangOpen ? "true" : "false"}
                  >
                    <span className="globalIcon">
                      <GlobalIcons />
                    </span>
                    <p className="text-capitalize fs-5 ms-2">{language}</p>
                  </NavLink>

                  <div
                    className={`dropdown-menu ${isLangOpen ? "show" : ""}`}
                    aria-labelledby="navbarDropdown"
                  >
                    {lang.map((Language) => (
                      <NavLink
                        onClick={() => changeLang(Language.lang)}
                        key={Language.lang}
                        href=""
                        className="nav-link text-white d-flex flex-column gap-3 fs-5 fw-bold"
                      >
                        {Language.title}
                      </NavLink>
                    ))}
                  </div>
                </div>

                <div className='d-block d-xl-none'>
                  {/* <UserDropdown /> */}
                  {signIn ? (
                    <>
                      <Link href="" className='nav-link'><span className='me-3' onClick={() => logoutUser()}><SignoutIcon /></span> Sign Out</Link>
                      {/* <NavLink href="/account-overview" className='dropdown-item white_icon'><SettingIcon /> Account Settings</NavLink> */}
                    </>
                  ) : (
                    <Link href="/login" className='nav-link'><span className='me-3'><SignoutIcon /></span> Log In</Link>
                  )}

                  <Link href="/help" className='nav-link'><span className='me-3'><HelpIcon /></span> Help Center</Link>
                  <NavLink href="/partner" className='nav-link white_stroke_icon'><span className='me-3'><PartnershipIcon /></span> Partnership</NavLink>
                  <NavLink href="/refer-a-friend" className='nav-link'><span className='me-3'><ReferIcon /></span> Refer A Friend</NavLink>
                </div>
              </div>
            </Navbar.Collapse>

            {isActive && (
              <div
                onClick={handleNavClick}
                className="sidebar_backdrop d-xl-none"
              />
            )}
            {/* Language Desktop */}
            <div className="languageDropdown d-none d-xl-flex">
              <div
                className={`nav-item common_dropdown dropdown ${isOpenLanguage ? "show" : ""}`}
                onMouseEnter={handleLanguageMouseEnter}
                onMouseLeave={handleLanguageMouseLeave}
                onClick={toggleLanguageDropdown}
              >
                <NavLink
                  className="nav-link dropdown-toggle"
                  href="#"
                  id="navbarDropdown"
                  role="button"
                  aria-haspopup="true"
                  aria-expanded={isOpenLanguage ? "true" : "false"}
                >
                  <div className={`globalIcon ${isOpenLanguage ? "active" : ""}`}>
                    <img
                      src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-global.svg"
                      alt="Global Icon Black"
                      className="icon black"
                    />
                    <img
                      src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-brand-blue-global.svg"
                      alt="Global Icon Blue"
                      className="icon blue"
                    />
                  </div>
                  <p className="text-capitalize fs-5 ms-2">{language}</p>
                </NavLink>

                <div
                  className={`dropdown-menu ${isOpenLanguage ? "show" : ""}`}
                  aria-labelledby="navbarDropdown"
                >
                  {lang.map((Language) => (
                    <NavLink
                      onClick={() => changeLang(Language.lang)}
                      key={Language.lang}
                      href=""
                      className="nav-link text-white d-flex flex-column gap-3 p-2 px-3 fs-5 fw-bold"
                    >
                      {Language.title}
                    </NavLink>
                  ))}
                </div>
              </div>
            </div>
            <div className="mx-2 mx-xl-4 d-none d-xl-block">
              {renderUserDropdown()}
            </div>
            {!loading && (
              <>
                {!loginToken && pathname !== "/pricing" && (
                  <Link href="/pricing">
                    <CommonButton className="gradient-btn" title="Get started" />
                  </Link>
                )}

                {loginToken && isFreeUser && !isPricingPage && (
                  <Link href="/pricing?source=free_header_upgrade_button&feature=buy_trial">
                    <button className="btn-style gradient-btn ">
                      <div className="d-flex flex-column align-items-center text-center gap-1">
                        <span style={{ lineHeight: "1" }} >Upgrade Now</span>
                        <span style={{ fontSize: "0.70em", lineHeight: "1" }}>30-Day Free Trial</span>
                      </div>
                    </button>
                  </Link>
                )}
              </>
            )}

          </Container>
        </Navbar >
      </div>
    </header >
  );
};

const UserDropdown = ({ signIn }) => {
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);

  const router = useRouter();

  const logoutUser = async () => {
    // First clear all local auth data
    Cookies.remove("authToken");
    sessionStorage.clear();
    localStorage.clear();
    
    // Then call the API to logout on server
    const success = await logout();
    
    // Always redirect to login page
    router.push("/login");
  };


  return (
    <Dropdown
      align="end"
      className="common_dropdown userDropdown"
      show={isUserDropdownOpen}
      onToggle={(isOpen) => setIsUserDropdownOpen(isOpen)}
    >
      <Dropdown.Toggle variant="" id="dropdown-basic">
        <span className="user_icon">
          {isUserDropdownOpen ? <UserBluekIcon /> : <UserBlackIcon />}
        </span>
        <span className="user_name"></span>
      </Dropdown.Toggle>
      <Dropdown.Menu>
        {signIn ? (
          <>
            <NavLink
              href="/dashboard"
              className="dropdown-item white_icon flex items-center"
            >
              <DashboardIcon color="image_color_to_white" /> Dashboard
            </NavLink>
            <NavLink
              href="/help"
              className="dropdown-item white_icon flex items-center"
            >
              <HelpIcon /> Help Center
            </NavLink>
            <NavLink
              href="/account/overview"
              className="dropdown-item white_icon flex items-center"
            >
              <SettingIcon color="image_color_to_white" /> Account Settings
            </NavLink>
            <NavLink
              href="/partner"
              className="dropdown-item white_stroke_icon flex items-center"
            >
              <PartnershipIcon /> Partnership
            </NavLink>
            <NavLink
              href="/refer-a-friend"
              className="dropdown-item white_icon flex items-center"
            >
              <ReferIcon /> Refer A Friend
            </NavLink>
            <NavLink
              href="/login"
              onClick={() => { logoutUser() }}
              className="dropdown-item white_icon flex items-center"
            >
              <SignoutIcon /> Log Out
            </NavLink>
          </>
        ) : (
          <>
            <NavLink href="/login" className="dropdown-item d-flex align-items-center">
              <SignoutIcon /> Log In
            </NavLink>
            <NavLink href="/help" className="dropdown-item d-flex align-items-center">
              <HelpIcon /> Help Center
            </NavLink>
            <NavLink href="/partner" className="dropdown-item d-flex align-items-center white_stroke_icon">
              <PartnershipIcon /> Partnership
            </NavLink>
            <NavLink href="/refer-a-friend" className="dropdown-item d-flex align-items-center">
              <ReferIcon /> Refer A Friend
            </NavLink>
          </>
        )}
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default Header;
