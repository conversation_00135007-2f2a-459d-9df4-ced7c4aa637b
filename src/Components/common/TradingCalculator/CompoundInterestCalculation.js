"use client";
import React, { useState } from "react";
import { Col, Row } from "react-bootstrap";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";

export default function CompoundInterestCalculation() {
    const [accountSize, setAccountSize] = useState("");
    const [days, setDays] = useState("");
    const [interest, setInterest] = useState("");
    const [compoundedSize, setCompoundedSize] = useState("$0.00");

    const formatCurrency = (value) => {
        const num = parseFloat(value);
        return isNaN(num) ? "" : `$${num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    };

    const validateInterestInput = (value) => {
        let sanitizedValue = value.replace(/[^0-9.]/g, "");

        const firstPeriodIndex = sanitizedValue.indexOf(".");
        if (firstPeriodIndex !== -1) {
            sanitizedValue =
                sanitizedValue.slice(0, firstPeriodIndex + 1) +
                sanitizedValue.slice(firstPeriodIndex + 1).replace(/\./g, "");
        }

        const parts = sanitizedValue.split(".");
        parts[0] = parts[0].slice(0, 5);

        if (parts[1]) {
            parts[1] = parts[1].slice(0, 2);
        }

        return parts.join(".");
    };
    const validateAccountSizeInput = (value) => {
        let sanitizedValue = value.replace(/[^0-9.]/g, "");

        const firstPeriodIndex = sanitizedValue.indexOf(".");
        if (firstPeriodIndex !== -1) {
            sanitizedValue =
                sanitizedValue.slice(0, firstPeriodIndex + 1) +
                sanitizedValue.slice(firstPeriodIndex + 1).replace(/\./g, "");
        }

        const parts = sanitizedValue.split(".");
        parts[0] = parts[0].slice(0, 9);

        if (parts[1]) {
            parts[1] = parts[1].slice(0, 2);
        }

        return parts.join(".");
    };

    const calculateCompoundedAccountSize = (account, rate, day) => {
        const accountNum = parseFloat(account);
        const interestNum = parseFloat(rate) / 100;
        const daysNum = parseInt(day, 10);

        if (!isNaN(accountNum) && !isNaN(interestNum) && !isNaN(daysNum) && daysNum >= 0) {
            const result = accountNum * Math.pow(1 + interestNum, daysNum);
            setCompoundedSize(formatCurrency(result));
        } else {
            setCompoundedSize("$0.00");
        }
    };

    return (
        <>
            <div className="trade_calculators_card mt-20">
                <div className="trade_head mb-0 py-3 pb-4">
                    <h6>Daily Compound Interest Calculator</h6>
                    <CommonTooltip
                        className="CustomTooltip"
                        content="See how your balance grows over time with daily compounding. Enter the initial amount, daily return rate, and number of days to calculate your future balance. Ideal for tracking investment growth and compounding strategies."
                        position="top-left"
                    >
                        <SolidInfoIcon />
                    </CommonTooltip>
                </div>
                <Row className="gx-2 gx-xl-3">
                    <Col xs={4}>
                        <div className="tradeacct">
                            <h6>Account Size</h6>
                            <input
                                type="text"
                                placeholder="$1,000,000.00"
                                className="w-full"
                                value={accountSize === "" ? "" : `$${accountSize}`}
                                onChange={(e) => {
                                    const numericValue = validateAccountSizeInput(e.target.value);
                                    setAccountSize(numericValue);
                                    calculateCompoundedAccountSize(numericValue, interest, days);
                                }}
                            />
                        </div>
                    </Col>

                    <Col xs={4}>
                        <div className="tradeacct">
                            <h6>Day</h6>
                            <input
                                type="text"
                                placeholder="365"
                                className="w-full"
                                value={days}
                                onChange={(e) => {
                                    const numericValue = e.target.value.replace(/\D/g, "").slice(0, 5); // Only numbers, max 5 digits
                                    setDays(numericValue);
                                    calculateCompoundedAccountSize(accountSize, interest, numericValue);
                                }}
                            />
                        </div>
                    </Col>

                    <Col xs={4}>
                        <div className="tradeacct">
                            <h6>Interest</h6>
                            <input
                                type="text"
                                placeholder="2.00%"
                                className="w-full"
                                value={interest}
                                onChange={(e) => {
                                    let numericValue = e.target.value.replace(/[^0-9.]/g, "");
                                    numericValue = validateInterestInput(numericValue);
                                    setInterest(numericValue);
                                    calculateCompoundedAccountSize(accountSize, numericValue, days);
                                }}
                                onBlur={() => {
                                    if (interest) setInterest((prev) => `${prev}%`);
                                }}
                                onFocus={() => {
                                    setInterest((prev) => prev.replace("%", ""));
                                }}
                            />
                        </div>
                    </Col>

                    <Col xs={12} className="mt-10">
                        <div className="tradeacct value_change">
                            <h6>Compounded Account Size</h6>
                            <p className="wrap_value">
                                {compoundedSize}
                            </p>
                        </div>
                    </Col>
                </Row>
            </div>
        </>
    );
}