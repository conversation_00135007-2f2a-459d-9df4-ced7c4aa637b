import React from 'react'
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";

export default function RiskRewardCalculator() {
    const riskdata = [
        {
            title: "1X (1:1)",
            text: "Not Profitable",
            text2: "Not Profitable",
            text3: "Not Profitable",
            text4: "Break Even",
            text5: "Profitable"
        },
        {
            title: "2X (2:1)",
            text: "Not Profitable",
            text2: "Not Profitable",
            text3: "Profitable",
            text4: "Profitable",
            text5: "Profitable"
        },
        {
            title: "3X (3:1)",
            text: "Not Profitable",
            text2: "Profitable",
            text3: "Profitable",
            text4: "Profitable",
            text5: "Profitable"
        },
        {
            title: "4X (4:1)",
            text: "Break Even",
            text2: "Profitable",
            text3: "Profitable",
            text4: "Profitable",
            text5: "Profitable"
        },
        {
            title: "5X (5:1)",
            text: "Profitable",
            text2: "Profitable",
            text3: "Profitable",
            text4: "Profitable",
            text5: "Profitable"
        }
    ];
    return (
        <>
            <div className="trade_calculators_card">
                <div className="trade_head mb-0 py-3 pb-4">
                    <h6>Risk / Reward calculator</h6>
                    <CommonTooltip
                        className="CustomTooltip"
                        content="This tool helps assess your required risk/reward ratio based on win rate. For example, a 20% win rate needs a 4:1 ratio to break even, while a 50% win rate requires 1:1. Use this to determine if your trading strategy is mathematically profitable."
                        position="top-left"
                    >
                        <SolidInfoIcon />
                    </CommonTooltip>
                </div>
                <div className="trade_calculators_card_risk">
                    <div className="mob_winrate">
                        <h5>Win Rate</h5>
                    </div>
                    <ul className="risk_thead">
                        <li>
                            <span className="risk_text">Risk/Reward</span>{" "}
                            <span className="rr_text">R:R</span>
                        </li>
                        <li>
                            20% <span className="win_R">(Win Rate)</span>
                        </li>
                        <li>
                            30% <span className="win_R">(Win Rate)</span>
                        </li>
                        <li>
                            40% <span className="win_R">(Win Rate)</span>
                        </li>
                        <li>
                            50% <span className="win_R">(Win Rate)</span>
                        </li>
                        <li>
                            60% <span className="win_R">(Win Rate)</span>
                        </li>
                    </ul>
                    <div className="risk_tboday">
                        {riskdata.map((item, index) => (
                            <ul key={index}>
                                <li>{item.title}</li>
                                <li>
                                    <h6 className={`${item.text === "Profitable" ? "greenlight_text" : ""} ${item.text === "Not Profitable" ? "redlight_text" : ""} ${item.text === "Break Even" ? "yellowlight_text" : ""}`}>
                                        <span>{item.text}</span>
                                    </h6>
                                </li>
                                <li>
                                    <h6
                                        className={`
                                                        ${item.text2 ===
                                                "Profitable"
                                                ? "greenlight_text"
                                                : ""
                                            } 
                                                        ${item.text2 ===
                                                "Not Profitable"
                                                ? "redlight_text"
                                                : ""
                                            } 
                                                        ${item.text2 ===
                                                "Break Even"
                                                ? "yellowlight_text"
                                                : ""
                                            }
                                                    `}
                                    >
                                        <span>{item.text2}</span>
                                    </h6>
                                </li>
                                <li>
                                    <h6
                                        className={`
                                                        ${item.text3 ===
                                                "Profitable"
                                                ? "greenlight_text"
                                                : ""
                                            } 
                                                        ${item.text3 ===
                                                "Not Profitable"
                                                ? "redlight_text"
                                                : ""
                                            } 
                                                        ${item.text3 ===
                                                "Break Even"
                                                ? "yellowlight_text"
                                                : ""
                                            }
                                                    `}
                                    >
                                        <span>{item.text3}</span>
                                    </h6>
                                </li>
                                <li>
                                    <h6
                                        className={`
                                                        ${item.text4 ===
                                                "Profitable"
                                                ? "greenlight_text"
                                                : ""
                                            } 
                                                        ${item.text4 ===
                                                "Not Profitable"
                                                ? "redlight_text"
                                                : ""
                                            } 
                                                        ${item.text4 ===
                                                "Break Even"
                                                ? "yellowlight_text"
                                                : ""
                                            }
                                                    `}
                                    >
                                        <span>{item.text4}</span>
                                    </h6>
                                </li>
                                <li>
                                    <h6
                                        className={`
                                                        ${item.text5 ===
                                                "Profitable"
                                                ? "greenlight_text"
                                                : ""
                                            } 
                                                        ${item.text5 ===
                                                "Not Profitable"
                                                ? "redlight_text"
                                                : ""
                                            } 
                                                        ${item.text5 ===
                                                "Break Even"
                                                ? "yellowlight_text"
                                                : ""
                                            }
                                                    `}
                                    >
                                        <span>{item.text5}</span>
                                    </h6>
                                </li>
                            </ul>
                        ))}
                    </div>
                    <div className="risktext  mt-1">
                        <h5 className="red_text">
                            <span className="red_bg"></span> Not Profitable
                        </h5>
                        <h5 className="yellowlight_text">
                            <span className="yellow_bg"></span> Break Even
                        </h5>
                        <h5 className="greenlight_text">
                            <span className="green_bg"></span> Profitable
                        </h5>
                    </div>
                    <h6 className="mt-2">
                        A 5X risk/reward means your profit is 5X your risk. Win just 2 out of 10 trades, and you're profitable. At 3X, you only need 3/10 wins. Higher R:R reduces the pressure to be right—focus on managing risk, not chasing perfect trades.
                    </h6>
                </div>
            </div>
        </>
    )
}
