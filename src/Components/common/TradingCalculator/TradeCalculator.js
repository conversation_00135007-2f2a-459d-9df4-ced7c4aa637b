"use client";
import React, { useState, useEffect } from "react";
import { Col, Row } from "react-bootstrap";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";
import { Vast_Shadow } from "next/font/google";

export default function TradeCalculator() {
    const [entry, setEntry] = useState("");
    const [target, setTarget] = useState("");
    const [stopLoss, setStopLoss] = useState("");
    const [availableToTrade, setAvailableToTrade] = useState("");
    const [riskReward, setRiskReward] = useState("");
    const [riskPerShare, setRiskPerShare] = useState("");
    const [profitPerShare, setProfitPerShare] = useState("");
    const [tradeType, setTradeType] = useState("");

    const handleNumericInput = (value, setValue) => {
        let formattedValue = value.replace(/[^0-9.]/g, "");
        formattedValue = formattedValue.replace(/^0+(?=\d)/, "");

        const parts = formattedValue.split(".");
        if (parts.length > 2) return;

        parts[0] = parts[0].slice(0, 9); // Restrict to 9 digits before decimal
        if (parts[1]) parts[1] = parts[1].slice(0, 3); // Restrict to 2 digits after decimal

        setValue(parts.join("."));
    };

    const getNumericValue = (value) => parseFloat(value) || 0;

    const determineTradeType = (entry, target) => {
        if (entry < target) {
            return "LONG";
        } else if (entry > target) {
            return "SHORT";
        } else {
            return "BREAKEVEN"; // If Entry = Target, the trade is breakeven
        }
    }

    const calculateRiskReward = (entry, target, stoploss) => {
        if (isNaN(entry) || isNaN(target) || isNaN(stoploss)) {
            return "";
        }
        const tradeType = determineTradeType(entry, target);

        if (tradeType === "BREAKEVEN") {
            return "NO TRADE SETUP"; // If Entry = Target, no trade setup exists
        }

        if (tradeType === "LONG") {
            if (stoploss > entry) {
                return "DECREASE STOP LOSS";
            } else if (stoploss === entry) {
                return "NO DEFINED RISK";
            }
        } else if (tradeType === "SHORT") {
            if (stoploss < entry) {
                return "INCREASE STOP LOSS";
            } else if (stoploss === entry) {
                return "NO DEFINED RISK";
            }
        }

        // Calculate Risk/Reward for valid setups
        let riskReward;
        if (tradeType === "LONG") {
            riskReward = (target - entry) / (entry - stoploss);
        } else if (tradeType === "SHORT") {
            riskReward = (entry - target) / (stoploss - entry);
        }

        return `${riskReward.toFixed(1)}X`; // Return formatted risk/reward ratio
    };

    useEffect(() => {
        const numEntry = getNumericValue(entry);
        const numTarget = getNumericValue(target);
        const numStopLoss = getNumericValue(stopLoss);

        setTradeType(determineTradeType(numEntry, numTarget));
        setRiskReward(calculateRiskReward(numEntry, numTarget, numStopLoss));
        setRiskPerShare(Math.abs(numEntry - numStopLoss).toFixed(3));
        setProfitPerShare(Math.abs(numTarget - numEntry).toFixed(3));
    }, [entry, target, stopLoss]);

    const getRiskRewardClass = (value) => {
        if (value === "NO DEFINED RISK" || value === "NO TRADE SETUP" || value === "BREAKEVEN" || value === "INCREASE STOP LOSS" || value === "DECREASE STOP LOSS") {
            return "trade_yellow_bg";
        }
        if (value === "LOSS" || value === "INVALID TRADE") {
            return "trade_red_bg";
        }
        if (/^\d+(\.\d+)?X$/.test(value)) {
            return "trade_green_bg";
        }
        return "trade_white_bg";
    };

    // Position Size Formulas
    const riskAmounts = [5, 25, 50, 100, 150, 250, 500, 750, 1000, 1500, 2500, 3500, 5000, 10000, 25000];

    const calculateRiskPercent = (riskAmount, availableToTrade) => {
        return availableToTrade ? ((riskAmount / availableToTrade) * 100).toFixed(2) + "%" : "0.00%";
    };

    const calculateQty = (riskAmount, riskPerShare) => {
        return riskPerShare ? (riskAmount / riskPerShare).toFixed(2) : 0;
    };

    const calculatePosition = (entry, qty) => {
        return (entry * qty).toFixed(0);
    };

    const calculateProfit = (qty, entry, target) => {
        return (qty * Math.abs(entry - target)).toFixed(0);
    };

    const calculateEstActVal = (availableToTrade, profit) => {
        return (availableToTrade + profit).toFixed(2);
    };

    return (
        <>
            <div className="trade_calculators_card mt-20">
                {/* Trade Calculator */}
                <div>
                    <div className="trade_head mb-0 py-3 pb-4">
                        <h6>Trade calculator</h6>
                        <CommonTooltip
                            className="CustomTooltip"
                            content="Enter your available trading capital, entry price, stop loss, and target price to calculate trade outcomes. This tool estimates potential risk, reward, and profit/loss to help you make informed trading decisions."
                            position="top-left">
                            <SolidInfoIcon />
                        </CommonTooltip>
                    </div>
                    {[
                        { label: "ENTRY", state: entry, setter: setEntry },
                        { label: "TARGET", state: target, setter: setTarget },
                        { label: "STOP LOSS", state: stopLoss, setter: setStopLoss },
                        { label: "AVAILABLE TO TRADE", state: availableToTrade, setter: setAvailableToTrade },
                    ].map(({ label, state, setter }, index) => (
                        <Row key={index}>
                            <Col xs={7} md={6}><div className="trade_calculators_card_tradecal_tradebox"><h6>{label}</h6></div></Col>
                            <Col xs={5} md={6}>
                                <div className="trade_calculators_card_tradecal_tradebox1">
                                    <input
                                        type="text"
                                        placeholder="$0.00"
                                        value={state ? `$${state}` : ""}
                                        onChange={(e) => handleNumericInput(e.target.value.replace("$", ""), setter)}
                                        className="w-full"
                                    />
                                </div>
                            </Col>
                        </Row>
                    ))}

                    {[
                        { label: "TRADE TYPE", value: tradeType },
                        { label: "RISK/REWARD", value: riskReward, className: getRiskRewardClass(riskReward) },
                        { label: "RISK PER SHARE", value: `$${riskPerShare}` },
                        { label: "PROFIT PER SHARE", value: `$${profitPerShare}` }
                    ].map(({ label, value, className = "whitelight_bg" }, index) => (
                        <Row key={index}>
                            <Col xs={7} md={6}><div className="trade_calculators_card_tradecal_tradebox"><h6>{label}</h6></div></Col>
                            <Col xs={5} md={6}><div className={`trade_calculators_card_tradecal_tradebox1 ${className}`}><h6>{value}</h6></div></Col>
                        </Row>
                    ))}
                </div>
                <div className="trade_calculators_card_divider"></div>
                <div className="trade_calculators_card mt-20">
                    <div className="trade_head mb-0 py-3 pb-4">
                        <h6>Position Size</h6>
                        <CommonTooltip
                            className="CustomTooltip"
                            content="This tool pulls data from the Trade Calculator to determine your position size, risk, trade value, quantity, profit, and estimated account balance after the trade. Use it to optimize risk management and trade sizing."
                            position="top-left"
                        >
                            <SolidInfoIcon />
                        </CommonTooltip>
                    </div>
                    <div className="trade_calculators_card_risk position_size">
                        <ul className="risk_thead">
                            <li><h6>ACT RISK %</h6></li>
                            <li><h6>RISK $</h6></li>
                            <li><h6>POSITION</h6></li>
                            <li><h6>QTY</h6></li>
                            <li><h6>PROFIT</h6></li>
                            <li><h6>EST.ACT VAL</h6></li>
                        </ul>
                        <div className="risk_tboday">
                            {riskAmounts.map((riskAmount, index) => {
                                const riskPercent = calculateRiskPercent(riskAmount, getNumericValue(availableToTrade));
                                const qty = calculateQty(riskAmount, getNumericValue(riskPerShare));
                                const position = calculatePosition(getNumericValue(entry), qty);
                                const profit = calculateProfit(qty, getNumericValue(entry), getNumericValue(target));
                                const estActVal = calculateEstActVal(getNumericValue(availableToTrade), getNumericValue(profit));

                                return (
                                    <ul key={index}>
                                        <li><h6>{riskPercent}</h6></li>
                                        <li><h6>${riskAmount}</h6></li>
                                        <li><h6>${position}</h6></li>
                                        <li><h6>{qty}</h6></li>
                                        <li><h6>${profit}</h6></li>
                                        <li><h6>${estActVal}</h6></li>
                                    </ul>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}