import { v4 as uuidv4 } from "uuid";

const ThirdPartyLogin = ({ type }) => {
  const handleThirdPartyLogin = (provider) => {
    if (type === "signup") {
      const sessionKey = `signup_${provider}`;
      let data = JSON.parse(sessionStorage.getItem(sessionKey) || "{}");

      if (!data.uuid) {
        const newUuid = uuidv4();
        const expiresInMinutes = 15;
        const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;

        data = {
          uuid: newUuid,
          expiresAt,
        };

        sessionStorage.setItem(sessionKey, JSON.stringify(data));
      }

      const uuid = data.uuid;
      
      // Redirect to backend with UUID in query
      window.location.href = `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/${provider}/registerationRedirect?uuid=${uuid}`;

    } else {
      window.location.href = `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/${provider}/loginRedirect`;
    }
  }

  return (
    <div className="thirdParty_login d-flex justify-content-center">
      <button onClick={() => handleThirdPartyLogin("google")} className="thirdParty_login_btn">
        <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-google-icon.svg" alt="Continue with Google" />
      </button>
      <button onClick={() => handleThirdPartyLogin("facebook")} className="thirdParty_login_btn">
        <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-facebook-icon.svg" alt="Continue with Facebook" />
      </button>
      <button onClick={() => handleThirdPartyLogin("apple")} className="thirdParty_login_btn" disabled>
        <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-apple-icon.svg" alt="Continue with Apple" />
      </button>
    </div>
  );
};

export default ThirdPartyLogin;
