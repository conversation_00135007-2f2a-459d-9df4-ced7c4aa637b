"use client";
import { useState, useEffect } from "react";

const Typewriter = ({ words, period = 2000 }) => {
  const [text, setText] = useState(words?.[0]?.head || "Stock");
  const [body, setBody] = useState(words?.[0]?.body || "Trade Analysis");
  const [index, setIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [typingSpeed, setTypingSpeed] = useState(250);

  useEffect(() => {
    if (!words || words.length === 0) return;

    const currentWord = words[index];
    let timeout;

    if (isDeleting) {
      setTypingSpeed(75);
      timeout = setTimeout(() => {
        setText((prev) => prev.slice(0, -1));
        setBody((prev) => prev.slice(0, -1));
      }, typingSpeed);
    } else {
      timeout = setTimeout(() => {
        setText((prev) => currentWord.head.slice(0, prev.length + 1));
        setBody((prev) => currentWord.body.slice(0, prev.length + 1));
      }, typingSpeed);
    }

    if (!isDeleting && text === currentWord.head) {
      timeout = setTimeout(() => setIsDeleting(true), period);
    } else if (isDeleting && text === "") {
      setIsDeleting(false);
      setIndex((prev) => (prev + 1) % words.length);
    }

    return () => clearTimeout(timeout);
  }, [text, isDeleting, index, words, period, typingSpeed]);

  return (
    <>
      <h1 className="typewrite d-flex justify-content-center">
        <div style={{ display: "none" }} aria-hidden="true">
          <span>Stock Trade Analysis</span>
          <span>Stock Trade Analyzer</span>
          <span>Stock Trade Analyzed</span>
          <span>Crypto Trade Analysis</span>
          <span>Crypto Trade Analyzer</span>
          <span>Crypto Trade Analyzed</span>
        </div>
        <span className="txt-rotate d-flex">
          {text}
          <span className="caret">|</span>
          <span id="prefix"></span>&nbsp;Trade Analy
          <span id="suffix" style={{ color: "#00ADEF" }}>
            {body.slice(-3)}
          </span>
        </span>
        <span className="caret">|</span>
      </h1>
    </>
  );
};

export default Typewriter;
