'use client';

import { useState, useRef, useEffect } from 'react';
import { WhiteDownArrow, SearchIcons } from '@/assets/svgIcons/SvgIcon';

export default function CustomDropdown({
    options = [],
    defaultValue = 'Select an option',
    optionLabelKey = 'label',
    onSelect,
    showSearch = true,
}) {
    const [isOpen, setIsOpen] = useState(false);
    const [selected, setSelected] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const dropdownRef = useRef(null);

    const toggleDropdown = () => setIsOpen(!isOpen);

    const handleSelect = (option) => {
        const label = option[optionLabelKey];
        setSelected(label);
        onSelect && onSelect(option);
        setIsOpen(false);
        setSearchTerm('');
    };

    const filteredOptions = options.filter((option) =>
        option[optionLabelKey].toLowerCase().includes(searchTerm.toLowerCase())
    );

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);

            setTimeout(() => {
                const dropdownElement = dropdownRef.current;
                if (dropdownElement) {
                    const rect = dropdownElement.getBoundingClientRect();
                    const dropdownBottom = rect.bottom;
                    const viewportHeight = window.innerHeight;

                    if (dropdownBottom > viewportHeight) {
                        window.scrollBy({
                            top: dropdownBottom - viewportHeight + 10,
                            behavior: 'smooth',
                        });
                    }
                }
            }, 0);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);

    return (
        <div className="account-custom-select" ref={dropdownRef}>
            <div className="header" onClick={toggleDropdown}>
                <span>{selected || defaultValue}</span>
                <WhiteDownArrow />
            </div>

            {isOpen && (
                <div className="body">
                    {showSearch && (
                        <div className="search">
                            <SearchIcons />
                            <input
                                type="text"
                                placeholder="Search..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>
                    )}

                    <ul>
                        {filteredOptions.length > 0 ? (
                            filteredOptions.map((option, index) => (
                                <li key={index} onClick={() => handleSelect(option)}>
                                    {option[optionLabelKey]}
                                </li>
                            ))
                        ) : (
                            <li className="no-results">No results found</li>
                        )}
                    </ul>
                </div>
            )}
        </div>
    );
}
