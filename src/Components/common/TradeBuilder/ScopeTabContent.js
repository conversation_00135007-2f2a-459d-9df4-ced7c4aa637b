import React, { useEffect, useRef, useState, useMemo } from "react";
import { SolidInfoIcon, RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";

export default function ScopeTabContent({
  activeTab,
  setActiveTab,
  dimensionData,
  metricsData,
  onFieldSelect,
  onSelectAllTab,
  selectedFields,
  disabledFields,
  limitPerSubscription,
  onExtraLimitReached
}) {
  const scrollRef = useRef(null);
  const [showPrevArrow, setShowPrevArrow] = useState(false);
  const [showNextArrow, setShowNextArrow] = useState(false);

  const checkScroll = () => {
    const el = scrollRef.current;
    if (!el) return;
    const { scrollLeft, scrollWidth, clientWidth } = el;
    setShowPrevArrow(scrollLeft > 5);
    setShowNextArrow(scrollLeft + clientWidth < scrollWidth - 5);
  };

  useEffect(() => {
    const el = scrollRef.current;
    if (!el) return;
    checkScroll();
    el.addEventListener("scroll", checkScroll);
    window.addEventListener("resize", checkScroll);
    return () => {
      el.removeEventListener("scroll", checkScroll);
      window.removeEventListener("resize", checkScroll);
    };
  }, []);

  const scrollLeft = () => {
    scrollRef.current?.scrollBy({ left: -100, behavior: "smooth" });
  };

  const scrollRight = () => {
    scrollRef.current?.scrollBy({ left: 100, behavior: "smooth" });
  };

  const currentData = activeTab === "dimension" ? dimensionData : metricsData;

  const numDefaults = disabledFields.length;

  const selectedMap = useMemo(() => {
    const map = {};
    for (const field of selectedFields) {
      map[field.database_field] = true;
    }
    return map;
  }, [selectedFields]);

  const currentExtra = selectedFields.length - numDefaults;
  const remaining = limitPerSubscription - currentExtra;

  const nonDisabledItems = currentData.filter(
    (item) => !disabledFields.includes(item.database_field)
  );

  const allSelected = nonDisabledItems.every(
    (item) => selectedMap[item.database_field]
  );

  const unselectedCount = nonDisabledItems.filter(
    (item) => !selectedMap[item.database_field]
  ).length;

  const selectAllDisabled = !allSelected && unselectedCount > remaining;

  const handleSelectAll = (e) => {
    onSelectAllTab(activeTab, e.target.checked);
  };

  const realExtra = selectedFields.filter(
    (f) => !disabledFields.includes(f.database_field)
  ).length;

  useEffect(() => {
    if (typeof onExtraLimitReached === "function") {
      onExtraLimitReached(realExtra >= limitPerSubscription);
    }
  }, [realExtra, limitPerSubscription]);

  return (
    <div>
      <div className="left_side">
        {showPrevArrow && (
          <div className="move-pre-arrow" onClick={scrollLeft}>
            <div className="icon">
              <RightArrowIcon />
            </div>
          </div>
        )}
        <div className="scrollable_tabs" ref={scrollRef}>
          <div
            className={`scope_dimension ${activeTab === "dimension" ? "active" : ""}`}
            onClick={() => setActiveTab("dimension")}
          >
            <p>Dimensions</p>
            <span className="scopeCount">{dimensionData?.length}</span>
          </div>
          <div
            className={`scope_metrices ${activeTab === "metrics" ? "active" : ""}`}
            onClick={() => setActiveTab("metrics")}
          >
            <p>Metrics</p>
            <span className="scopeCount">{metricsData?.length}</span>
          </div>
        </div>
        {showNextArrow && (
          <div className="move-next-arrow" onClick={scrollRight}>
            <div className="icon">
              <RightArrowIcon />
            </div>
          </div>
        )}
      </div>

      <div className="right_side">
        <label className="scope_dimension_show" htmlFor="selectAll">
          <input
            className="custom_checkbox_input form-check-input mt-0"
            type="checkbox"
            id="selectAll"
            checked={allSelected}
            disabled={selectAllDisabled}
            onChange={handleSelectAll}
          />
          Select All
        </label>

        {currentData.map((item, index) => {
          const isDisabledField = disabledFields.includes(item.database_field);
          const isSelected = selectedFields.some(
              (f) => f.database_field === item.database_field
            );

          const realExtra = selectedFields.filter(
            (f) => !disabledFields.includes(f.database_field)
          ).length;

          const shouldDisable = isDisabledField || (!isSelected && realExtra >= limitPerSubscription);

          return (
            <label
              className={
                activeTab === "dimension"
                  ? "scope_dimension_show"
                  : "scope_metrices_show"
              }
              key={index}
              htmlFor={`select-${activeTab}-${index}`}
            >
              <input
                className="custom_checkbox_input form-check-input mt-0"
                type="checkbox"
                id={`select-${activeTab}-${index}`}
                checked={isSelected || isDisabledField}
                disabled={shouldDisable}
                onChange={(e) => {
                  if (isDisabledField) return;
                  onFieldSelect(item, e.target.checked);
                }}
              />
              {item.field_name}
              <CommonTooltip
                className="CustomTooltip"
                content={item.summary}
                position="bottom-right"
              >
                <SolidInfoIcon />
              </CommonTooltip>
            </label>
          );
        })}
      </div>
    </div>
  );
}
