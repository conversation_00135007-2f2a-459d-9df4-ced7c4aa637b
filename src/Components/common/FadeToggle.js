'use client';

import { useState, useEffect } from "react";

export default function FadeText({ text }) {
  const [displayedText, setDisplayedText] = useState(text);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(false); 

    const timeout = setTimeout(() => {
      setDisplayedText(text); 
      setIsVisible(true); 
    }, 100); 

    return () => clearTimeout(timeout);
  }, [text]);

  return (
    <div className={`transition-opacity  ${isVisible ? "opacity-100" : "opacity-0"}`}
    style={{ transitionDuration: "400ms" }}
    >
      <p className="">{displayedText}</p>
    </div>
  );
}
