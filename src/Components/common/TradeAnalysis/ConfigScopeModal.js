import React, { useEffect, useRef, useState } from "react";
import { WhiteCrossIcon, SearchIcons, RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import ScopeTabContent from "@/Components/common/TradeAnalysis/ScopeTabContent";
import CommonButton from "@/Components/UI/CommonButton";
import toast from "react-hot-toast";

export default function ConfigScopeModal({
  title,
  toggleModal,
  btnIncExc = false,
  sendToParentFunction,
  defaultValues = {},
}) {
  const [activeScope, setActiveScope] = useState("Transaction");
  const [transactionActiveTab, setTransactionActiveTab] = useState("dimension");
  const [tradeActiveTab, setTradeActiveTab] = useState("dimension");
  const [portfolioActiveTab, setPortfolioActiveTab] = useState("dimension");
  const [activeIncExc, setActiveIncExc] = useState(defaultValues?.type || "");
  const [selectedFieldData, setSelectedFieldData] = useState({
    title: defaultValues?.title || null,
    id: defaultValues?.id || null,
    activeScope: defaultValues?.activeScope || null,
  });
  const [inlineError, setInlineError] = useState("");
  const scopeContainerRef = useRef(null);
  const [showPrevArrow, setShowPrevArrow] = useState(false);
  const [showNextArrow, setShowNextArrow] = useState(false);

  const scopes = [
    { name: "Transaction", count: 367 },
    { name: "Trade", count: 365 },
    { name: "Portfolio", count: 350 },
  ];

  const transactionDimension = [
    { id: 1, title: "DIMENSION ACCOUNT INITIAL BALANCE" },
    { id: 2, title: "DIMENSION MAX RISK PERCENTAGE" },
    { id: 3, title: "DIMENSION PROFIT ALLOCATION TO CAPITAL RESERCE" },
    { id: 4, title: "DIMENSION ACCOUNT GROWTH GOAL" },
    { id: 5, title: "DIMENSION ACCOUNT STOP RISK VALUE" },
    { id: 6, title: "DIMENSION STOCK UNIT OF MEASUREMENT" },
    { id: 7, title: "DIMENSION ACCOUNT INITIAL BALANCE" },
  ];

  const transactionMetrices = [
    { id: 1, title: "METRICES ACCOUNT INITIAL BALANCE" },
    { id: 2, title: "METRICES MAX RISK PERCENTAGE" },
    { id: 3, title: "METRICES PROFIT ALLOCATION TO CAPITAL RESERCE" },
    { id: 4, title: "METRICES ACCOUNT GROWTH GOAL" },
    { id: 5, title: "METRICES ACCOUNT STOP RISK VALUE" },
    { id: 6, title: "METRICES STOCK UNIT OF MEASUREMENT" },
  ];
  const showInlineToast = (message) => {
    setInlineError(message);
    setTimeout(() => {
      setInlineError("");
    }, 2000);
  };
  const isDisabled =
    (btnIncExc && (!activeIncExc || !selectedFieldData.title));
  const submitModal = () => {
    if (btnIncExc) {
      if (!activeIncExc) {
        showInlineToast("Please select Include or Exclude.");
        return;
      }
      if (!selectedFieldData.title) {
        showInlineToast("Please select an Entity");
        return;
      }
      sendToParentFunction({ type: activeIncExc, ...selectedFieldData });
      return;
    }
    toggleModal();
  };
  const checkScrollPosition = () => {
    const el = scopeContainerRef.current;
    if (!el) return;

    const { scrollLeft, clientWidth, scrollWidth } = el;

    setShowPrevArrow(scrollLeft > 5);
    setShowNextArrow(scrollLeft + clientWidth < scrollWidth - 5);
  };

  useEffect(() => {
    const el = scopeContainerRef.current;
    if (!el) return;

    checkScrollPosition();
    el.addEventListener("scroll", checkScrollPosition);
    window.addEventListener("resize", checkScrollPosition);

    return () => {
      el.removeEventListener("scroll", checkScrollPosition);
      window.removeEventListener("resize", checkScrollPosition);
    };
  }, []);

  const handleNextScroll = () => {
    scopeContainerRef.current?.scrollBy({ left: 100, behavior: "smooth" });
  };

  const handlePreviousScroll = () => {
    scopeContainerRef.current?.scrollBy({ left: -100, behavior: "smooth" });
  };
  return (
    <div className="modal_overlay">
      <div className="search_section">
        <h4>{title}</h4>
        <div className="search_header">
          <div className="row">
            <div className={!btnIncExc ? "col-lg-4 col-8 order-1" : "col-lg-2 col-8 order-1"}>
              <div className=" closing_Section p-0" >
                <button onClick={toggleModal}>
                  <WhiteCrossIcon />
                </button>
                <div className="d-lg-flex d-block align-items-center gap-2">
                  <p >Select Field</p>
                  {!btnIncExc && (
                    <>
                      <span>19 of 367 selected</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            <div className={!btnIncExc ? "col-lg-6 col-12 order-lg-2 order-3 mt-lg-0 mt-3" : "col-lg-8 col-12 order-lg-2 order-3 mt-lg-0 mt-3"}>
              <div className="search">
                <SearchIcons />
                <input type="text" placeholder="Search metrics" />
              </div>
            </div>
            <div className="col-lg-2 col-4 order-lg-3 order-2">
              <CommonButton
                title="Confirm"
                className=" p-2 rounded-4 lh-sm"
                onClick={submitModal}
                disabled={isDisabled}
              />
            </div>
          </div>
        </div>
        {btnIncExc && (
          <div className="d-flex gap-2">
            <button
              className={`btn_include  ${activeIncExc == "include" ? "active" : ""
                }`}
              onClick={() => setActiveIncExc("include")}
            >
              Include
            </button>
            <button
              className={`btn_exclude  ${activeIncExc == "exclude" ? "active" : ""
                }`}
              onClick={() => setActiveIncExc("exclude")}
            >
              Exclude
            </button>
          </div>
        )}
        <div className="scope_section_wrapper">
          <div className="scope_section" ref={scopeContainerRef}>
            {showPrevArrow && (
              <div className="move-pre-arrow" onClick={handlePreviousScroll}>
                <div className="icon">
                  <RightArrowIcon />
                </div>
              </div>
            )}
            {scopes.map((scope) => (
              <div
                key={scope.name}
                className={`scopeName ${activeScope === scope.name ? "active" : ""
                  }`}
                onClick={() => setActiveScope(scope.name)}
              >
                <p>{scope.name} (Scope)</p>
                <span className="scopeCount">{scope.count}</span>
              </div>
            ))}
            {showNextArrow && (
              <div className="move-next-arrow" onClick={handleNextScroll}>
                <div className="icon">
                  <RightArrowIcon />
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="scope_content">
          {activeScope === "Transaction" && (
            <ScopeTabContent
              scope="Transaction"
              activeTab={transactionActiveTab}
              setActiveTab={setTransactionActiveTab}
              dimensionData={transactionDimension}
              metricsData={transactionMetrices}
              btnIncExc={btnIncExc}
              onSelectField={(data) =>
                setSelectedFieldData({
                  title: data.title,
                  id: data.id,
                  activeScope: activeScope,
                })
              }
              selectField={selectedFieldData}
            />
          )}

          {activeScope === "Trade" && (
            <ScopeTabContent
              scope="Trade"
              activeTab={tradeActiveTab}
              setActiveTab={setTradeActiveTab}
              dimensionData={transactionDimension}
              metricsData={transactionMetrices}
              btnIncExc={btnIncExc}
              onSelectField={(data) =>
                setSelectedFieldData({
                  title: data.title,
                  id: data.id,
                  activeScope: activeScope,
                })
              }
              selectField={selectedFieldData}
            />
          )}

          {activeScope === "Portfolio" && (
            <ScopeTabContent
              scope="Portfolio"
              activeTab={portfolioActiveTab}
              setActiveTab={setPortfolioActiveTab}
              dimensionData={transactionDimension}
              metricsData={transactionMetrices}
              btnIncExc={btnIncExc}
              onSelectField={(data) =>
                setSelectedFieldData({
                  title: data.title,
                  id: data.id,
                  activeScope: activeScope,
                })
              }
              selectField={selectedFieldData}
            />
          )}
        </div>
        {inlineError && (
          <div
            style={{
              position: "absolute",
              top: 20,
              right: 20,
              background: "#fdd",
              padding: "10px 16px",
              borderRadius: "8px",
              color: "#900",
              fontSize: "14px",
              animation: "fadeOut 2s ease forwards"
            }}
          >
            {inlineError}
          </div>
        )}
      </div>
    </div>
  );
}
