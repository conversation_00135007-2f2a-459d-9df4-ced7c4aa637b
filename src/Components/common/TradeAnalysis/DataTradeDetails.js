import React, { useState } from "react";
import {
  DeleteIcon,
  SolidInfoIcon,
  AddPlusIcon,
  DragDropIcon,
  DropArrowIcon,
} from "@/assets/svgIcons/SvgIcon";
import { CSS } from "@dnd-kit/utilities";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import ConfigScopeModal from "@/Components/common/TradeAnalysis/ConfigScopeModal";
import { useDraggable } from "@dnd-kit/core";
import { useSortable } from "@dnd-kit/sortable";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

const DraggableDimension = ({ item, deleteDimension, onDoubleClick }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useDraggable({
    id: item.id,
    data: {
      from: "dimensions",
      item: item,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: isDragging ? "grabbing" : "grab",
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 100 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      onDoubleClick={onDoubleClick}
    >
      <div className="include_trades">
        <div className="head">
          <DragDropIcon />
          <div>
            <div className="d-flex align-items-center gap-2">
              <h6>{item.title}</h6>
              <CommonTooltip
                className="CustomTooltip"
                content="💡 Select the dimensions and metrics you want to analyze. Dimensions describe your data (like asset type, date, or market), while metrics quantify performance (like net profit, win rate, or average trade size)."
                position="top-right"
              >
                <SolidInfoIcon height={"20px"} width={"20px"} />
              </CommonTooltip>
            </div>
            <p>Scope: Trade</p>
          </div>
        </div>
        <button
          className="p-1"
          onClick={deleteDimension}
          onPointerDown={(e) => e.stopPropagation()}
        >
          <DeleteIcon />
        </button>
      </div>
    </div>
  );
};

const DraggableMetric = ({ item, deleteMetric, onDoubleClick }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useDraggable({
    id: item.id,
    data: {
      from: "metrics",
      item,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: isDragging ? "grabbing" : "grab",
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 100 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      onDoubleClick={onDoubleClick}
    >
      <div className="include_trades">
        <div className="head">
          <DragDropIcon />
          <div>
            <div className="d-flex align-items-center gap-2">
              <h6>{item.title}</h6>
              <CommonTooltip
                className="CustomTooltip"
                content="💡 These are portfolio-level settings."
                position="top-right"
              >
                <SolidInfoIcon height={"20px"} width={"20px"} />
              </CommonTooltip>
            </div>
            <p>Scope: Trade</p>
          </div>
        </div>
        <button
          className="p-2 pe-1"
          onClick={deleteMetric}
          onPointerDown={(e) => e.stopPropagation()}
        >
          <DeleteIcon />
        </button>
      </div>
    </div>
  );
};

export default function DataTradeDetails({
  dimensions,
  metrics,
  values,
  rows,
  columns,
  setMetricRow,
  setDimensionRow,
  valueSet,
  rowSet,
}) {
  const [isDimentionModal, setIsDimentionModal] = useState(false);
  const [isMetricsModal, setIsMetricsModal] = useState(false);
  const [isFilterCollapse, setFilterCollapse] = useState(true);

  const collapseFilter = () => {
    setFilterCollapse((prev) => !prev);
  };
  const dimentionModal = () => {
    setIsDimentionModal((prev) => !prev);
  };
  const metricsModal = () => {
    setIsMetricsModal((prev) => !prev);
  };
  const dbClickDimen = (dim) => {
    const alreadyExists =
      rows.some((v) => v.originalId === dim.id) ||
      columns.some((v) => v.originalId === dim.id);
    const copiedItem = {
      ...dim,
      id: `${dim.id}-${Date.now()}`,
      originalId: dim.id,
    };
    if (!alreadyExists) rowSet((prev) => [...prev, copiedItem]);
  };

  const dbClickMetric = (metric) => {
    const alreadyExists = values.some((v) => v.originalId === metric.id);
    const copiedItem = {
      ...metric,
      id: `${metric.id}-${Date.now()}`,
      originalId: metric.id,
    };
    if (!alreadyExists) valueSet((prev) => [...prev, copiedItem]);
  };

  return (
    <>
      <div className="heading">
        <h2>Data</h2>
        <CommonTooltip
          className="CustomTooltip"
          content="💡 Select the dimensions and metrics you want to analyze. Dimensions describe your data (like asset type, date, or market), while metrics quantify performance (like net profit, win rate, or average trade size)."
          position="top-right"
        >
          <SolidInfoIcon />
        </CommonTooltip>
        <span className="collapse_main" onClick={collapseFilter}>
          <DropArrowIcon />
        </span>
      </div>
      {isFilterCollapse && (
        <div>
          <div className="innerCard">
            <div className="cardHeading">
              <p>Dimensions</p>
            </div>
            <div className="trade_analysis_configure">
              <div className="configure_add">
                <div className="d-flex gap-2">
                  <div className="dashed_design"></div>
                  <h6 className="header_title">Add Dimension</h6>
                </div>
                <div
                  className="d-flex gap-2 cursor-pointer"
                  onClick={dimentionModal}
                >
                  <p className="count_add">({dimensions?.length}/15)</p>
                  <AddPlusIcon />
                </div>
              </div>
              <div className="trade_analysis_dimension">
                <SortableContext
                  items={dimensions.map((d) => d.id)}
                  strategy={verticalListSortingStrategy}
                >
                  {dimensions.map((dim) => (
                    <DraggableDimension
                      key={dim.id}
                      item={dim}
                      onDoubleClick={() => dbClickDimen(dim)}
                      deleteDimension={() =>
                        setDimensionRow(
                          dimensions.filter((d) => d.id !== dim.id)
                        )
                      }
                    />
                  ))}
                </SortableContext>
                {isDimentionModal && (
                  <ConfigScopeModal
                    title="Dimensions"
                    toggleModal={dimentionModal}
                  />
                )}
              </div>
            </div>
          </div>
          <div className="innerCard">
            <div className="cardHeading">
              <p>Metrics</p>
            </div>
            <div className="trade_analysis_configure">
              <div className="configure_add">
                <div className="d-flex gap-2">
                  <div className="dashed_design"></div>
                  <h6 className="header_title">Add Metric</h6>
                </div>
                <div
                  className="d-flex gap-2 cursor-pointer"
                  onClick={metricsModal}
                >
                  <p className="count_add">({metrics?.length}/15)</p>
                  <AddPlusIcon />
                </div>
              </div>
              <div className="trade_analysis_metrics">
                <SortableContext
                  items={metrics.map((m) => m.id)}
                  strategy={verticalListSortingStrategy}
                >
                  {metrics.map((metric) => (
                    <DraggableMetric
                      key={metric.id}
                      item={metric}
                      onDoubleClick={() => dbClickMetric(metric)}
                      deleteMetric={() =>
                        setMetricRow(metrics.filter((m) => m.id !== metric.id))
                      }
                    />
                  ))}
                </SortableContext>
                {isMetricsModal && (
                  <ConfigScopeModal
                    title="Metrics"
                    toggleModal={metricsModal}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
