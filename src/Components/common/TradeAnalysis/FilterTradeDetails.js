import { Dropdown } from "react-bootstrap";
import React, { useState, useEffect, useRef } from "react";
import {
  AddPlusIcon,
  DeleteIcon,
  BlackDownIcon,
  SolidInfoIcon,
  TripleDotsMenu,
  DropArrowIcon,
  WhiteDownArrow,
  OpenNewtabIcon,
  DeleteDarkIcon,
  RenameIcon,
} from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import ConfigScopeModal from "@/Components/common/TradeAnalysis/ConfigScopeModal";
import ConditionBuilder from "./ConditionBuilder";
export default function FilterTradeDetails() {
  const [isFilterCollapse, setFilterCollapse] = useState(true);
  const [strategyModal, setStrategyModal] = useState(false);
  const [isFilterModal, setIsFilterModal] = useState(false);
  const [editingFilterIndex, setEditingFilterIndex] = useState(null);
  const [editingFilterData, setEditingFilterData] = useState(null);
  const [openIndex, setOpenIndex] = useState(null);
  const dropdownRefs = useRef([]);

  const [stategyList, setStategyList] = useState([
    { id: 1, title: "Max Risk Percentage" },
    { id: 2, title: "Stock Unit of Measurement" },
  ]);
  const [filterList, setFilterList] = useState([]);

  const collapseFilter = () => {
    setFilterCollapse((prev) => !prev);
  };

  const filterModal = (item = null, index = null) => {
    if (item !== null && index !== null) {
      setEditingFilterData(item);
      setEditingFilterIndex(index);
    } else {
      setEditingFilterData(null);
      setEditingFilterIndex(null);
    }
    setIsFilterModal((prev) => !prev);
  };

  const dimensionData = [
    { id: 1, title: "Max Risk Percentage" },
    { id: 2, title: "PROFIT ALLOCATION TO CAPITAL" },
    { id: 3, title: "PROFIT ALLOCATION TO CAPITAL" },
    { id: 4, title: "STOCK UNIT OF MEASUREMENT" },
  ];
  const addStrategies = () => {
    setStrategyModal((prev) => !prev);
  };

  const deleteStategyList = (index) => {
    const updatedList = [...stategyList];
    updatedList.splice(index, 1);
    setStategyList(updatedList);
  };
  const deleteFilterList = (index) => {
    const updatedList = [...filterList];
    updatedList.splice(index, 1);
    setFilterList(updatedList);
  };

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      const clickedInsideAny = dropdownRefs.current.some(
        (ref) => ref && ref.contains(event.target)
      );
      if (!clickedInsideAny) {
        setOpenIndex(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleDropdown = (index) => {
    setOpenIndex((prev) => (prev === index ? null : index));
  };
  const handleConditionsUpdate = () => {
    // setAndSections(updated)
  };

  const handleFieldSelect = (data) => {
    if (editingFilterIndex !== null) {
      const updated = [...filterList];
      updated[editingFilterIndex] = data;
      setFilterList(updated);
    } else {
      setFilterList((prev) => [...prev, data]);
    }
    setEditingFilterData(null);
    setEditingFilterIndex(null);
    setIsFilterModal(false);
  };

  return (
    <>
      <div className="heading">
        <h2>Filters</h2>
        <CommonTooltip
          className="CustomTooltip"
          content="💡 Apply conditions to narrow down which trades are included in your analysis. Use strategies or custom filters to include or exclude specific data."
          position="top-right"
        >
          <SolidInfoIcon />
        </CommonTooltip>
        <span className="collapse_main" onClick={collapseFilter}>
          <DropArrowIcon />
        </span>
      </div>
      {isFilterCollapse && (
        <div>
          <div className="innerCard">
            <div className="trade_analysis_portfolio">
              <div className="d-flex gap-2 align-items-center mb-2">
                <div className="greenCircle"></div>
                <span>
                  Includes 351 Transactions Within 157 Trades [15% of Portfolio]
                </span>
              </div>
              <div className="d-flex gap-2 align-items-center">
                <div className="redCircle"></div>
                <span>
                  Excludes 1990 Transactions Within 890 Trades [85% of
                  Portfolio]
                </span>
              </div>
            </div>
          </div>
          <div className="innerCard relative">
            <div className="cardHeading">
              <p>Strategies</p>
            </div>
            <div className="trade_analysis_configure">
              <div className="configure_add">
                <div className="d-flex gap-2">
                  <div className="dashed_design"></div>
                  <h6 className="header_title">Add Strategy</h6>
                </div>
                <div
                  className="d-flex gap-2 cursor-pointer"
                  onClick={addStrategies}
                >
                  <p className="count_add">({stategyList?.length}/5)</p>
                  <AddPlusIcon />
                </div>
              </div>
              <div className="trade_analysis_strategy">
                {stategyList.map((item, index) => (
                  <div className="include_trades" key={index}>
                    <div className="head">
                      <div className="include_trades_circle"></div>
                      <span>{item.title}</span>
                      <CommonTooltip
                        className="CustomTooltip"
                        content="💡 These are portfolio-level settings."
                        position="top-right"
                      >
                        <SolidInfoIcon height={"20px"} width={"20px"} />
                      </CommonTooltip>
                    </div>
                    <button onClick={() => deleteStategyList(index)}>
                      <DeleteIcon />
                    </button>
                  </div>
                ))}
              </div>
            </div>
            {strategyModal && (
              <div className="trade_analysis_strategy">
                <div className="condition-dropdown">
                  <div className="right_side">
                    <div className="scope_dimension_show">
                      <p className="select_category">Name</p>
                      <BlackDownIcon />
                    </div>
                    {dimensionData.map((item, index) => (
                      <div
                        className="add_strategy_dropdown"
                        key={index}
                      >
                        <label
                          htmlFor={`strategy_check_${index}`}
                          className="d-flex gap-2 align-items-center w-100"
                        >
                          <input
                            className="custom_checkbox_input form-check-input"
                            type="checkbox"
                            id={`strategy_check_${index}`}
                            style={{ pointerEvents: "none" }} // So clicking inside label doesn't directly toggle checkbox
                          />
                          <span className="name">{item.title}</span>
                          <CommonTooltip
                            className="CustomTooltip"
                            content="💡 These are portfolio-level settings."
                            position="top-right"
                          >
                            <SolidInfoIcon height={"20px"} width={"20px"} />
                          </CommonTooltip>
                        </label>
                        <div
                          className="TripleDotBtn"
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault(); // prevent label default
                            toggleDropdown(index);
                          }}
                          ref={(el) => (dropdownRefs.current[index] = el)}
                        >
                          <TripleDotsMenu />
                        </div>

                        {openIndex === index && (
                          <Dropdown.Menu
                            show
                            style={{
                              position: "absolute",
                              bottom: "75%",
                              right: 8,
                              zIndex: 1000,
                            }}
                          >
                            <Dropdown.Item eventKey="2">
                              <div className="dropdownlist">
                                <OpenNewtabIcon />
                                <span>Open in new tab</span>
                              </div>
                            </Dropdown.Item>
                            <Dropdown.Item eventKey="3">
                              <div className="dropdownlist">
                                <RenameIcon />
                                <span>Rename</span>
                              </div>
                            </Dropdown.Item>
                            <Dropdown.Item eventKey="4">
                              <div className="dropdownlist">
                                <DeleteDarkIcon />
                                <span>Delete</span>
                              </div>
                            </Dropdown.Item>
                          </Dropdown.Menu>
                        )}
                      </div>
                    ))}
                  </div>

                  <div className="btns">
                    <button className="cancel" onClick={addStrategies}>
                      Cancel
                    </button>
                    <button className="confirm">Confirm</button>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="innerCard">
            <div className="cardHeading">
              <p>Filters</p>
            </div>
            <div className="trade_analysis_configure">
              <div className="configure_add">
                <div className="d-flex gap-2">
                  <div className="dashed_design"></div>
                  <h6 className="header_title">Add Filter Condition</h6>
                </div>
                <div
                  className="d-flex gap-2 cursor-pointer"
                  onClick={filterModal}
                >
                  <p className="count_add">({filterList?.length}/5)</p>
                  <AddPlusIcon />
                </div>
              </div>
              <div className="trade_analysis_filter">
                {filterList.map((item, index) => (
                  <div className="filter_section" key={index}>
                    <div className="include_trades">
                      <div className="head">
                        <div
                          className={`${item.type == "include" ? "include" : "exclude"
                            }`}
                        ></div>
                        <span>
                          {item.type == "include" ? "Include" : "Exclude"}{" "}
                          trades when
                        </span>
                      </div>
                      <button onClick={() => deleteFilterList(index)}>
                        <DeleteIcon />
                      </button>
                    </div>
                    <div className="include_trades relative">
                      <div className="head">
                        <div>
                          <h6>{item.title}</h6>
                          <p>Scope: {item.activeScope}</p>
                        </div>
                      </div>
                      <button onClick={() => filterModal(item, index)}>
                        <WhiteDownArrow height={"7px"} width={"14px"} />
                      </button>
                    </div>
                    <ConditionBuilder
                      onConditionsChange={(conds) =>
                        handleConditionsUpdate(conds)
                      }
                    />
                  </div>
                ))}

                {isFilterModal && (
                  <ConfigScopeModal
                    title="Filters"
                    toggleModal={filterModal}
                    btnIncExc={true}
                    sendToParentFunction={handleFieldSelect}
                    defaultValues={editingFilterData}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
