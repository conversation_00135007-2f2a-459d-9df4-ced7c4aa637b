"use client";

import CommonSearch from "@/Components/UI/CommonSearch";
import { RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import { debounce } from "lodash";
import Link from "next/link"; // Import Next.js Link
import { useCallback, useState } from "react";

const CommonBlackCard = ({
  title,
  text,
  onclick,
  link = "#",
  Linktext,
  Linkicon,
  children,
  className,
  editicon,
  tradeacct,
  searcbar,
  subtext,
  submit,
  onSearchChange,
  onClick
}) => {
  const [searchKeyword, setSearchKeyword] = useState("");
  const isLinkValid = link && link !== "#";
  const debouncedSearch = useCallback(
    debounce((searchTerm) => {
      setSearchKeyword(searchTerm);
      onSearchChange(searchTerm);
    }, 300),
    []
  );

  const handleHomeSearch = (event) => {
    debouncedSearch(event.target.value);
  };

  const buttonContent = (
    <button className="d-flex align-items-center" onClick={onClick}>
      {editicon && <span className="me-2">{editicon}</span>}
      {Linktext}
      {Linkicon && <span className="ms-2 link-icon">{Linkicon}</span>}
    </button>
  );

  return (
    <>
      <div className={`common_whitecard ${className}`}>
        <div className="common_whitecard_innerheader">
          <div className="common_whitecard_innerheader_content">
            <h6>{title}</h6>
            <p>{text}</p>
            {searcbar && (
              <div className="common_whitecard_innerheader_search my-3">
                <CommonSearch
                  icon={true}
                  placeholder="Try Searching “Add New Category”"
                  onChange={handleHomeSearch}
                  name={"accountConnection"}
                />
              </div>
            )}
            {subtext && <h6>{subtext}</h6>}
            {submit && (
              <button className="blue_text_btn px-0 mt-3 d-flex align-items-center gap-2">
                Submit a Request <RightArrowIcon />
              </button>
            )}
          </div>
          <div className="common_whitecard_innerheader_icon">
            {isLinkValid ? (
              <Link href={link}>{buttonContent}</Link>
            ) : (
              buttonContent
            )}
            {tradeacct && (
              <div className="common_whitecard_innerheader_tradeacct">
                <h6>Trade Account</h6>
                <p>acct-123</p>
              </div>
            )}
          </div>
        </div>
        <div className="common_whitecard_innerbody">{children}</div>
      </div>
    </>
  );
};

export default CommonBlackCard;
