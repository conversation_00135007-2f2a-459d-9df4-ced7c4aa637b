"use client";
import { useRef, useState } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import {
  ProfileUserDarkIcon,
  BlueLocationIcon,
} from "@/assets/svgIcons/SvgIcon";

export default function UserInformation() {
  const fileInputRef = useRef(null);
  const [image, setImage] = useState(null);

  const handleImageClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <CommonWhiteCard title="User Information" className="account_card">
      <div className="account_card_information">
        <div className="main_inform">
          <div className="position-relative">
            <div
              className="profile_photo"
              onClick={handleImageClick}
              style={{ cursor: "pointer" }}
            >
              {image ? (
                <img
                  src={image}
                  alt="Profile"
                  style={{ width: "100%", height: "100%", borderRadius: "50%" }}
                />
              ) : (
                <ProfileUserDarkIcon />
              )}
              <input
                type="file"
                accept="image/*"
                ref={fileInputRef}
                onChange={handleFileChange}
                style={{ display: "none" }}
              />
            </div>
            <p
              className="profile_updateBtn"
              onClick={handleImageClick}
              title="Click to update photo"
            >
              Update Profile Picture
            </p>
            <button className="round-bluefill-btn" type="button">
              Follow
            </button>
          </div>
          <div>
            <h4>Aaron McCloud</h4>
            <p className="mail">@aaron</p>
            <p className="location">
              <BlueLocationIcon />
              United States
            </p>
            <p className="transaction">6 transactions totalling $9,405</p>
            <p className="active_sign">
              <span></span>Active 13 hours ago
            </p>
            <p className="since">Member since: January 2018</p>
          </div>
        </div>
      </div>
    </CommonWhiteCard>
  );
}
