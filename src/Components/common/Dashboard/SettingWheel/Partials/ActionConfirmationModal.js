import React from 'react'
import { WhiteCrossIcon } from "@/assets/svgIcons/SvgIcon";
import { Col, Row } from "react-bootstrap";

export default function ActionConfirmationModal({ type, toggleModal, closeModal }) {
    let title = "";
    let body = "";
    let buttonText = "";

    if (type === "setGlobal") {
        title = "Overwrite Global Settings?";
        body = "You're about to replace your current global settings with the configuration from this page. This change will affect all pages that are set to use global settings. Your previous global settings will be overwritten.";
        buttonText = "Confirm and Save as Global";
    } else if (type === "applyGlobal") {
        title = "Apply Global Settings Everywhere?";
        body = "This will turn off local filter settings for all pages and apply the current global settings to every view (including Trade Manager, Metrics dashboards, and others). Your previous local settings will be preserved but won’t be active unless you switch them back manually.";
        buttonText = "Confirm and Apply Globally";
    }
    return (
        <div className="modal_overlay">
            <div className="modal-body">
                <div className='d-flex justify-content-between align-items-center mb-4'>
                    <h4 className='mb-0'>{title}</h4>
                    <button onClick={closeModal}>
                        <WhiteCrossIcon />
                    </button>
                </div>
                <div>
                    <p>{body}</p>
                </div>
                <Row className='justify-content-center mt-3'>
                    <Col lg={5} xs={12}>
                        <button className="btn-style white-btn w-100 modal-footer-btn" onClick={closeModal}>Cancel</button>
                    </Col>
                    <Col lg={5} xs={12} className='mt-xl-0 mt-3'>
                        <button className="btn-style w-100 modal-footer-btn" onClick={toggleModal}>{buttonText}</button>
                    </Col>
                </Row>
            </div>
        </div>
    )
}
