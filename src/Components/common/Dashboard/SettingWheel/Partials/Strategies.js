import React, { useState, useEffect, useRef } from "react";
import { Dropdown } from "react-bootstrap";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import {
    AddPlusIcon,
    DeleteIcon,
    BlackDownIcon,
    SolidInfoIcon,
    TripleDotsMenu,
    OpenNewtabIcon,
    DeleteDarkIcon,
    RenameIcon,
} from "@/assets/svgIcons/SvgIcon";

export default function Strategies() {
    const [strategyModal, setStrategyModal] = useState(false);
    const [openIndex, setOpenIndex] = useState(null);
    const dropdownRefs = useRef([]);

    const addStrategies = () => {
        setStrategyModal((prev) => !prev);
    };
    const [stategyList, setStategyList] = useState([
        { id: 1, title: "Max Risk Percentage" },
        { id: 2, title: "Stock Unit of Measurement" },
    ]);
    const dimensionData = [
        { id: 1, title: "Max Risk Percentage" },
        { id: 2, title: "PROFIT ALLOCATION TO CAPITAL" },
        { id: 3, title: "PROFIT ALLOCATION TO CAPITAL" },
        { id: 4, title: "STOCK UNIT OF MEASUREMENT" },
    ];
    const deleteStategyList = (index) => {
        const updatedList = [...stategyList];
        updatedList.splice(index, 1);
        setStategyList(updatedList);
    };

    useEffect(() => {
        const handleClickOutside = (event) => {
            const clickedInsideAny = dropdownRefs.current.some(
                (ref) => ref && ref.contains(event.target)
            );
            if (!clickedInsideAny) {
                setOpenIndex(null);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    const toggleDropdown = (index) => {
        setOpenIndex((prev) => (prev === index ? null : index));
    };
    return (
        <div className="strategies relative">
            <div className="cardHeading">
                <p>Strategies</p>
            </div>
            <div className="add_strategies">
                <div className="d-flex gap-2 align-items-center">
                    <div className="dashed_design"></div>
                    <h6 className="header_title">Add Strategy</h6>
                </div>
                <div
                    className="d-flex gap-2 cursor-pointer"
                    onClick={addStrategies}
                >
                    <p className="count_add">({stategyList?.length}/5)</p>
                    <AddPlusIcon />
                </div>
            </div>
            <div className="show_strategies">
                {stategyList.map((item, index) => (
                    <div className="include_trades" key={index}>
                        <div className="head">
                            <div className="include_trades_circle"></div>
                            <span>{item.title}</span>
                            <CommonTooltip
                                className="CustomTooltip"
                                content=""
                                position="top-left"
                            >
                                <SolidInfoIcon height={"20px"} width={"20px"} />
                            </CommonTooltip>
                        </div>
                        <button onClick={() => deleteStategyList(index)}>
                            <DeleteIcon />
                        </button>
                    </div>
                ))}
            </div>
            {strategyModal && (
                <div className="add-strategies-dropdown">
                    <div className="header">
                        <p>Name</p>
                        <BlackDownIcon />
                    </div>
                    {dimensionData.map((item, index) => (
                        <div
                            className="show-dropdown-strategies"
                            key={index}
                        >
                            <label
                                htmlFor={`strategy_check_${index}`}
                                className="d-flex gap-2 align-items-center w-100"
                            >
                                <input
                                    className="custom_checkbox_input form-check-input"
                                    type="checkbox"
                                    id={`strategy_check_${index}`}
                                />
                                <span className="name">{item.title}</span>
                                <CommonTooltip
                                    className="CustomTooltip"
                                    content="💡 These are portfolio-level settings."
                                    position="top-right"
                                >
                                    <SolidInfoIcon height={"20px"} width={"20px"} />
                                </CommonTooltip>
                            </label>
                            <div
                                className="TripleDotBtn me-2"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    e.preventDefault(); // prevent label default
                                    toggleDropdown(index);
                                }}
                                ref={(el) => (dropdownRefs.current[index] = el)}
                            >
                                <TripleDotsMenu />
                            </div>

                            {openIndex === index && (
                                <Dropdown.Menu
                                    show
                                    style={{
                                        position: "absolute",
                                        right: 10,
                                        zIndex: 1000,
                                        bottom: "75%"
                                    }}
                                >
                                    <Dropdown.Item eventKey="2">
                                        <div className="dropdownlist">
                                            <OpenNewtabIcon />
                                            <span>Open in new tab</span>
                                        </div>
                                    </Dropdown.Item>
                                    <Dropdown.Item eventKey="3">
                                        <div className="dropdownlist">
                                            <RenameIcon />
                                            <span>Rename</span>
                                        </div>
                                    </Dropdown.Item>
                                    <Dropdown.Item eventKey="4">
                                        <div className="dropdownlist">
                                            <DeleteDarkIcon />
                                            <span>Delete</span>
                                        </div>
                                    </Dropdown.Item>
                                </Dropdown.Menu>
                            )}
                        </div>
                    ))}
                    <div className="btns">
                        <button className="cancel" onClick={addStrategies}>
                            Cancel
                        </button>
                        <button className="confirm">Confirm</button>
                    </div>
                </div>
            )}
        </div>
    )
}
