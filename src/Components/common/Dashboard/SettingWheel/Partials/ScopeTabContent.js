import React, { useEffect, useRef, useState } from "react";
import { SolidInfoIcon, RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";

export default function ScopeTabContent({
    activeTab,
    setActiveTab,
    dimensionData,
    metricsData,
    btnIncExc,
    onSelectField,
    selectField
}) {

    const scrollRef = useRef(null);
    const [showPrevArrow, setShowPrevArrow] = useState(false);
    const [showNextArrow, setShowNextArrow] = useState(false);

    const [selectedField, setSelectedField] = useState({
        id: selectField?.id || '',
        title: selectField?.title || '',
    });
    const handleSelect = (item) => {
        onSelectField(item);
        setSelectedField({
            id: item.id,
            title: item.title,
        });
    };
    const checkScroll = () => {
        const el = scrollRef.current;
        if (!el) return;
        const { scrollLeft, scrollWidth, clientWidth } = el;
        setShowPrevArrow(scrollLeft > 5);
        setShowNextArrow(scrollLeft + clientWidth < scrollWidth - 5);
    };

    useEffect(() => {
        const el = scrollRef.current;
        if (!el) return;

        checkScroll();
        el.addEventListener("scroll", checkScroll);
        window.addEventListener("resize", checkScroll);

        return () => {
            el.removeEventListener("scroll", checkScroll);
            window.removeEventListener("resize", checkScroll);
        };
    }, []);

    const scrollLeft = () => {
        scrollRef.current?.scrollBy({ left: -100, behavior: "smooth" });
    };

    const scrollRight = () => {
        scrollRef.current?.scrollBy({ left: 100, behavior: "smooth" });
    };
    return (
        <div>
            <div className="">
                <div className="left_side">
                    {showPrevArrow && (
                        <div className="move-pre-arrow" onClick={scrollLeft}>
                            <div className="icon">
                                <RightArrowIcon />
                            </div>
                        </div>
                    )}
                    <div className="scrollable_tabs" ref={scrollRef}>
                        <div
                            className={`scope_dimension ${activeTab === "dimension" ? "active" : ""
                                }`}
                            onClick={() => setActiveTab("dimension")}
                        >
                            <p>Dimensions</p>
                            <span className="scopeCount">120</span>
                        </div>
                        <div
                            className={`scope_metrices ${activeTab === "metrics" ? "active" : ""
                                }`}
                            onClick={() => setActiveTab("metrics")}
                        >
                            <p>Metrics</p>
                            <span className="scopeCount">359</span>
                        </div>
                    </div>
                    {showNextArrow && (
                        <div className="move-next-arrow" onClick={scrollRight}>
                            <div className="icon">
                                <RightArrowIcon />
                            </div>
                        </div>
                    )}
                </div>

                <div className="right_side">
                    {activeTab === "dimension" &&
                        dimensionData.map((item, index) =>
                            !btnIncExc ? (
                                <label
                                    className={`scope_dimension_show`}
                                    key={index}
                                    htmlFor={`selectDimension-${index}`}
                                >
                                    <input
                                        className="custom_checkbox_input form-check-input"
                                        type="checkbox"
                                        id={`selectDimension-${index}`}
                                        style={{ pointerEvents: "auto" }}
                                    />
                                    <span
                                        className="name custom_checkbox_label ps-1"
                                    >
                                        {item.title}
                                    </span>
                                    <CommonTooltip
                                        className="CustomTooltip"
                                        content="💡 These are portfolio-level settings. Some are required to submit trades and will."
                                        position="top-right"
                                    >
                                        <SolidInfoIcon />
                                    </CommonTooltip>
                                </label>
                            ) : (
                                <div className={`scope_dimension_show ${selectedField.id == item.id ? 'active_row' : ''}`} key={index} onClick={() => handleSelect(item)}>
                                    <p className="name custom_checkbox_label ps-1">{item.title}</p>
                                    <CommonTooltip
                                        className="CustomTooltip"
                                        content="💡 These are portfolio-level settings. Some are required to submit trades and will."
                                        position="top-right"
                                    >
                                        <SolidInfoIcon />
                                    </CommonTooltip>
                                </div>
                            )
                        )}

                    {activeTab === "metrics" &&
                        metricsData.map((item, index) =>
                            !btnIncExc ? (
                                <label
                                    className="scope_dimension_show"
                                    key={index}
                                    htmlFor={`selectDimension-${index}`}
                                >
                                    <input
                                        className="custom_checkbox_input form-check-input"
                                        type="checkbox"
                                        id={`selectDimension-${index}`}
                                        style={{ pointerEvents: "auto" }}
                                    />
                                    <span
                                        className="name custom_checkbox_label ps-1"
                                    >
                                        {item.title}
                                    </span>
                                    <CommonTooltip
                                        className="CustomTooltip"
                                        content="💡 These are portfolio-level settings. Some are required to submit trades and will."
                                        position="top-right"
                                    >
                                        <SolidInfoIcon />
                                    </CommonTooltip>
                                </label>
                            ) : (
                                <div className={`scope_dimension_show ${selectedField.id == item.id ? 'active_row' : ''}`} key={index} onClick={() => handleSelect(item)}>
                                    <p className="name custom_checkbox_label ps-1">{item.title}</p>
                                    <CommonTooltip
                                        className="CustomTooltip"
                                        content="💡 These are portfolio-level settings. Some are required to submit trades and will."
                                        position="top-right"
                                    >
                                        <SolidInfoIcon />
                                    </CommonTooltip>
                                </div>
                            )
                        )}
                </div>
            </div>
        </div>
    );
}
