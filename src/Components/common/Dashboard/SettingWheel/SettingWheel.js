import React, { useEffect, useRef, useState } from "react";
import { SolidInfoIcon, WhiteCrossIcon, RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import Strategies from "./Partials/Strategies";
import Filters from "./Partials/Filters";
import ActionConfirmationModal from "./Partials/ActionConfirmationModal";
import CommonTooltip from "@/Components/UI/CommonTooltip";


export default function SettingWheel({ hideSetting }) {
    const [activeSetting, setActiveSetting] = useState("local");
    const [isConfirmationModal, setConfirmationModal] = useState(false);
    const [modalType, setModalType] = useState("");

    const showConfirmationModal = (type) => {
        setModalType(type);
        setConfirmationModal(true);
    }
    const updateResponse = () => {
        setConfirmationModal(false);
    }
    const hideConfirmationModal = () => {
        setConfirmationModal(false);
        setModalType("");
    }

    const scrollRef = useRef(null);
    const [showPrevArrow, setShowPrevArrow] = useState(false);
    const [showNextArrow, setShowNextArrow] = useState(false);

    const checkScroll = () => {
        const el = scrollRef.current;
        if (!el) return;
        const { scrollLeft, scrollWidth, clientWidth } = el;
        setShowPrevArrow(scrollLeft > 5);
        setShowNextArrow(scrollLeft + clientWidth < scrollWidth - 5);
    };

    useEffect(() => {
        const el = scrollRef.current;
        if (!el) return;

        checkScroll();
        el.addEventListener("scroll", checkScroll);
        window.addEventListener("resize", checkScroll);

        return () => {
            el.removeEventListener("scroll", checkScroll);
            window.removeEventListener("resize", checkScroll);
        };
    }, []);

    const scrollLeft = () => {
        scrollRef.current?.scrollBy({ left: -100, behavior: "smooth" });
    };

    const scrollRight = () => {
        scrollRef.current?.scrollBy({ left: 100, behavior: "smooth" });
    };

    return (
        <div>
            <div className="setting-wheel-popup">
                <div className="heading-container">
                    {showPrevArrow && (
                        <div className="move-pre-arrow" onClick={scrollLeft}>
                            <div className="icon">
                                <RightArrowIcon />
                            </div>
                        </div>
                    )}
                    <div className="scrollable_tabs" ref={scrollRef}>
                        <div
                            className={`heading ${activeSetting === "local" ? "active" : ""}`}
                            onClick={() => setActiveSetting("local")}
                        >
                            <p>Local Settings</p>
                            <CommonTooltip
                                className="CustomTooltip"
                                content="Local Settings apply only to this page."
                                position="top-left"
                            >
                                <SolidInfoIcon />
                            </CommonTooltip>
                        </div>
                        <div
                            className={`heading ${activeSetting === "global" ? "active" : ""}`}
                            onClick={() => setActiveSetting("global")}
                        >
                            <p>Global Settings</p>
                            <CommonTooltip
                                className="CustomTooltip"
                                content="Global Settings apply to all pages set to use global."
                                position="top-left"
                            >
                                <SolidInfoIcon />
                            </CommonTooltip>
                        </div>
                    </div>
                    {showNextArrow && (
                        <div className="move-next-arrow" onClick={scrollRight}>
                            <div className="icon">
                                <RightArrowIcon />
                            </div>
                        </div>
                    )}
                </div>
                <div className="popup_card">
                    {activeSetting === "local" && (
                        <div className="confirmation locally">
                            <div className="head">
                                <p className="title">These filters apply only to this page.</p>
                                <div className="pe-3 buttons">
                                    <button onClick={hideSetting}>
                                        <WhiteCrossIcon />
                                    </button>
                                </div>
                            </div>
                            <div className="d-flex gap-2 align-items-center mt-2">
                                <label className="switch">
                                    <input
                                        type="checkbox"
                                    />
                                    <span className="slider"></span>
                                </label>
                                <label className="switch-label d-flex gap-2 align-items-center">
                                    Use Global Settings for This Page
                                    <CommonTooltip
                                        className="CustomTooltip"
                                        content=" Turn this on to use the global filter configuration on this page. Your local settings will be preserved and reactivated if you turn it off later."
                                        position="top-left"
                                    >
                                        <SolidInfoIcon />
                                    </CommonTooltip>
                                </label>
                            </div>
                            <div className="d-sm-flex d-block gap-2 align-items-center mt-3">
                                <button className="btn-style small-btn"
                                    onClick={() => showConfirmationModal("setGlobal")}
                                >
                                    Set as Global
                                </button>
                                <label className="switch-label d-flex gap-2 align-items-center mt-2 mt-sm-0">
                                    Set These as Global Settings
                                    <CommonTooltip
                                        className="CustomTooltip"
                                        content="Save your current local configuration as the new global settings. This won’t affect other pages unless they’re set to use global settings."
                                        position="top-left"
                                    >
                                        <SolidInfoIcon />
                                    </CommonTooltip>
                                </label>
                            </div>
                        </div>
                    )}
                    {activeSetting === "global" && (
                        <div className="confirmation globally">
                            <div className="head">
                                <p className="title">These filters apply to all pages set to use global settings.</p>
                                <div className="pe-3 buttons">
                                    <button onClick={hideSetting}>
                                        <WhiteCrossIcon />
                                    </button>
                                </div>
                            </div>
                            <div className="d-sm-flex d-block gap-2 align-items-center mt-2">
                                <button className="btn-style small-btn"
                                    onClick={() => showConfirmationModal("applyGlobal")}
                                >
                                    Apply Globally
                                </button>
                                <label className="switch-label d-flex gap-2 align-items-center mt-2 mt-sm-0">
                                    Apply Global Settings to All Pages
                                    <CommonTooltip
                                        className="CustomTooltip"
                                        content={
                                            <>
                                                <p className='mb-1 fw-bold'>
                                                    Force all pages to use global settings.
                                                </p>
                                                <p>
                                                    This disables local filters on every page and applies the current global configuration everywhere.
                                                </p>
                                            </>
                                        } position="top-left"
                                    >
                                        <SolidInfoIcon />
                                    </CommonTooltip>
                                </label>
                            </div>
                        </div>
                    )}
                    <div className="col-xl-5 col-12">
                        <div className="innerCard ">
                            <div className="portfolio">
                                <div className="d-flex gap-2 align-items-center mb-2">
                                    <div className="greenCircle"></div>
                                    <span>
                                        Includes 351 Transactions Within 157 Trades [15% of Portfolio]
                                    </span>
                                </div>
                                <div className="d-flex gap-2 align-items-center">
                                    <div className="redCircle"></div>
                                    <span>
                                        Excludes 1990 Transactions Within 890 Trades [85% of
                                        Portfolio]
                                    </span>
                                </div>
                            </div>
                            <Strategies />
                        </div>
                        <div className="innerCard ">
                            <Filters />
                        </div>
                    </div>
                </div>
            </div>
            {isConfirmationModal && (
                <ActionConfirmationModal
                    type={modalType}
                    toggleModal={updateResponse}
                    closeModal={hideConfirmationModal}
                />
            )}
        </div>
    )
}
