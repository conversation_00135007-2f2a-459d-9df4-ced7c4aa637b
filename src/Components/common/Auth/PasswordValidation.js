import React from 'react'
import { Row, Col } from "react-bootstrap";

export default function PasswordValidation({ password, isValid }) {
    const trimmedPassword = password.replace(/\s/g, "");
    const length = trimmedPassword.length;
    const isLongEnough = length >= 8;
    const hasUppercase = /[A-Z]/.test(trimmedPassword);
    const hasLowercase = /[a-z]/.test(trimmedPassword);
    const hasUppercaseLowercase = hasUppercase && hasLowercase;
    const hasNumber = /\d/.test(trimmedPassword);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(trimmedPassword);

    let requirements = [];
    let includeParts = [];

    if (!isLongEnough) requirements.push("be at least 8 characters long");
    if (!hasUppercase) includeParts.push("one uppercase letter");
    if (!hasLowercase) includeParts.push("one lowercase letter");
    if (!hasNumber) includeParts.push("number");
    if (!hasSpecialChar) includeParts.push("symbol");

    if (includeParts.length > 0) {
        requirements.push("include " + includeParts.join(", "));
    }

    let errorMessage = "";
    if (requirements.length > 0 || password.includes(" ")) {
        errorMessage = "Password must " + requirements.join(", ") + ". Spaces are not allowed.";
    }

    const conditionsMet = [isLongEnough, hasUppercaseLowercase, hasNumber, hasSpecialChar].filter(Boolean).length
    return (
        <>
            <div className="mb-3 password_check">
                {errorMessage && <span className="error-message pt-0 pb-1">{errorMessage}</span>}
                {conditionsMet === 0 && (
                    <div className="box1">
                        <Row className="mt-2">
                            <Col><div className="box1_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                        </Row>
                    </div>
                )}
                {conditionsMet === 1 && (
                    <div className="box2">
                        <Row className="mt-2">
                            <Col><div className="box1_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box2_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                        </Row>
                    </div>
                )}
                {conditionsMet === 2 && (
                    <div className="box3">
                        <Row className="mt-2">
                            <Col><div className="box1_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box2_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box3_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                        </Row>
                    </div>
                )}
                {conditionsMet === 3 && (
                    <div className="box4">
                        <Row className="mt-2">
                            <Col><div className="box1_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box2_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box3_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box4_bg p-1 rounded-5"></div></Col>
                            <Col><div className="white10_bg p-1 rounded-5"></div></Col>
                        </Row>
                    </div>
                )}

                {conditionsMet === 4 || isValid && (
                    <div className="box5">
                        <Row className="mt-2">
                            <Col><div className="box1_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box2_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box3_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box4_bg p-1 rounded-5"></div></Col>
                            <Col><div className="box5_bg p-1 rounded-5"></div></Col>
                        </Row>
                    </div>
                )}
            </div>
        </>
    )
}
