import React, { useState } from 'react';
import { PlusIcon, BlackDropDownArrowIcon, WhiteCrossCircle } from '@/assets/svgIcons/SvgIcon';

const ConditionBuilder = ({ onConditionsChange }) => {
    const [condition, setCondition] = useState(null);
    const [selectedOperator, setSelectedOperator] = useState('Select operation');
    const [expressionValue, setExpressionValue] = useState('');
    const [showDropdown, setShowDropdown] = useState(false);
    const [showOperatorDropdown, setShowOperatorDropdown] = useState(false);
    const [isEditing, setIsEditing] = useState(false);

    // Error states
    const [errors, setErrors] = useState({
        operator: '',
        expression: ''
    });

    const conditionOperators = [
        'contains',
        'does not contain',
        'does not exactly match (≠)',
        'is not one of',
        '<',
        '>',
        '≥',
        '≤',
    ];

    const handleConfirm = () => {
        const newErrors = {
            operator: selectedOperator === 'Select operation' ? 'Please select an operator' : '',
            expression: expressionValue.trim() === '' ? 'Please enter an expression' : '',
        };

        setErrors(newErrors);

        const hasErrors = Object.values(newErrors).some(err => err !== '');
        if (hasErrors) return;

        const newCondition = {
            operator: selectedOperator,
            expression: expressionValue,
        };
        setCondition(newCondition);
        onConditionsChange([newCondition]);

        // Reset UI
        setShowDropdown(false);
        setIsEditing(false);
        setErrors({ operator: '', expression: '' });
    };

    const handleRemove = () => {
        setCondition(null);
        setSelectedOperator('Select operation');
        setExpressionValue('');
        setShowDropdown(false);
        setIsEditing(false);
        onConditionsChange([]);
        setErrors({ operator: '', expression: '' });
    };

    const handleEdit = () => {
        setSelectedOperator(condition.operator);
        setExpressionValue(condition.expression);
        setShowDropdown(true);
        setIsEditing(true);
        setErrors({ operator: '', expression: '' });
    };

    return (
        <div className="condition_section position-relative">
            <div className="conditions-wrapper">
                {condition && (
                    <div className="show-condition" onClick={handleEdit}>
                        <span>{`${condition.operator} "${condition.expression}"`}</span>
                        <span className="remove-icon" onClick={(e) => { e.stopPropagation(); handleRemove(); }}>
                            <WhiteCrossCircle />
                        </span>
                    </div>
                )}
            </div>

            {!condition && (
                <div className="show-condition" onClick={() => setShowDropdown(true)}>
                    <PlusIcon className="black" />
                    Add Condition
                </div>
            )}

            {showDropdown && (
                <div className="condition-dropdown">
                    <p>Condition</p>
                    <div className="divider"></div>
                    <div className="position-relative">
                        <div
                            className={`select-operation ${errors.operator ? 'error-field' : ''}`}
                            onClick={() => setShowOperatorDropdown(prev => !prev)}
                        >
                            <span>{selectedOperator}</span>
                            <BlackDropDownArrowIcon width={15} />
                        </div>
                        {errors.operator && <p className="error-message">{errors.operator}</p>}

                        {showOperatorDropdown && (
                            <div className="select-operation-dropdown">
                                {conditionOperators.map((operator, idx) => (
                                    <span key={idx} onClick={() => {
                                        setSelectedOperator(operator);
                                        setShowOperatorDropdown(false);
                                        setErrors(prev => ({ ...prev, operator: '' }));
                                    }}>
                                        {operator}
                                    </span>
                                ))}
                            </div>
                        )}
                    </div>
                    <div className="divider"></div>
                    <div className="expression">
                        <input
                            type="text"
                            placeholder="Enter expression"
                            value={expressionValue}
                            onChange={(e) => {
                                setExpressionValue(e.target.value);
                                setErrors(prev => ({ ...prev, expression: '' }));
                            }}
                            className={errors.expression ? 'error-field' : ''}
                        />
                    </div>
                    {errors.expression && <p className="error-message">{errors.expression}</p>}
                    <div className="divider"></div>
                    <div className="btns">
                        <button className="cancel" onClick={() => {
                            setShowDropdown(false);
                            setIsEditing(false);
                            setErrors({ operator: '', expression: '' });
                        }}>Cancel</button>
                        <button className="confirm" onClick={handleConfirm}>
                            {isEditing ? 'Update' : 'Confirm'}
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ConditionBuilder;
