import React, { useState } from 'react';
import { Row, Col } from 'react-bootstrap';
import {
    DropDownArrowIcon,
    WhiteCrossCircle,
    DeleteIcon,
} from '@/assets/svgIcons/SvgIcon';
import SelectFieldModal from './SelectFieldModal';
import ConditionBuilder from './ConditionBuilder';

export default function IncludeStrategy({ onDelete }) {
    const [andSections, setAndSections] = useState([
        {
            id: Date.now(),
            orSections: [
                {
                    id: Date.now(),
                    selectedField: '',
                    activeScope: '',
                    showFieldModal: false,
                    conditions: [],
                },
            ],
        },
    ]);

    const addAndSection = () => {
        setAndSections([
            ...andSections,
            {
                id: Date.now(),
                orSections: [
                    {
                        id: Date.now(),
                        selectedField: '',
                        activeScope: '',
                        showFieldModal: false,
                        conditions: [],
                    },
                ],
            },
        ]);
    };

    const addOrSection = (andIndex) => {
        const updated = [...andSections];
        updated[andIndex].orSections.push({
            id: Date.now(),
            selectedField: '',
            activeScope: '',
            showFieldModal: false,
            conditions: [],
        });
        setAndSections(updated);
    };

    const removeOrSection = (andIndex, orIndex) => {
        const updated = [...andSections];
        if (updated[andIndex].orSections.length > 1) {
            updated[andIndex].orSections.splice(orIndex, 1);
            setAndSections(updated);
        }
    };
    const removeAndSection = (andIndex) => {
        const updated = [...andSections];
        updated.splice(andIndex, 1);
        setAndSections(updated);
    };

    const openFieldModal = (andIndex, orIndex) => {
        const updated = [...andSections];
        updated[andIndex].orSections[orIndex].showFieldModal = true;
        setAndSections(updated);
    };

    const closeFieldModal = (andIndex, orIndex) => {
        const updated = [...andSections];
        updated[andIndex].orSections[orIndex].showFieldModal = false;
        setAndSections(updated);
    };

    const handleFieldSelect = (andIndex, orIndex, field, scope) => {
        const updated = [...andSections];
        updated[andIndex].orSections[orIndex].selectedField = field;
        updated[andIndex].orSections[orIndex].activeScope = scope;
        updated[andIndex].orSections[orIndex].showFieldModal = false;
        setAndSections(updated);
    };

    const handleConditionsUpdate = (andIndex, orIndex, updatedConditions) => {
        const updated = [...andSections];
        updated[andIndex].orSections[orIndex].conditions = updatedConditions;
        setAndSections(updated);
    };

    return (
        <div className='strategy_builder_addStrategy'>
            <div className='include_trades'>
                <div className='head'>
                    <div className='include_trades_circle'></div>
                    <span>Include users when</span>
                </div>
                <button onClick={onDelete}>
                    <DeleteIcon />
                </button>
            </div>

            {andSections.map((andSection, andIndex) => (
                <React.Fragment key={andSection.id}>
                    <div className='select_section'>
                        {andSection.orSections.map((orSection, orIndex) => (
                            <div className='or-section' key={orSection.id}>
                                <Row className='align-items-center'>
                                    <Col xxl={3} md={4} xs={12} className='mb-3 mb-md-0'>
                                        <div
                                            className='selectField'
                                            onClick={() => openFieldModal(andIndex, orIndex)}
                                        >
                                            <div>
                                                <span>{orSection.selectedField || 'Select field'}</span>
                                                <p>Scope: {orSection.activeScope || 'None'}</p>
                                            </div>
                                            <DropDownArrowIcon width={15} />
                                        </div>
                                    </Col>

                                    <Col xxl={7} md={5} xs={12} className='mb-3 mb-md-0'>
                                        <ConditionBuilder
                                            onConditionsChange={(conds) =>
                                                handleConditionsUpdate(andIndex, orIndex, conds)
                                            }
                                        />
                                    </Col>

                                    <Col xxl={2} md={3} xs={12}>
                                        <div className='d-flex align-items-center justify-content-end gap-4'>
                                            {orIndex === andSection.orSections.length - 1 && (
                                                <button
                                                    className='whiteBtn'
                                                    onClick={() => addOrSection(andIndex)}
                                                >
                                                    Or
                                                </button>
                                            )}
                                            {(andSection.orSections.length > 1 || andIndex > 0) && (
                                                <span
                                                    style={{ cursor: 'pointer' }}
                                                    onClick={() => {
                                                        if (andSection.orSections.length > 1) {
                                                            removeOrSection(andIndex, orIndex);
                                                        } else if (orIndex === 0 && andIndex > 0) {
                                                            removeAndSection(andIndex);
                                                        }
                                                    }}
                                                >
                                                    <WhiteCrossCircle />
                                                </span>
                                            )}
                                        </div>
                                    </Col>
                                </Row>

                                {orSection.showFieldModal && (
                                    <SelectFieldModal
                                        onClose={() => closeFieldModal(andIndex, orIndex)}
                                        onFieldSelect={(field, scope) =>
                                            handleFieldSelect(andIndex, orIndex, field, scope)
                                        }
                                    />
                                )}

                                {andSection.orSections.length > 1 &&
                                    orIndex < andSection.orSections.length - 1 && (
                                        <div className='sectionDivider my-2'>
                                            <span>OR</span>
                                            <div className='center-line'></div>
                                        </div>
                                    )}
                            </div>
                        ))}
                    </div>

                    {andSections.length > 1 && andIndex < andSections.length - 1 && (
                        <div className='sectionDivider my-3'>
                            <span>AND</span>
                            <div className='center-line'></div>
                        </div>
                    )}
                </React.Fragment>
            ))}

            <button className='whiteBtn' onClick={addAndSection}>
                AND
            </button>
        </div>
    );
}