// app/components/Home/TestimonialsClient.tsx

"use client";

import { useState } from "react";
import { QuoteIcon } from "@/assets/svgIcons/SvgIcon";
import FadeText from "../FadeToggle";

const TestimonialClient = ({ reviews }) => {
    const [index, setIndex] = useState(0);

    const nextReviews = () => {
        setIndex((prev) => (prev + 1) % reviews.length);
    };

    const prevReviews = () => {
        setIndex((prev) =>
            prev - 2 < 0 ? reviews.length - (reviews.length % 2 || 1) : prev - 1
        );
    };

    const displayed = [
        reviews[index],
        reviews[(index + 1) % reviews.length]
    ];

    return (
        <div className="position-relative">
            <div className="g-3 d-flex justify-content-end">
                <div className="d-flex g-3">
                    {displayed.map((review, i) => (
                        <div
                            key={review.id}
                            className={
                                i === 1
                                    ? "pb-lg-5 mb-lg-5 align-items-start d-flex col-md-6 col-12"
                                    : "pt-lg-5 mt-lg-5 align-items-end d-md-flex d-none col-lg-6"
                            }
                        >
                            <div className="p-3">
                                <div className="testimonial_card">
                                    <span className="quoteIcon">
                                        <QuoteIcon />
                                    </span>
                                    <div className="text-black">
                                        <FadeText text={review.description} />
                                    </div>
                                    <h3 className="d-flex align-items-center fs-5 text-md-nowrap">
                                        {review.title}
                                    </h3>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            <div className="d-flex justify-content-end align-items-center mt-4">
                <div className="d-flex gap-3">
                <button className="arrow-right d-flex align-items-center justify-content-center" onClick={prevReviews}>
                   <svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http:www.w3.org/2000/svg">
                     <g transform="scale(-1,1) translate(-8,0)">
                       <path fillRule="evenodd" clipRule="evenodd" d="M7.15694 7.71163L1.49994 13.3686L0.0859375 11.9546L5.03594 7.00462L0.0859375 2.05463L1.49994 0.640625L7.15694 6.29763C7.34441 6.48515 7.44972 6.73946 7.44972 7.00462C7.44972 7.26979 7.34441 7.5241 7.15694 7.71163Z" fill="white" />
                     </g>
                   </svg>
                 </button>
                 <button className="arrow-right d-flex align-items-center justify-content-center" onClick={nextReviews}>
                   <svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http:www.w3.org/2000/svg">
                     <path fillRule="evenodd" clipRule="evenodd" d="M7.15694 7.71163L1.49994 13.3686L0.0859375 11.9546L5.03594 7.00462L0.0859375 2.05463L1.49994 0.640625L7.15694 6.29763C7.34441 6.48515 7.44972 6.73946 7.44972 7.00462C7.44972 7.26979 7.34441 7.5241 7.15694 7.71163Z" fill="white" />
                   </svg>
                 </button>
                </div>
            </div>

        </div>
    );
};

export default TestimonialClient;
