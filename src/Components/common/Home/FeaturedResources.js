import { Col, Container, <PERSON> } from "react-bootstrap";
import CommonHeading from "@/Components/UI/CommonHeading";
import CommonButton from "@/Components/UI/CommonButton";
import NavLink from "@/Components/UI/NavLink";
import "../../../css/Home/FeaturedResources.scss";

const FeaturedResources = ({ initialArticle }) => {
  if (!initialArticle) return null;

  return (
    <section className="featured_resources py-100">
      <Container>
        <div className="featured_resources_heading">
          <CommonHeading heading="Featured Resources" centered />
        </div>

        <Row className="align-items-center gx-xl-5">
          <Col lg={6} sm={12}>
            <div className="featured_resources_content">
              <h3>{initialArticle?.title || "Untitled Article"}</h3>
              <p>{initialArticle?.summary || "No summary available."}</p>
              <NavLink href={`/education/${initialArticle?.slug}`}>
                <CommonButton title="Link to Full Content" className="green-btn" />
              </NavLink>
            </div>
          </Col>
          <Col lg={6} sm={12} className="text-center mt-5 mt-lg-0">
            <div className="featured_resources_img">
              <img
                src={initialArticle?.feature_image_url}
                alt={initialArticle?.title || "Featured Resource"}
              />
            </div>
          </Col>
        </Row>

        <div className="mt-4 mt-md-5 pt-md-3 pt-xl-5 text-center">
          <NavLink href="/education" className="btn-style">
            Explore Resources
          </NavLink>
        </div>
      </Container>
    </section>
  );
};

export default FeaturedResources;

