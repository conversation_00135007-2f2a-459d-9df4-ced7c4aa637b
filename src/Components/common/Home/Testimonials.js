// "use client";

// import { Container } from "react-bootstrap";
// import "slick-carousel/slick/slick.css";
// import "slick-carousel/slick/slick-theme.css";
// import { ArtArrowIcon, QuoteIcon } from "@/assets/svgIcons/SvgIcon";
// import "../../../css/Home/Testimonials.scss";
// import FadeText from "../FadeToggle";
// import { useState } from "react";

// const Testimonials = () => {

//   const Reviews = [
//     {
//       id: 1,
//       title: "✅ Powerful Trade Builder",
//       description:
//         "No other platform allows traders to add detailed metrics while building a trade, with real-time calculations like a trading calculator."
//     },
//     {
//       id: 2,
//       title: "✅ Flexible Dashboards",
//       description:
//         "Traders can create multiple custom dashboards—one for tracking profits, another for losses, even one for stocks under $3 on Tuesdays—allowing for complete trading insights."
//     },
//     {
//       id: 3,
//       title: "✅ Accurate AutoSync",
//       description:
//         "Many platforms struggle with missed trades or broken imports. TradeReply’s AutoSync ensures trades are correctly pulled in—no manual fixes needed."
//     },
//     {
//       id: 4,
//       title: "✅ Custom Reports",
//       description:
//         "TradeReply allows traders to break down performance data in ways that help identify patterns and anomalies, making strategy refinement easy."
//     },
//     {
//       id: 5,
//       title: "✅ Instant Manual Edits",
//       description:
//         "Some platforms require constant refreshing after manual edits. TradeReply updates instantly, saving time and reducing frustration."
//     },
//     {
//       id: 6,
//       title: "✅ Clear Broker Support",
//       description:
//         "No guessing which brokers support AutoSync or manual imports. TradeReply makes this clear from the start, and the CSV importer works seamlessly."
//     },
//     {
//       id: 7,
//       title: "✅ Smart Trade Tagging",
//       description:
//         "A flexible tagging system allows traders to add custom labels to trades, making organization and review easier than ever."
//     },
//     {
//       id: 8,
//       title: "✅ Market Playback",
//       description:
//         "The Trade Replay feature provides a snapshot of exactly where the stock was at the time of trade entry and exit—giving traders valuable insight into their execution."
//     },
//     {
//       id: 9,
//       title: "✅ Real-Time Insights",
//       description:
//         "TradeReply lets you see key metrics instantly as you build a trade—stop loss, take profit, max risk, R:R, deviation, and more—without navigating away or breaking your flow."
//     },
//     {
//       id: 10,
//       title: "✅ Fast & Accurate",
//       description:
//         "TradeReply is built for speed, with no lag and correct date/time tracking to ensure every trade is recorded accurately."
//     }
//   ];
//   const [index, setIndex] = useState(0);


//   const nextReviews = () => {
//     setIndex((prevIndex) => (prevIndex + 1) % Reviews.length);
//   };

//   const prevReviews = () => {
//     setIndex((prevIndex) =>
//       prevIndex - 2 < 0 ? Reviews.length - (Reviews.length % 2 || 1) : prevIndex - 1
//     );
//   };
//   const displayedReviews = [
//     Reviews[index],
//     Reviews[(index + 1) % Reviews.length]
//   ];

//   return (
//     <>
//       <section className="testimonials_sec">
//         <Container>
//           <div className="testimonials_sec_content">
//             <h3 className="d-inline-flex align-items-start">
//               Why Traders Choose TradeReply{" "}
//               <span className="ms-3">
//                 <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-art-arrow.svg" alt="" />
//               </span>
//             </h3>
//             <p>User-Driven Features That Traders Trust, Rely On, and Love</p>
//           </div>
//           <div className="position-relative">
//             <div className="g-3 d-flex justify-content-end">
//               <div className="d-flex g-3">
//                 {displayedReviews.map((review, index) => (
//                   <div key={review.id} className={index == 1 ? 'pb-lg-5 mb-lg-5 align-items-start d-flex col-md-6 col-12' : 'pt-lg-5 mt-lg-5 align-items-end d-md-flex d-none col-lg-6'}>
//                     <div className="p-3">
//                       <div className="testimonial_card">
//                         <span className="quoteIcon">
//                           <QuoteIcon />
//                         </span>
//                         <div className="text-black">
//                           <FadeText text={review.description} />
//                         </div>
//                         <h3 className="d-flex align-items-center fs-5 text-md-nowrap">{review.title}</h3>
//                       </div>
//                     </div>
//                   </div>
//                 ))}
//               </div>
//             </div>
//             {/* <div className="row g-3 d-xl-none d-flex">
//             <div className="d-xl-block d-flex align-items-center justify-content-center w-xl-full">
//               <div key={displayedReviews[0].id} className={index == 1 ? 'pb-5 align-items-start d-flex' : 'pt-5 mt-5 col-md-4 align-items-end   d-flex'}>
//                 <div className="p-3">
//                   <div className="testimonial_card min-h-500">
//                     <span className="quoteIcon">
//                       <QuoteIcon />
//                     </span>
//                     <div className="text-black">
//                     <FadeText text={displayedReviews[0].description} />
//                     </div>
//                     <h3 className="d-flex align-items-center fs-5 text-nowrap">{displayedReviews[0].title}</h3>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div> */}
//             <div className="d-xl-block d-flex align-items-center justify-content-end w-xl-full">
//               <div className="d-flex gap-3 items-center bottom-btn-0">
//                 <button className="arrow-right d-flex align-items-center justify-content-center" onClick={prevReviews}>
//                   <svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
//                     <g transform="scale(-1,1) translate(-8,0)">
//                       <path fillRule="evenodd" clipRule="evenodd" d="M7.15694 7.71163L1.49994 13.3686L0.0859375 11.9546L5.03594 7.00462L0.0859375 2.05463L1.49994 0.640625L7.15694 6.29763C7.34441 6.48515 7.44972 6.73946 7.44972 7.00462C7.44972 7.26979 7.34441 7.5241 7.15694 7.71163Z" fill="white" />
//                     </g>
//                   </svg>
//                 </button>
//                 <button className="arrow-right d-flex align-items-center justify-content-center" onClick={nextReviews}>
//                   <svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
//                     <path fillRule="evenodd" clipRule="evenodd" d="M7.15694 7.71163L1.49994 13.3686L0.0859375 11.9546L5.03594 7.00462L0.0859375 2.05463L1.49994 0.640625L7.15694 6.29763C7.34441 6.48515 7.44972 6.73946 7.44972 7.00462C7.44972 7.26979 7.34441 7.5241 7.15694 7.71163Z" fill="white" />
//                   </svg>
//                 </button>
//               </div>
//             </div>
//           </div>
//           {/* <div className="slider-container mt-4 mt-md-5">
//             <Slider {...settings}>
//               {Reviews?.map((item, index) => (
//                 <div className="testimonial_card" key={index}>
//                   <span className="quoteIcon">
//                     <QuoteIcon />
//                   </span>
//                   <p>
//                     {item?.description}
//                   </p>
//                   <h3>- {item?.title}</h3>
//                 </div>
//               ))}
//             </Slider>
//           </div> */}
//         </Container>
//       </section>
//     </>
//   );
// };

// export default Testimonials;

// app/components/Home/Testimonials.tsx (Server Component)

import { Container } from "react-bootstrap";
import { QuoteIcon } from "@/assets/svgIcons/SvgIcon";
import "../../../css/Home/Testimonials.scss";
import TestimonialClient from "./TestimonialClient";

const Reviews = [
  {
    id: 1,
    title: "✅ Powerful Trade Builder",
    description:
      "No other platform allows traders to add detailed metrics while building a trade, with real-time calculations like a trading calculator."
  },
  {
    id: 2,
    title: "✅ Flexible Dashboards",
    description:
      "Traders can create multiple custom dashboards—one for tracking profits, another for losses, even one for stocks under $3 on Tuesdays—allowing for complete trading insights."
  },
  {
    id: 3,
    title: "✅ Accurate AutoSync",
    description:
      "Many platforms struggle with missed trades or broken imports. TradeReply’s AutoSync ensures trades are correctly pulled in—no manual fixes needed."
  },
  {
    id: 4,
    title: "✅ Custom Reports",
    description:
      "TradeReply allows traders to break down performance data in ways that help identify patterns and anomalies, making strategy refinement easy."
  },
  {
    id: 5,
    title: "✅ Instant Manual Edits",
    description:
      "Some platforms require constant refreshing after manual edits. TradeReply updates instantly, saving time and reducing frustration."
  },
  {
    id: 6,
    title: "✅ Clear Broker Support",
    description:
      "No guessing which brokers support AutoSync or manual imports. TradeReply makes this clear from the start, and the CSV importer works seamlessly."
  },
  {
    id: 7,
    title: "✅ Smart Trade Tagging",
    description:
      "A flexible tagging system allows traders to add custom labels to trades, making organization and review easier than ever."
  },
  {
    id: 8,
    title: "✅ Market Playback",
    description:
      "The Trade Replay feature provides a snapshot of exactly where the stock was at the time of trade entry and exit—giving traders valuable insight into their execution."
  },
  {
    id: 9,
    title: "✅ Real-Time Insights",
    description:
      "TradeReply lets you see key metrics instantly as you build a trade—stop loss, take profit, max risk, R:R, deviation, and more—without navigating away or breaking your flow."
  },
  {
    id: 10,
    title: "✅ Fast & Accurate",
    description:
      "TradeReply is built for speed, with no lag and correct date/time tracking to ensure every trade is recorded accurately."
  }
];

const Testimonials = () => {
  return (
    <section className="testimonials_sec">
      <Container>
        <div className="testimonials_sec_content">
          <h3 className="d-inline-flex align-items-start">
            Why Traders Choose TradeReply{" "}
            <span className="ms-3">
              <img
                src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-art-arrow.svg"
                alt=""
              />
            </span>
          </h3>
          <p>User-Driven Features That Traders Trust, Rely On, and Love</p>
        </div>

        {/* Render all content for SEO */}
        <div className="d-none">
          {Reviews.map((review) => (
            <div key={review.id}>
              <h4>{review.title}</h4>
              <p>{review.description}</p>
            </div>
          ))}
        </div>

        {/* Client-side component handles interaction */}
        <TestimonialClient reviews={Reviews} />
      </Container>
    </section>
  );
};

export default Testimonials;

