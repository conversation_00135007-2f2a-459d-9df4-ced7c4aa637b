import React from "react";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";
export default function MiniTabs(props) {
  return (
    <>
      <div className="d-flex gap-1">
        {props.tabsTAnalysis.map((tab, index) => (
          <div
            onClick={() => props?.onClick?.(tab)}
            className={`portfolio_manager_heading ${
              props.activeTab === tab ? "active" : ""
            }`}
            key={index}
          >
            <p>{tab}</p>
            {tab !== 'Settings' && (
              <CommonTooltip
                className="CustomTooltip"
                content="💡 These are portfolio-level settings. Some are required to submit trades and will be used as default values in Trade Builder. Be sure to fill them out for accurate trade tracking and smoother strategy building."
                position="top-right"
              >
                <SolidInfoIcon />
              </CommonTooltip>
            )}
          </div>
        ))}
      </div>
    </>
  );
}
