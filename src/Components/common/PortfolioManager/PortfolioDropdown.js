'use client';

import { useState, useRef, useEffect } from 'react';
import { BlackDownArrow, SearchIcons } from '@/assets/svgIcons/SvgIcon';


export default function PortfolioDropdown({
    title = '',
    options = [],
    defaultValue = 'Select an option',
    optionLabelKey = 'label',
    onSelect,
    showSearch = true,
    value,
}) {
    const [isOpen, setIsOpen] = useState(false);
    const [selected, setSelected] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const dropdownRef = useRef(null);

    const toggleDropdown = () => setIsOpen(!isOpen);

    const selectedLabel = (() => {
        if (!value) return '';
        const matched = options.find(opt =>
            typeof opt === 'object'
                ? opt.value === value
                : opt === value
        );
        return typeof matched === 'object' ? matched[optionLabelKey] : matched || '';
    })();

    const handleSelect = (option) => {
        onSelect && onSelect(option);
        setIsOpen(false);
        setSearchTerm('');
    };

    const filteredOptions = options.filter((option) => {
        const label = typeof option === 'string' ? option : option[optionLabelKey];
        return label?.toLowerCase().includes(searchTerm.toLowerCase());
    });

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);

            setTimeout(() => {
                const dropdownElement = dropdownRef.current;
                if (dropdownElement) {
                    const rect = dropdownElement.getBoundingClientRect();
                    const dropdownBottom = rect.bottom;
                    const viewportHeight = window.innerHeight;

                    if (dropdownBottom > viewportHeight) {
                        window.scrollBy({
                            top: dropdownBottom - viewportHeight + 10,
                            behavior: 'smooth',
                        });
                    }
                }
            }, 0);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);
    return (
        <div className="porfolio-custom-select" ref={dropdownRef} title={title}>
            <div className="header" onClick={() => setIsOpen(!isOpen)}>
                <span>{selected || defaultValue}</span>
                {/* <BlackDownArrow /> */}
            </div>

            {isOpen && (
                <div className="body">
                    {showSearch && (
                        <div className="search">
                            <input
                                type="text"
                                placeholder="Search..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>
                    )}

                    <ul>
                        {filteredOptions.length > 0 ? (
                            filteredOptions.map((option, index) => (
                                <li key={index} onClick={() => handleSelect(option)}>
                                    {typeof option === 'string' ? option : option[optionLabelKey]}
                                </li>
                            ))
                        ) : (
                            <li className="no-results">No results found</li>
                        )}
                    </ul>
                </div>
            )}
        </div>
    )
}
