'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import Cookies from 'js-cookie';
import useAuthHeartbeat from '@/Hooks/useAuthHeartbeat';

/**
 * Provider component that handles authentication heartbeat monitoring
 * Only activates on authenticated pages (not login, signup, etc.)
 */
export default function AuthHeartbeatProvider({ children }) {
  const pathname = usePathname();
  const { startHeartbeat, stopHeartbeat } = useAuthHeartbeat();

  // Define routes that don't need heartbeat monitoring (public routes)
  const publicRoutes = [
    '/login',
    '/signup',
    '/forget',
    '/reset-password',
    '/verify-email',
    '/locate-account',
    '/auth',
    '/',
    '/blog',
    '/education',
    '/about',
    '/contact',
    '/privacy',
    '/terms',
    '/pricing'
  ];

  // Define routes that definitely need heartbeat monitoring (authenticated routes)
  const authenticatedRoutes = [
    '/dashboard',
    '/account',
    '/create-username',
    '/change-password',
    '/security-check'
  ];

  // Check if current route needs authentication monitoring
  const isAuthenticatedRoute = () => {
    // First check if it's explicitly an authenticated route
    if (authenticatedRoutes.some(route => pathname.startsWith(route))) {
      return true;
    }

    // If it's a public route, no monitoring needed
    if (publicRoutes.some(route => pathname.startsWith(route))) {
      return false;
    }

    // For other routes, check if user has auth token
    const token = Cookies.get("authToken");
    return !!token;
  };

  useEffect(() => {
    const isAuth = isAuthenticatedRoute();

    if (isAuth) {
      // Start heartbeat monitoring for authenticated routes
      startHeartbeat();
    } else {
      // Stop heartbeat monitoring for public routes
      stopHeartbeat();
    }

    // Cleanup on route change or unmount
    return () => {
      // Don't stop heartbeat on cleanup unless we're going to a public route
      // This prevents stopping heartbeat when navigating between authenticated pages
    };
  }, [pathname, startHeartbeat, stopHeartbeat]);

  return children;
}
