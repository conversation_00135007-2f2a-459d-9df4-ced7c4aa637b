<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\OauthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;



//OAUTH
//
//Route::get('/google/registerationRedirect', [OauthController::class, 'registerationRedirect']);
//Route::get('/google/loginRedirect', [OauthController::class, 'loginRedirect']);
//Route::get('/google/callback', [OauthController::class, 'googleCallback']);
//
//Route::get('/facebook/redirect', [OauthController::class, 'facebookRedirect']);
//Route::get('/facebook/callback', [OauthController::class, 'facebookCallback']);


Route::post('/register', [AuthController::class, 'register']);
Route::get('/verify-token', [AuthController::class, 'verifyToken']);
Route::post('/create-username', [AuthController::class, 'createUsername']);
Route::post('/forgot-password', [AuthController::class, 'forgetPassword']);
//    ->middleware('throttle:1,1');
Route::post('/reset-password', [AuthController::class, 'resetPassword']);
Route::post('/resend-verification-code', [AuthController::class, 'resendVerificationCode'])->middleware('throttle:1,1'); // Allow max 5 requests per minute
Route::post('/login', [AuthController::class, 'login']);

Route::get('/signup-data/{providerName}/{uuid}', [OauthController::class, 'fetchSignupEmail']);

// Deletion endpoint (actual deletion logic)
Route::post('/account/delete', [AuthController::class, 'deleteAccount']);

// Facebook instructions (public JSON response)
Route::get('/facebook-account-deletion', function () {
    return redirect(rtrim(env('FRONTEND_URL', 'http://localhost:3000'), '/') . '/privacy');

});

//Route::get('/facebook-account-deletion', function () {
//    return response()->json([
//        'url' => 'https://'.env('APP_URL').'/api/v1/auth/account/delete',
////        'url' => 'https://api.tradereply.com/api/v1/auth/account/delete',  // ← exact route with prefix
//        'method' => 'POST',
//        'fields' => ['id'],
//        'confirmation_code' => uniqid('fb_', true),
//    ]);
//});
//deletion instruction url
//https://************/api/v1/auth/facebook-account-deletion (PUBLIC URL)
//https://api.tradereply.com/api/v1/auth/facebook-account-deletion


Route::post('/facebook/deauthorize', [AuthController::class, 'handleFacebookDeauthorize']);

Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/user', function (Request $request) {
        return response()->json(Auth::user());
    });

    Route::post('/logout', [AuthController::class, 'logout']);
});




