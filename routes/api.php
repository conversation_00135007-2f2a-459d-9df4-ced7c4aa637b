<?php

use App\Http\Controllers\AddressController;
use App\Http\Controllers\Admin\ArticleController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\Dashboard\PortfolioManagerController;
use App\Http\Controllers\Dashboard\TradeAccountController;
use App\Http\Controllers\Dashboard\TradeBuilderController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\PhoneController;
use App\Http\Controllers\SecretQuestionController;
use App\Http\Controllers\PlanController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\SecurityVerificationController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\ThirdPartyProvidersController;
use App\Http\Controllers\TwoFactorController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ZendeskProxyController;
use App\Http\Controllers\LoginActivityController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/


Route::get('/featured-resource', [ArticleController::class, 'featured_resource']);

// Public route for reserved usernames (no auth required)
Route::get('/reserved-usernames', [UserController::class, 'getReservedUsernames']);


Route::prefix('search')->group(function () {
    Route::get('/home', [SearchController::class, 'home']);
    Route::get('/', [SearchController::class, 'Search']);
    Route::get('/blog-sitemap', [SearchController::class, 'blogSitemap']);
    Route::get('/{blog:slug}', [SearchController::class, 'show']);
});


Route::match(['get', 'post', 'put', 'delete'], '/zendesk-proxy/{path}', [ZendeskProxyController::class, 'handle'])
    ->where('path', '.*');


Route::get('/blog-sitemap', [ArticleController::class, 'blogSitemap']);
Route::get('/article/', [ArticleController::class, 'index']);
Route::get('/article/{type}/{slug}', [ArticleController::class, 'show'])
    ->where('type', '^(blog|education)$');
Route::post('education/{slug}/progress', [ArticleController::class, 'updateProgress']);


Route::prefix('category')->group(function () {
    Route::get('/{category:slug?}', [CategoryController::class, 'index']);
    Route::get('/page/{page}', [CategoryController::class, 'index'])->where('page', '[0-9]+');
});


Route::prefix('/stripe')->group(function () {
    Route::get('plans', [PlanController::class, 'index']);

    Route::post('/subscribe', [SubscriptionController::class, 'createSubscription']);
    Route::post('/cancel-subscription', [SubscriptionController::class, 'cancelSubscription']);
});


Route::apiResource('thirdpartyprovider', ThirdPartyProvidersController::class);



//Route::middleware(['BearerTokenSanctum','auth:sanctum'])->group(function () {
Route::middleware('auth:sanctum')->group(function () {

    // Authentication heartbeat (lightweight token validation)
    Route::get('/auth/heartbeat', [AuthController::class, 'heartbeat']);

    // Security verification routes (authenticated users only)
    Route::prefix('security-verification')->group(function () {
        Route::post('/send-code', [SecurityVerificationController::class, 'sendVerificationCode']);
        Route::post('/verify-code', [SecurityVerificationController::class, 'verifyCode']);
        Route::post('/resend-code', [SecurityVerificationController::class, 'resendCode']);
        Route::get('/status', [SecurityVerificationController::class, 'checkSecurityStatus']);
        Route::post('/test-email', [SecurityVerificationController::class, 'testEmail']); // For debugging
        Route::post('/debug-redirect', [SecurityVerificationController::class, 'debugRedirect']); // For debugging
    });


    Route::get('/account', [UserController::class, 'index'])->name('account.index');
    Route::put('/account/update/{user}', [UserController::class, 'update'])->name('account.update');
    Route::put('/account/email/update', [UserController::class, 'updateEmailDirect'])->name('account.email.update.direct');
    Route::post('/account/email/verify', [UserController::class, 'verifyEmailUpdate'])->name('account.email.verify');
    Route::post('/account/change-password', [UserController::class, 'changePassword'])->name('account.change-password');

    // Secret Questions routes
    Route::get("/account/secret-questions", [SecretQuestionController::class, "index"])->name("account.secret-questions.index");
    Route::post("/account/secret-questions", [SecretQuestionController::class, "store"])->name("account.secret-questions.store");
    Route::put("/account/secret-questions", [SecretQuestionController::class, "update"])->name("account.secret-questions.update");
    Route::post("/account/secret-questions/verify", [SecretQuestionController::class, "verify"])->name("account.secret-questions.verify");
    Route::post('/account/{user}/email-update', [UserController::class, 'updateEmail'])->name('account.email.update');

    // Phone number management routes (simplified - no verification)
    Route::prefix('phone')->group(function () {
        Route::post('/setup', [PhoneController::class, 'store'])->name('phone.setup');
        Route::delete('/remove', [PhoneController::class, 'destroy'])->name('phone.remove');
    });

    // Two-Factor Authentication routes
    Route::prefix('2fa')->group(function () {
        Route::get('/status', [TwoFactorController::class, 'status']);
        Route::post('/enable', [TwoFactorController::class, 'enable']);
        Route::post('/disable', [TwoFactorController::class, 'disable']);
        Route::post('/generate-restore-code', [TwoFactorController::class, 'generateRestoreCode']);
        Route::post('/update-always-required', [TwoFactorController::class, 'updateAlwaysRequired']);
    });

    // Address management routes
    Route::prefix('addresses')->group(function () {
        Route::get('/', [AddressController::class, 'index'])->name('addresses.index');
        Route::post('/', [AddressController::class, 'store'])->name('addresses.store');
        Route::get('/{id}', [AddressController::class, 'show'])->name('addresses.show');
        Route::put('/{id}', [AddressController::class, 'update'])->name('addresses.update');
        Route::post('/{id}/set-default', [AddressController::class, 'setDefault'])->name('addresses.setDefault');
        Route::delete('/{id}', [AddressController::class, 'destroy'])->name('addresses.destroy');
    });

    // Username management routes
    Route::prefix('username')->group(function () {
        Route::post('/update', [UserController::class, 'updateUsername'])->name('username.update');
        Route::post('/check-availability', [UserController::class, 'checkUsernameAvailability'])->name('username.check');
    });

    Route::prefix('super-admin/category')->group(function () {
        Route::get('/categories-list', [CategoryController::class, 'categoriesList']);
        Route::post('/store', [CategoryController::class, 'store']);
        Route::get('/{category:slug}/edit', [CategoryController::class, 'edit']);
        Route::put('/{category:slug}', [CategoryController::class, 'update']);
        Route::delete('/{category:slug}', [CategoryController::class, 'destroy']);
    });

    Route::prefix('super-admin/articles')->group(function () {
        Route::get('/list/{type}', [ArticleController::class, 'articlesList'])->where(['type' => 'blog|education']);
        Route::get('/create', [ArticleController::class, 'create']);
        Route::post('/store', [ArticleController::class, 'store']);
        Route::put('/{article}', [ArticleController::class, 'update']);
        Route::delete('/{id}', [ArticleController::class, 'destroy'])->where('id', '[0-9]+');
        Route::get('/{type}/{article}/edit', [ArticleController::class, 'edit'])->where(['type' => 'blog|education']);
    });

    Route::get('/trade-accounts', [TradeAccountController::class, 'index']);

    Route::prefix('trade')->group(function () {
        Route::get('/', [TradeBuilderController::class, 'index']);
        Route::get('fetch/fields', [TradeBuilderController::class, 'fetchAllFields']);
        Route::post('initialize', [TradeBuilderController::class, 'initialize']);
        Route::post('calculate', [TradeBuilderController::class, 'calculateFormulas']);
        Route::post('save-section', [TradeBuilderController::class, 'saveSection']);
        Route::post('publish-form', [TradeBuilderController::class, 'publishForm']);
        Route::post('publish-trade', [TradeBuilderController::class, 'publishTrade']);
        Route::get('form-status/{formKey}', [TradeBuilderController::class, 'getFormStatus']);
        Route::get('{accountTradeId}/data', [TradeBuilderController::class, 'getTradeData']);
        Route::post('delete-form', [TradeBuilderController::class, 'deleteForm']);
        Route::post('upload-chart', [TradeBuilderController::class, 'uploadChart']);
        Route::post('{accountTradeId}', [TradeBuilderController::class, 'deleteTrade']);
    });
    Route::get('drafts', [TradeBuilderController::class, 'getDraftTrades']);
    Route::post('remove-extra-field', [TradeBuilderController::class, 'removeExtraField']);

    Route::prefix('portfolio')->group(function () {
        Route::get('/', [PortfolioManagerController::class, 'index']);
        Route::post('store', [PortfolioManagerController::class, 'store']);
        Route::post('delete', [PortfolioManagerController::class, 'deleteAllEntries']);
    });

    Route::get('/active/subscription', [PlanController::class, 'getActiveUserSubsciption']);
    Route::post('/subscription/downgrade', [PlanController::class, 'downgradePlan']);
    Route::post('/assign/free/plan', [PlanController::class, 'assignFreePlan']);

    Route::delete('/orders/{order}', [OrderController::class, 'destroy']);
    Route::post('/checkout/save', [OrderController::class, 'storeBillingInfoAndChargePayment']);

    // Login Activity routes
    Route::prefix('login-activity')->group(function () {
        Route::get('/', [LoginActivityController::class, 'index']);
        Route::post('/logout-all-devices', [LoginActivityController::class, 'logoutAllDevicesIncludingCurrent']);
        Route::post('/logout-other-devices', [LoginActivityController::class, 'logoutAllDevices']);
    });
});

Route::get('orders', [OrderController::class,'index']);
Route::middleware(['web'])->get('create/subscription/order', [OrderController::class,'storeSubscriptionOrder']);
