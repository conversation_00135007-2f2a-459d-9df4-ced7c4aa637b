<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trade_builder_forms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('trade_builder_id')->constrained()->onDelete('cascade');
            $table->enum('form_type', ['entry', 'exit']);
            $table->json('form_fields');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trade_builder_forms');
    }
};
