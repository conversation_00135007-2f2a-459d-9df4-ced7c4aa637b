<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trade_builders', function (Blueprint $table) {
            Schema::dropIfExists('trade_builder_forms');

            Schema::dropIfExists('trade_builders');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trade_builders', function (Blueprint $table) {
            //
        });
    }
};
