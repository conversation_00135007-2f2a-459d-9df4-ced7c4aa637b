<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolio_field_definitions', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('field_definition_id');
                $table->string('database_field');
                $table->text('summary')->nullable();
                $table->string('account_field')->nullable();

                $table->timestamps();

                $table->foreign('field_definition_id')->references('id')->on('field_definitions')->onDelete('cascade');
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolio_field_definitions');
    }
};
