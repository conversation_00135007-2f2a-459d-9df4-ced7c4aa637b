<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_child_subcategories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_subcategory_id');
            $table->string('name');
            $table->string('slug')->unique();
            $table->integer('sort_order')->default(0);

            $table->timestamps();

            $table->foreign('product_subcategory_id')->references('id')->on('product_subcategories')->onDelete('cascade');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_child_subcategories');
    }
};
