<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
            Schema::create('third_party_providers', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('title');
                $table->string('api');
                $table->string('secret');
                $table->string('password');
                $table->boolean('status')->default(1);
                $table->boolean('installed')->default(0);
                $table->boolean('activated')->default(0);
                $table->string('url');
                $table->unsignedBigInteger('product_id')->nullable();
                $table->integer('stocks')->default(0);
                $table->boolean('crypto')->default(0);
                $table->boolean('auto_sync')->default(0);
                $table->timestamps();
            });        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('third_party_providers');
    }
};
