<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trade_form_sections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('trade_form_id')->constrained()->onDelete('cascade');
            $table->enum('section', ['overview', 'projection', 'outcome']);
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trade_form_sections');
    }
};
