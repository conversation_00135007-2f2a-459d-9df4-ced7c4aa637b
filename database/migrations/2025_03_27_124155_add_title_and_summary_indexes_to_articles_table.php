<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('articles', function (Blueprint $table) {
            // Add indexes for title and summary
            $table->index('title', 'articles_title_index');
            DB::statement('ALTER TABLE articles ADD INDEX articles_summary_index (summary(191));');
        });
    }

    public function down()
    {
        Schema::table('articles', function (Blueprint $table) {
            // Rollback: Drop indexes if needed
            $table->dropIndex(['articles_title_index']);
            DB::statement('ALTER TABLE articles DROP INDEX articles_summary_index;');
        });
    }
};
