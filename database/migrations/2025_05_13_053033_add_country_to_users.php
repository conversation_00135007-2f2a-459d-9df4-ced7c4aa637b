<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            //
            $table->string('first_name')->after('name');
            $table->string('last_name')->after('first_name');
            $table->string('phone_number')->after('last_name');
            $table->string('country')->after('role');
            $table->string('language', 10)->nullable()->after('country');
            $table->string('timezone', 50)->nullable()->after('language');
            $table->string('currency', 5)->nullable()->after('timezone');
            $table->string('number_format', 20)->nullable()->after('currency');
            $table->unsignedTinyInteger('username_change_count')->default(0)->after('username');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            //
        });
    }
};
