<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('user_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Each user has a subscription
            $table->foreignId('subscription_id')->constrained()->onDelete('cascade'); // Linked to subscription plans
            $table->boolean('is_trial')->default(false); // Indicates if this is a trial
            $table->dateTime('trial_expires_at')->nullable(); // Trial expiry date
            $table->dateTime('expires_at')->nullable(); // Subscription expiry
            $table->timestamps();
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_subscriptions');
    }
};
