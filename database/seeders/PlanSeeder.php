<?php

namespace Database\Seeders;

use App\Models\Plan;
use App\Models\PlanRule;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    public function run()
    {
        $oldPlans = Plan::all();

        foreach ($oldPlans as $oldPlan) {
            $oldPlan->delete();
        }

        $plans = [
            [
            'title' => 'Free',
            'price' => 0.00,
            'billing_type' => 'free',
            'billed_description' => null,
            'discount' => null,
            'stripe_price_id' => null,
            'rules' => [
                ['key' => 'ads', 'value' => 'Yes', 'description' => 'Ads Shown', 'is_allowed' => false],
                ['key' => 'trade_accounts', 'value' => '1', 'description' => '1 Trade Account', 'is_allowed' => true],
                ['key' => 'trade_history', 'value' => '6 Months', 'description' => 'Trade History Access (Last 6 Months Only)', 'is_allowed' => true],
                ['key' => 'dashboards', 'value' => '1', 'description' => '1 Dashboard', 'is_allowed' => true],
                ['key' => 'strategy_limit', 'value' => '5', 'description' => '5 Strategy Limit', 'is_allowed' => true],
                ['key' => 'kpi_widget_types', 'value' => '5', 'description' => '5 KPI Widget Types', 'is_allowed' => true],
                ['key' => 'dashboard_presets', 'value' => '1', 'description' => '1 Dashboard Preset', 'is_allowed' => true],
                ['key' => 'strategy_presets', 'value' => '1', 'description' => '1 Strategy Preset', 'is_allowed' => true],
                ['key' => 'sync_brokers', 'value' => 'No', 'description' => 'Sync Brokers API', 'is_allowed' => false],
                ['key' => 'csv_trade_import', 'value' => 'No', 'description' => 'CSV Trade Import', 'is_allowed' => false],
                ['key' => 'marketplace_access', 'value' => 'Yes', 'description' => 'Marketplace Access', 'is_allowed' => true],
                ['key' => 'upload_chart_images', 'value' => 'No', 'description' => 'Upload Chart Images', 'is_allowed' => false],
                ['key' => 'market_replay_limit', 'value' => '1', 'description' => '1 Market Replay Limit', 'is_allowed' => true],
                ['key' => 'analytics_report_access', 'value' => 'Yes', 'description' => 'Analytics Report Access', 'is_allowed' => true],
                ['key' => 'analytics_saved_views', 'value' => '1', 'description' => '1 Analytics Saved View', 'is_allowed' => true],
                ['key' => 'custom_tags', 'value' => '5', 'description' => '5 Custom Tags', 'is_allowed' => true],
                ['key' => 'trade_notes', 'value' => 'Yes', 'description' => 'Trade Notes', 'is_allowed' => true],
                ['key' => 'trade_builder_fields', 'value' => '3', 'description' => 'Trade Builder Extra Fields: 3', 'is_allowed' => true],
                ['key' => 'refer_friend', 'value' => 'Yes', 'description' => 'Refer A Friend Access', 'is_allowed' => true],
                ['key' => 'affiliate_partner', 'value' => 'Yes', 'description' => 'Affiliate Partner Access', 'is_allowed' => true],
                ['key' => 'standard_support', 'value' => 'Yes', 'description' => 'Standard Support (Ticket-based)', 'is_allowed' => true],
                ['key' => 'marketplace_seller', 'value' => 'No', 'description' => 'Marketplace Seller Privileges', 'is_allowed' => false],
                ['key' => 'marketplace_fee', 'value' => 'N/A', 'description' => 'Marketplace Seller Fee', 'is_allowed' => false],
                ['key' => 'priority_support', 'value' => 'No', 'description' => 'Priority Support', 'is_allowed' => false],
            ],  
        ],

            // Essential Monthly
            [
                'title' => 'Essential',
                'price' => 14.95,
                'billing_type' => 'monthly',
                'billed_description' => null,
                'discount' => null,
                'stripe_price_id' => 'price_1RZ02lCQUVLf5GMuLSMsi2Nn',
                'rules' => [
                    ['key' => 'ads', 'value' => 'No', 'description' => 'No Ads', 'is_allowed' => true],
                    ['key' => 'trade_accounts', 'value' => '3', 'description' => '3 Trade Accounts', 'is_allowed' => true],
                    ['key' => 'trade_history', 'value' => 'Unlimited', 'description' => 'Unlimited Trade History Access', 'is_allowed' => true],
                    ['key' => 'dashboards', 'value' => '10', 'description' => '10 Dashboards', 'is_allowed' => true],
                    ['key' => 'strategy_limit', 'value' => '25', 'description' => '25 Strategy Limit', 'is_allowed' => true],
                    ['key' => 'kpi_widget_types', 'value' => 'Unlimited', 'description' => 'KPI Widget Types (Unlimited)', 'is_allowed' => true],
                    ['key' => 'dashboard_presets', 'value' => '10', 'description' => '10 Dashboard Presets', 'is_allowed' => true],
                    ['key' => 'strategy_presets', 'value' => '10', 'description' => '10 Strategy Presets', 'is_allowed' => true],
                    ['key' => 'sync_brokers', 'value' => 'Yes', 'description' => 'Sync Brokers', 'is_allowed' => true],
                    ['key' => 'csv_import', 'value' => 'Yes', 'description' => 'CSV Trade Import', 'is_allowed' => true],
                    ['key' => 'marketplace_access', 'value' => 'Yes', 'description' => 'Marketplace Access', 'is_allowed' => true],
                    ['key' => 'upload_chart_images', 'value' => 'Yes', 'description' => 'Upload Chart Images', 'is_allowed' => true],
                    ['key' => 'market_replay_limit', 'value' => '25', 'description' => '25 Market Replay Limit', 'is_allowed' => true],
                    ['key' => 'analytics_access', 'value' => 'Yes', 'description' => 'Analytics Report Access', 'is_allowed' => true],
                    ['key' => 'analytics_saved_views', 'value' => '10', 'description' => '10 Analytics Saved Views', 'is_allowed' => true],
                    ['key' => 'custom_tags', 'value' => 'Unlimited', 'description' => 'Custom Tags (Unlimited)', 'is_allowed' => true],
                    ['key' => 'trade_notes', 'value' => 'Yes', 'description' => 'Trade Notes', 'is_allowed' => true],
                    ['key' => 'trade_builder_fields', 'value' => '9', 'description' => 'Trade Builder Extra Fields: 9', 'is_allowed' => true],
                    ['key' => 'refer_friend', 'value' => 'Yes', 'description' => 'Refer A Friend Access', 'is_allowed' => true],
                    ['key' => 'affiliate_partner', 'value' => 'Yes', 'description' => 'Affiliate Partner Access', 'is_allowed' => true],
                    ['key' => 'standard_support', 'value' => 'Yes', 'description' => 'Standard Support (Ticket-based)', 'is_allowed' => true],
                    ['key' => 'marketplace_seller', 'value' => 'Yes', 'description' => 'Marketplace Seller Privileges', 'is_allowed' => true],
                    ['key' => 'marketplace_fee', 'value' => '5%', 'description' => 'Marketplace Seller Fee: 5%', 'is_allowed' => true],
                    ['key' => 'priority_support', 'value' => 'No', 'description' => 'Priority Support', 'is_allowed' => false],
                ],
            ],
            // Essential Yearly
            [
                'title' => 'Essential',
                'price' => 12.95,
                'billing_type' => 'yearly',
                'billed_description' => '$155.40 Billed Annually',
                'discount' => 'Savings: $24 Annually',
                'stripe_price_id' => 'price_1RZ03jCQUVLf5GMum5oo4vaP',
                'rules' => [
                    ['key' => 'ads', 'value' => 'No', 'description' => 'No Ads', 'is_allowed' => true],
                    ['key' => 'trade_accounts', 'value' => '3', 'description' => '3 Trade Accounts', 'is_allowed' => true],
                    ['key' => 'trade_history', 'value' => 'Unlimited', 'description' => 'Unlimited Trade History Access', 'is_allowed' => true],
                    ['key' => 'dashboards', 'value' => '10', 'description' => '10 Dashboards', 'is_allowed' => true],
                    ['key' => 'strategy_limit', 'value' => '25', 'description' => '25 Strategy Limit', 'is_allowed' => true],
                    ['key' => 'kpi_widget_types', 'value' => 'Unlimited', 'description' => 'KPI Widget Types (Unlimited)', 'is_allowed' => true],
                    ['key' => 'dashboard_presets', 'value' => '10', 'description' => '10 Dashboard Presets', 'is_allowed' => true],
                    ['key' => 'strategy_presets', 'value' => '10', 'description' => '10 Strategy Presets', 'is_allowed' => true],
                    ['key' => 'sync_brokers', 'value' => 'Yes', 'description' => 'Sync Brokers', 'is_allowed' => true],
                    ['key' => 'csv_import', 'value' => 'Yes', 'description' => 'CSV Trade Import', 'is_allowed' => true],
                    ['key' => 'marketplace_access', 'value' => 'Yes', 'description' => 'Marketplace Access', 'is_allowed' => true],
                    ['key' => 'upload_chart_images', 'value' => 'Yes', 'description' => 'Upload Chart Images', 'is_allowed' => true],
                    ['key' => 'market_replay_limit', 'value' => '25', 'description' => '25 Market Replay Limit', 'is_allowed' => true],
                    ['key' => 'analytics_access', 'value' => 'Yes', 'description' => 'Analytics Report Access', 'is_allowed' => true],
                    ['key' => 'analytics_saved_views', 'value' => '10', 'description' => '10 Analytics Saved Views', 'is_allowed' => true],
                    ['key' => 'custom_tags', 'value' => 'Unlimited', 'description' => 'Custom Tags (Unlimited)', 'is_allowed' => true],
                    ['key' => 'trade_notes', 'value' => 'Yes', 'description' => 'Trade Notes', 'is_allowed' => true],
                    ['key' => 'trade_builder_fields', 'value' => '9', 'description' => 'Trade Builder Extra Fields: 9', 'is_allowed' => true],
                    ['key' => 'refer_friend', 'value' => 'Yes', 'description' => 'Refer A Friend Access', 'is_allowed' => true],
                    ['key' => 'affiliate_partner', 'value' => 'Yes', 'description' => 'Affiliate Partner Access', 'is_allowed' => true],
                    ['key' => 'standard_support', 'value' => 'Yes', 'description' => 'Standard Support (Ticket-based)', 'is_allowed' => true],
                    ['key' => 'marketplace_seller', 'value' => 'Yes', 'description' => 'Marketplace Seller Privileges', 'is_allowed' => true],
                    ['key' => 'marketplace_fee', 'value' => '5%', 'description' => 'Marketplace Seller Fee: 5%', 'is_allowed' => true],
                    ['key' => 'priority_support', 'value' => 'No', 'description' => 'Priority Support', 'is_allowed' => false],
                ],
            ],
            // Plus Monthly
            [
                'title' => 'Plus',
                'price' => 29.95,
                'billing_type' => 'monthly',
                'billed_description' => null,
                'discount' => null,
                'stripe_price_id' => 'price_1RZ04SCQUVLf5GMuUOerNypC',
                'rules' => [
                    ['key' => 'ads', 'value' => 'No', 'description' => 'No Ads', 'is_allowed' => true],
                    ['key' => 'trade_accounts', 'value' => '10', 'description' => '10 Trade Accounts', 'is_allowed' => true],
                    ['key' => 'trade_history', 'value' => 'Unlimited', 'description' => 'Unlimited Trade History Access', 'is_allowed' => true],
                    ['key' => 'dashboards', 'value' => '25', 'description' => '25 Dashboards', 'is_allowed' => true],
                    ['key' => 'strategy_limit', 'value' => 'Unlimited', 'description' => 'Strategy Limit (Unlimited)', 'is_allowed' => true],
                    ['key' => 'kpi_widget_types', 'value' => 'Unlimited', 'description' => 'KPI Widget Types (Unlimited)', 'is_allowed' => true],
                    ['key' => 'dashboard_presets', 'value' => '25', 'description' => '25 Dashboard Presets', 'is_allowed' => true],
                    ['key' => 'strategy_presets', 'value' => '25', 'description' => '25 Strategy Presets', 'is_allowed' => true],
                    ['key' => 'sync_brokers', 'value' => 'Yes', 'description' => 'Sync Brokers', 'is_allowed' => true],
                    ['key' => 'csv_import', 'value' => 'Yes', 'description' => 'CSV Trade Import', 'is_allowed' => true],
                    ['key' => 'marketplace_access', 'value' => 'Yes', 'description' => 'Marketplace Access', 'is_allowed' => true],
                    ['key' => 'upload_chart_images', 'value' => 'Yes', 'description' => 'Upload Chart Images', 'is_allowed' => true],
                    ['key' => 'market_replay_limit', 'value' => '100', 'description' => '100 Market Replay Limit', 'is_allowed' => true],
                    ['key' => 'analytics_access', 'value' => 'Yes', 'description' => 'Analytics Report Access', 'is_allowed' => true],
                    ['key' => 'analytics_saved_views', 'value' => '25', 'description' => '25 Analytics Saved Views', 'is_allowed' => true],
                    ['key' => 'custom_tags', 'value' => 'Unlimited', 'description' => 'Custom Tags (Unlimited)', 'is_allowed' => true],
                    ['key' => 'trade_notes', 'value' => 'Yes', 'description' => 'Trade Notes', 'is_allowed' => true],
                    ['key' => 'trade_builder_fields', 'value' => '24', 'description' => 'Trade Builder Extra Fields: 24', 'is_allowed' => true],
                    ['key' => 'refer_friend', 'value' => 'Yes', 'description' => 'Refer A Friend Access', 'is_allowed' => true],
                    ['key' => 'affiliate_partner', 'value' => 'Yes', 'description' => 'Affiliate Partner Access', 'is_allowed' => true],
                    ['key' => 'standard_support', 'value' => 'Yes', 'description' => 'Standard Support (Ticket-based)', 'is_allowed' => true],
                    ['key' => 'marketplace_seller', 'value' => 'Yes', 'description' => 'Marketplace Seller Privileges', 'is_allowed' => true],
                    ['key' => 'marketplace_fee', 'value' => '3%', 'description' => 'Marketplace Seller Fee: 3%', 'is_allowed' => true],
                    ['key' => 'priority_support', 'value' => 'No', 'description' => 'Priority Support', 'is_allowed' => false],
                ],
            ],
            // Plus Yearly
            [
                'title' => 'Plus',
                'price' => 24.95,
                'billing_type' => 'yearly',
                'billed_description' => '$299.40 Billed Annually',
                'discount' => 'Savings: $60 Annually',
                'stripe_price_id' => 'price_1RZ05KCQUVLf5GMuBV6JYlAG',
                'rules' => [
                    ['key' => 'ads', 'value' => 'No', 'description' => 'No Ads', 'is_allowed' => true],
                    ['key' => 'trade_accounts', 'value' => '10', 'description' => '10 Trade Accounts', 'is_allowed' => true],
                    ['key' => 'trade_history', 'value' => 'Unlimited', 'description' => 'Unlimited Trade History Access', 'is_allowed' => true],
                    ['key' => 'dashboards', 'value' => '25', 'description' => '25 Dashboards', 'is_allowed' => true],
                    ['key' => 'strategy_limit', 'value' => 'Unlimited', 'description' => 'Strategy Limit (Unlimited)', 'is_allowed' => true],
                    ['key' => 'kpi_widget_types', 'value' => 'Unlimited', 'description' => 'KPI Widget Types (Unlimited)', 'is_allowed' => true],
                    ['key' => 'dashboard_presets', 'value' => '25', 'description' => '25 Dashboard Presets', 'is_allowed' => true],
                    ['key' => 'strategy_presets', 'value' => '25', 'description' => '25 Strategy Presets', 'is_allowed' => true],
                    ['key' => 'sync_brokers', 'value' => 'Yes', 'description' => 'Sync Brokers', 'is_allowed' => true],
                    ['key' => 'csv_import', 'value' => 'Yes', 'description' => 'CSV Trade Import', 'is_allowed' => true],
                    ['key' => 'marketplace_access', 'value' => 'Yes', 'description' => 'Marketplace Access', 'is_allowed' => true],
                    ['key' => 'upload_chart_images', 'value' => 'Yes', 'description' => 'Upload Chart Images', 'is_allowed' => true],
                    ['key' => 'market_replay_limit', 'value' => '100', 'description' => '100 Market Replay Limit', 'is_allowed' => true],
                    ['key' => 'analytics_access', 'value' => 'Yes', 'description' => 'Analytics Report Access', 'is_allowed' => true],
                    ['key' => 'analytics_saved_views', 'value' => '25', 'description' => '25 Analytics Saved Views', 'is_allowed' => true],
                    ['key' => 'custom_tags', 'value' => 'Unlimited', 'description' => 'Custom Tags (Unlimited)', 'is_allowed' => true],
                    ['key' => 'trade_notes', 'value' => 'Yes', 'description' => 'Trade Notes', 'is_allowed' => true],
                    ['key' => 'trade_builder_fields', 'value' => '24', 'description' => 'Trade Builder Extra Fields: 24', 'is_allowed' => true],
                    ['key' => 'refer_friend', 'value' => 'Yes', 'description' => 'Refer A Friend Access', 'is_allowed' => true],
                    ['key' => 'affiliate_partner', 'value' => 'Yes', 'description' => 'Affiliate Partner Access', 'is_allowed' => true],
                    ['key' => 'standard_support', 'value' => 'Yes', 'description' => 'Standard Support (Ticket-based)', 'is_allowed' => true],
                    ['key' => 'marketplace_seller', 'value' => 'Yes', 'description' => 'Marketplace Seller Privileges', 'is_allowed' => true],
                    ['key' => 'marketplace_fee', 'value' => '3%', 'description' => 'Marketplace Seller Fee: 3%', 'is_allowed' => true],
                    ['key' => 'priority_support', 'value' => 'No', 'description' => 'Priority Support', 'is_allowed' => false],
                ],
            ],
            // Premium Monthly
            [
                'title' => 'Premium',
                'price' => 49.95,
                'billing_type' => 'monthly',
                'billed_description' => null,
                'discount' => null,
                'stripe_price_id' => 'price_1RZ05wCQUVLf5GMuonqYlKbA',
                'rules' => [
                    ['key' => 'ads', 'value' => 'No', 'description' => 'No Ads', 'is_allowed' => true],
                    ['key' => 'trade_accounts', 'value' => '25', 'description' => '25 Trade Accounts', 'is_allowed' => true],
                    ['key' => 'trade_history', 'value' => 'Unlimited', 'description' => 'Unlimited Trade History Access', 'is_allowed' => true],
                    ['key' => 'dashboards', 'value' => '100', 'description' => '100 Dashboards', 'is_allowed' => true],
                    ['key' => 'strategy_limit', 'value' => 'Unlimited', 'description' => 'Strategy Limit (Unlimited)', 'is_allowed' => true],
                    ['key' => 'kpi_widget_types', 'value' => 'Unlimited', 'description' => 'KPI Widget Types (Unlimited)', 'is_allowed' => true],
                    ['key' => 'dashboard_presets', 'value' => '50', 'description' => '50 Dashboard Presets', 'is_allowed' => true],
                    ['key' => 'strategy_presets', 'value' => '50', 'description' => '50 Strategy Presets', 'is_allowed' => true],
                    ['key' => 'sync_brokers', 'value' => 'Yes', 'description' => 'Sync Brokers', 'is_allowed' => true],
                    ['key' => 'csv_import', 'value' => 'Yes', 'description' => 'CSV Trade Import', 'is_allowed' => true],
                    ['key' => 'marketplace_access', 'value' => 'Yes', 'description' => 'Marketplace Access', 'is_allowed' => true],
                    ['key' => 'upload_chart_images', 'value' => 'Yes', 'description' => 'Upload Chart Images', 'is_allowed' => true],
                    ['key' => 'market_replay_limit', 'value' => 'Unlimited', 'description' => 'Market Replay Limit (Unlimited)', 'is_allowed' => true],
                    ['key' => 'analytics_access', 'value' => 'Yes', 'description' => 'Analytics Report Access', 'is_allowed' => true],
                    ['key' => 'analytics_saved_views', 'value' => 'Unlimited', 'description' => 'Analytics Saved Views (Unlimited)', 'is_allowed' => true],
                    ['key' => 'custom_tags', 'value' => 'Unlimited', 'description' => 'Custom Tags (Unlimited)', 'is_allowed' => true],
                    ['key' => 'trade_notes', 'value' => 'Yes', 'description' => 'Trade Notes', 'is_allowed' => true],
                    ['key' => 'trade_builder_fields', 'value' => '45', 'description' => 'Trade Builder Extra Fields: 45', 'is_allowed' => true],
                    ['key' => 'refer_friend', 'value' => 'Yes', 'description' => 'Refer A Friend Access', 'is_allowed' => true],
                    ['key' => 'affiliate_partner', 'value' => 'Yes', 'description' => 'Affiliate Partner Access', 'is_allowed' => true],
                    ['key' => 'standard_support', 'value' => 'Yes', 'description' => 'Standard Support (Priority Support)', 'is_allowed' => true],
                    ['key' => 'marketplace_seller', 'value' => 'Yes', 'description' => 'Marketplace Seller Privileges', 'is_allowed' => true],
                    ['key' => 'marketplace_fee', 'value' => '1%', 'description' => 'Marketplace Seller Fee: 1%', 'is_allowed' => true],
                    ['key' => 'priority_support', 'value' => 'Yes', 'description' => 'Priority Support (Faster Response Time)', 'is_allowed' => true],
                ],
            ],
            // Premium Yearly
            [
                'title' => 'Premium',
                'price' => 39.95,
                'billing_type' => 'yearly',
                'billed_description' => '$479.40 Billed Annually',
                'discount' => 'Savings: $120 Annually',
                'stripe_price_id' => 'price_1RZ06pCQUVLf5GMuoibjuxEg',
                'rules' => [
                    ['key' => 'ads', 'value' => 'No', 'description' => 'No Ads', 'is_allowed' => true],
                    ['key' => 'trade_accounts', 'value' => '25', 'description' => '25 Trade Accounts', 'is_allowed' => true],
                    ['key' => 'trade_history', 'value' => 'Unlimited', 'description' => 'Unlimited Trade History Access', 'is_allowed' => true],
                    ['key' => 'dashboards', 'value' => '100', 'description' => '100 Dashboards', 'is_allowed' => true],
                    ['key' => 'strategy_limit', 'value' => 'Unlimited', 'description' => 'Strategy Limit (Unlimited)', 'is_allowed' => true],
                    ['key' => 'kpi_widget_types', 'value' => 'Unlimited', 'description' => 'KPI Widget Types (Unlimited)', 'is_allowed' => true],
                    ['key' => 'dashboard_presets', 'value' => '50', 'description' => '50 Dashboard Presets', 'is_allowed' => true],
                    ['key' => 'strategy_presets', 'value' => '50', 'description' => '50 Strategy Presets', 'is_allowed' => true],
                    ['key' => 'sync_brokers', 'value' => 'Yes', 'description' => 'Sync Brokers', 'is_allowed' => true],
                    ['key' => 'csv_import', 'value' => 'Yes', 'description' => 'CSV Trade Import', 'is_allowed' => true],
                    ['key' => 'marketplace_access', 'value' => 'Yes', 'description' => 'Marketplace Access', 'is_allowed' => true],
                    ['key' => 'upload_chart_images', 'value' => 'Yes', 'description' => 'Upload Chart Images', 'is_allowed' => true],
                    ['key' => 'market_replay_limit', 'value' => 'Unlimited', 'description' => 'Market Replay Limit (Unlimited)', 'is_allowed' => true],
                    ['key' => 'analytics_access', 'value' => 'Yes', 'description' => 'Analytics Report Access', 'is_allowed' => true],
                    ['key' => 'analytics_saved_views', 'value' => 'Unlimited', 'description' => 'Analytics Saved Views (Unlimited)', 'is_allowed' => true],
                    ['key' => 'custom_tags', 'value' => 'Unlimited', 'description' => 'Custom Tags (Unlimited)', 'is_allowed' => true],
                    ['key' => 'trade_notes', 'value' => 'Yes', 'description' => 'Trade Notes', 'is_allowed' => true],
                    ['key' => 'trade_builder_fields', 'value' => '45', 'description' => 'Trade Builder Extra Fields: 45', 'is_allowed' => true],
                    ['key' => 'refer_friend', 'value' => 'Yes', 'description' => 'Refer A Friend Access', 'is_allowed' => true],
                    ['key' => 'affiliate_partner', 'value' => 'Yes', 'description' => 'Affiliate Partner Access', 'is_allowed' => true],
                    ['key' => 'standard_support', 'value' => 'Yes', 'description' => 'Standard Support (Priority Support)', 'is_allowed' => true],
                    ['key' => 'marketplace_seller', 'value' => 'Yes', 'description' => 'Marketplace Seller Privileges', 'is_allowed' => true],
                    ['key' => 'marketplace_fee', 'value' => '1%', 'description' => 'Marketplace Seller Fee: 1%', 'is_allowed' => true],
                    ['key' => 'priority_support', 'value' => 'Yes', 'description' => 'Priority Support (Faster Response Time)', 'is_allowed' => true],
                ],
            ],
        ];

        foreach ($plans as $planData) {
            $rules = $planData['rules'];
            unset($planData['rules']);

            $plan = Plan::create($planData);

            foreach ($rules as $rule) {
                PlanRule::create([
                    'plan_id' => $plan->id,
                    'key' => $rule['key'],
                    'value' => $rule['value'],
                    'description' => $rule['description'],
                    'is_allowed' => $rule['is_allowed'],
                ]);
            }
        }
    }
}
