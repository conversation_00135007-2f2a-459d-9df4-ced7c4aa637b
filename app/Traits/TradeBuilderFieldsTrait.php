<?php

namespace App\Traits;

trait TradeBuilderFieldsTrait
{
    /**
     * Entry Overview Fields
     */
    private array $entryOverviewFields = [
        'portfolio_capital_reserve', 'portfolio_trade_reserve', 'portfolio_account_size',
        'portfolio_account_available', 'transaction_asset_type', 'transaction_ticker',
        'transaction_position_type', 'transaction_order_type', 'transaction_status',
        'transaction_entry_date', 'transaction_entry_time', 'transaction_leverage_factor',
        'transaction_entry_price', 'transaction_target_price', 'transaction_stop_loss_value',
        'transaction_risk_percentage', 'transaction_quantity_purchased', 'transaction_investment_value'
    ];

    /**
     * Exit Overview Fields
     */
    private array $exitOverviewFields = [
        'portfolio_capital_reserve', 'portfolio_trade_reserve', 'portfolio_account_size',
        'portfolio_account_available', 'transaction_asset_type', 'transaction_ticker',
        'transaction_position_type', 'transaction_order_type', 'transaction_status',
        'transaction_exit_date', 'transaction_exit_time', 'transaction_leverage_factor',
        'transaction_exit_price', 'transaction_target_price', 'transaction_stop_loss_value',
        'transaction_risk_percentage', 'transaction_quantity_closed', 'transaction_investment_value'
    ];

    /**
     * Projection Fields for Entry and Exit
     */
    private array $projectionFields = [
        'entry' => [
            'portfolio_account_growth_goal', 'portfolio_account_growth_value_of_goal', 'portfolio_account_growth_percentage_of_goal',
            'transaction_risk_per_share_value', 'transaction_risk_per_share_percentage', 'transaction_investment_percentage',
            'transaction_optimal_quantity', 'transaction_desired_profit', 'transaction_reward_value',
            'transaction_risk_reward_ratio', 'transaction_take_profit_value', 'transaction_take_profit_percentage'
        ],
        'exit'  => [
            'portfolio_account_growth_goal', 'portfolio_account_growth_value_of_goal', 'portfolio_account_growth_percentage_of_goal',
            'transaction_risk_per_share_value', 'transaction_risk_per_share_percentage', 'transaction_investment_percentage',
            'transaction_optimal_quantity', 'transaction_desired_profit', 'transaction_reward_value',
            'transaction_risk_reward_ratio', 'transaction_take_profit_value', 'transaction_take_profit_percentage'
        ]
    ];

    /**
     * Outcome Fields for Entry and Exit
     */
    private array $outcomeFields = [
        'entry' => [
            'transaction_outcome', 'transaction_realized_pl_profit_loss', 'transaction_realized_p&l_(profit_&_loss)', 'transaction_quantity_closed',
            'transaction_full_profit_value', 'transaction_full_profit_percentage', 'transaction_full_profit_efficiency_percentage',
            'transaction_full_profit_missed_value', 'transaction_deviation_profit_value', 'transaction_deviation_profit_percentage',
            'portfolio_wins', 'portfolio_losses', 'portfolio_win_rate'
        ],
        'exit'  => [
            'transaction_outcome', 'transaction_realized_pl_profit_loss', 'transaction_realized_p&l_(profit_&_loss)', 'transaction_quantity_closed',
            'transaction_full_profit_value', 'transaction_full_profit_percentage', 'transaction_full_profit_efficiency_percentage',
            'transaction_full_profit_missed_value', 'transaction_deviation_profit_value', 'transaction_deviation_profit_percentage',
            'portfolio_wins', 'portfolio_losses', 'portfolio_win_rate',
        ]
    ];
}
