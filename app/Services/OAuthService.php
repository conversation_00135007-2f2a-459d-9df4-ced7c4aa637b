<?php
namespace App\Services;

use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Laravel\Socialite\Facades\Socialite;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use SendGrid\Mail\Mail;

class OAuthService
{

    use ApiResponseTrait;


    protected $user;
    protected $verificationTemplateId;
    protected $forgetPasswordTemplateId;

    public function __construct()
    {
        // $this->authServices = $authServices;
        $this->user = new User();
        $this->forgetPasswordTemplateId = config('mail.templates.forget_password') ?? 'd-94e352c92bdd4f77b11ce496fbde15eb';
        $this->verificationTemplateId = config('mail.templates.verification_token') ?? 'd-9f8503c021af4f98bc833326a6aea6a5';
    }
    public function redirect($provider, $uuid,$mode)
    {
        $state = base64_encode(json_encode([
            'uuid' => $uuid,
            'mode' => $mode,
        ]));

//dd($state);
        return Socialite::driver($provider)
            ->stateless()
            ->with(['state' => $state])
            ->setHttpClient(new \GuzzleHttp\Client(['verify' => false]))
            ->redirect();
    }


    public function handleCallback($provider)
    {
        $stateRaw = request()->get('state');

        $state = json_decode(base64_decode($stateRaw), true);

        $uuid = $state['uuid'] ?? null;
        $mode = $state['mode'] ?? 'register';
        $socialiteUser = Socialite::driver($provider)
            ->stateless()
            ->setHttpClient(new \GuzzleHttp\Client(['verify' => false]))
            ->user();

        if ($mode === 'login') {
            return $this->handleOAuthLogin($socialiteUser, $provider, $uuid);
        } else {
            return $this->handleOAuthRegisteration($socialiteUser, $provider, $uuid);
        }
    }




    protected function handleOAuthRegisteration($socialiteUser, $provider, $uuid)
    {
        try {
            $token = $this->generateToken();
            $tokenExpiresAt = now()->addMinutes(15)->toISOString();
            $cacheKey = "signup_{$provider}_{$uuid}";

            $providerIdKey = $provider . '_id';

            $cacheData = [
                'email' => $socialiteUser->getEmail(),
                'password' => Hash::make($socialiteUser->getEmail() . "randomString_rZ7^Lm2#Wx"),
                'token' => $token,
                'token_expires_at' => $tokenExpiresAt,
                $providerIdKey => $socialiteUser->getId(),
                'avatar' => $socialiteUser->getAvatar(),
                'verified' => false,
            ];

            Cache::put($cacheKey, $cacheData, $tokenExpiresAt);

            $this->sendVerificationEmail($socialiteUser->getEmail(), $token, $this->verificationTemplateId);

            return redirect(
                rtrim(env('FRONTEND_URL', 'http://localhost:3000'), '/') .
                "/security-check?provider={$provider}"
            );

        } catch (\Exception $e) {
            return $this->errorResponse('Verification Email Sending Failed', 400);
        }
    }

    public function handleOAuthLogin($socialiteUser, $provider, $uuid = null)
    {
        $email = $socialiteUser->getEmail();

        $user = User::where('email', $email)->first();

        if (!$user) {
            return redirect()->to('/login?error=account_not_found');
        }

        $providerIdField = $provider . '_id';
        $providerId = $socialiteUser->getId();

        if (empty($user->{$providerIdField})) {
            $user->{$providerIdField} = $providerId;
            $user->save();
        }

        if (!$user->avatar && $socialiteUser->getAvatar()) {
            $user->avatar = $socialiteUser->getAvatar();
            $user->save();
        }


        Auth::login($user);

        $token = $user->createToken('API Token')->plainTextToken;
        $userData = $user->only(['id', 'email', 'name', 'username', 'role']);

        return response()->json([
            'success' => true,
            'message' => 'API login successful',
            'data' =>  [
                'user' => $userData,
                'captcha_required' => false,
                'token' => $token
            ],
        ], 200);


    }



    private function generateToken()
    {
        $characters = 'ACDEFGHJKMNPQRTUVWXYZ234679';
        return substr(str_shuffle(str_repeat($characters, 6)), 0, 6);
    }

    private function sendVerificationEmail($email, $token, $template_id,$username = null, $type = 'signup')
    {
        $sendgrid = new \SendGrid(env('SENDGRID_API_KEY'), ['verify_ssl' => false]);

        $message = new Mail();
        $message->setFrom('<EMAIL>', 'TradeReply');
        $message->setReplyTo('<EMAIL>', 'Support Team');
        $message->setSubject("Verify Your Account");
        $message->addTo($email);
        $message->setTemplateId($template_id);
        $message->addDynamicTemplateData('accountVerificationToken', $token);
        if($type == 'forgot_password'){
            $message->addDynamicTemplateData('accountUsername', $username);
            $message->addDynamicTemplateData('accountEmail', $email);
        }

        try {
            $response = $sendgrid->send($message);
            if ($response->statusCode() >= 200 && $response->statusCode() < 300) {
                return true;
            }
        } catch (\Exception $e) {
            \Log::error("SendGrid Email Error: " . $e->getMessage());
        }

        return false;
    }

}
