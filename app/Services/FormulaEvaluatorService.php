<?php

namespace App\Services;

class FormulaEvaluatorService
{
    protected array $formulas;
    protected array $inputs;
    protected array $locked;
    protected array $original;
    protected array $calculated = [];
    protected array $dependencies = [];

    public function calculate(array $inputs, array $locked, array $original, array $formulas): array
    {
        $this->inputs = $inputs;
        $this->original = $original;
        $this->locked = $locked;
        $this->calculated = [];
        $this->dependencies = $this->buildDependencyMap($formulas);

        $updatedFields = [];
        foreach ($inputs as $key => $value) {
            if (!array_key_exists($key, $original) || $original[$key] !== $value) {
                $updatedFields[] = $key;
            }
        }

        foreach ($formulas as $fieldGroup) {
            foreach ($fieldGroup['SCOPES'] ?? [] as $scope) {
                $targetField = $scope['DATABASE FIELD'] ?? null;
                $formula = $scope['FORMULA'] ?? null;

                if (!$targetField || !$formula || ($formula['usr'] ?? 0) != 1) continue;
                if ($this->locked[$targetField] ?? false) continue;

                $dependsOn = $this->dependencies[$targetField] ?? [];
                if (!array_intersect($updatedFields, $dependsOn)) continue;

                $value = $this->evaluateFallbackFormula($formula);
                if (!is_null($value)) {
                    $this->calculated[$targetField] = round($value, 2);
                }
            }
        }

        return $this->calculated;
    }

    protected function buildDependencyMap(array $formulas): array
    {
        $map = [];

        foreach ($formulas as $fieldGroup) {
            foreach ($fieldGroup['SCOPES'] ?? [] as $scope) {
                $target = $scope['DATABASE FIELD'] ?? null;
                $formula = $scope['FORMULA'] ?? null;
                if (!$target || !$formula) continue;

                $deps = [];
                for ($i = 1; $i <= 5; $i++) {
                    if (!empty($formula["f{$i}"]) && isset($formula["f{$i}v"])) {
                        $fields = $this->extractFields($formula["f{$i}v"]);
                        $deps = array_merge($deps, $fields);
                    }
                }

                $map[$target] = $deps;
            }
        }

        return $map;
    }

    protected function extractFields($formula): array
    {
        $fields = [];

        if (is_string($formula)) return [$formula];
        if (!is_array($formula)) return [];

        if (!empty($formula['field'])) {
            $fields[] = $formula['field'];
        }

        if (!empty($formula['condition']['field'])) {
            $fields[] = $formula['condition']['field'];
        }

        foreach ($formula['fields'] ?? [] as $child) {
            $fields = array_merge($fields, $this->extractFields($child));
        }

        if (!empty($formula['true_case'])) {
            $fields = array_merge($fields, $this->extractFields($formula['true_case']));
        }

        if (!empty($formula['false_case'])) {
            $fields = array_merge($fields, $this->extractFields($formula['false_case']));
        }

        return $fields;
    }

    protected function evaluateFallbackFormula(array $formula): float|int|string|null
    {
        for ($i = 1; $i <= 5; $i++) {
            if (!empty($formula["f{$i}"]) && !empty($formula["f{$i}v"])) {
                return $this->evaluateFormula($formula["f{$i}v"]);
            }
        }
        return null;
    }

    protected function evaluateFormula(array|string $formula): float|int|string|null
    {
        if (is_string($formula)) {
            return $this->inputs[$formula] ?? null;
        }

        if (!is_array($formula)) return null;

        $operation = strtoupper($formula['operation'] ?? '');

        switch ($operation) {
            case 'ADD':
                return array_sum(array_map([$this, 'evaluateFormula'], $formula['fields'] ?? []));

            case 'SUBTRACT':
                return $this->evaluateFormula($formula['fields'][0]) - $this->evaluateFormula($formula['fields'][1]);

            case 'MULTIPLY':
                return array_product(array_map([$this, 'evaluateFormula'], $formula['fields'] ?? []));

            case 'DIVIDE':
                $denominator = $this->evaluateFormula($formula['fields'][1]);
                return $denominator == 0 ? 0 : $this->evaluateFormula($formula['fields'][0]) / $denominator;

            case 'ABS':
                return abs($this->evaluateFormula($formula['fields'][0]));

            case 'REFERENCE':
                $ref = $formula['field'] ?? null;
                return $ref ? ($this->inputs[$ref] ?? null) : null;

            case 'IF':
                $cond = $formula['condition'] ?? [];
                $true = $formula['true_case'] ?? null;
                $false = $formula['false_case'] ?? null;

                if (isset($cond['field'], $cond['operator'], $cond['value'])) {
                    $val = $this->inputs[$cond['field']] ?? null;
                    $match = match ($cond['operator']) {
                        '=', '==' => $val == $cond['value'],
                        '!=', '<>' => $val != $cond['value'],
                        '>' => $val > $cond['value'],
                        '>=' => $val >= $cond['value'],
                        '<' => $val < $cond['value'],
                        '<=' => $val <= $cond['value'],
                        default => false,
                    };
                    return $this->evaluateFormula($match ? $true : $false);
                }
                return null;

            case 'DATE_FORMAT':
                $field = $formula['field'] ?? null;
                $format = $formula['format'] ?? 'Y-m-d';
                if (!$field || !isset($this->inputs[$field])) return null;
                $timestamp = strtotime($this->inputs[$field]);
                return $timestamp ? date($format, $timestamp) : null;

            default:
                return null;
        }
    }

    public function loadFormulaJson(): array
    {
        $file = public_path('TradeReply_Formulas.json');
        if (!file_exists($file)) {
            throw new \Exception('Formula JSON not found.');
        }
        return json_decode(file_get_contents($file), true);
    }
}
