<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Enums\ArticleType;

class ArticleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Modify authorization logic if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required', 'string'],
            'type' => ['required', Rule::in([ArticleType::EDUCATION->value, ArticleType::BLOG->value])],
            'feature_image' => ['nullable', 'string', 'max:255'],
            'primary_category_id' => ['required', 'exists:categories,id'],
            'is_featured' => ['nullable', 'boolean'],
            'summary' => ['nullable', 'string', 'max:500'],
            'clicks' => ['nullable', 'integer', 'min:0'],
            'secondary_categories' => ['nullable', 'array'],
            'secondary_categories.*' => ['exists:categories,id'],
        ];
    }

    /**
     * Custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The article title is required.',
            'content.required' => 'The article content is required.',
            'type.required' => 'The article type is required.',
            'type.in' => 'The selected type is invalid.',
            'primary_category_id.required' => 'A primary category is required.',
            'primary_category_id.exists' => 'The selected primary category is invalid.',
            'clicks.integer' => 'Clicks must be a valid number.',
            'secondary_categories.*.exists' => 'One or more selected secondary categories are invalid.',
        ];
    }
}
