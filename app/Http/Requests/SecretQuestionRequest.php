<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SecretQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'questions' => 'required|array|min:1|max:2',
            'questions.*.question' => 'required|string|max:500',
            'questions.*.answer' => 'required|string|min:3|max:64',
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'questions.required' => 'At least one secret question is required.',
            'questions.array' => 'Questions must be provided as an array.',
            'questions.min' => 'At least one secret question is required.',
            'questions.max' => 'Maximum of 2 secret questions allowed.',
            'questions.*.question.required' => 'Question text is required.',
            'questions.*.question.string' => 'Question must be a valid text.',
            'questions.*.question.max' => 'Question cannot exceed 500 characters.',
            'questions.*.answer.required' => 'Answer is required for each question.',
            'questions.*.answer.string' => 'Answer must be a valid text.',
            'questions.*.answer.min' => 'Answer must be at least 3 characters long.',
            'questions.*.answer.max' => 'Answer cannot exceed 64 characters.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $questions = $this->input('questions', []);
            
            // Check for duplicate questions
            $questionTexts = array_column($questions, 'question');
            if (count($questionTexts) !== count(array_unique($questionTexts))) {
                $validator->errors()->add('questions', 'You cannot select the same question twice.');
            }
        });
    }
}
