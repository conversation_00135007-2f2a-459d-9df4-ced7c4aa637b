<?php
namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => 'required|string|email:rfc|max:100',
            'password' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => 'The email field is required.',
            'email.string' => 'The email must be a valid string.',
            'email.email' => 'Please enter a valid email address with a domain extension (e.g., ".com", ".net").',
            'email.max' => 'The email must not exceed 100 characters.',
            'password.required' => 'The password field is required.',
            'password.string' => 'The password must be a valid string.',
            // 'password.min' => 'The password must be at least 8 characters long.',
        ];
    }
}
