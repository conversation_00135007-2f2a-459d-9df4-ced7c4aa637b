<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;


class EducationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id ?? null,
            'title' => $this->title ?? null,
            'slug' => $this->slug ?? null,
            'summary' => isset($this->summary) ? mb_strimwidth($this->summary, 0, 250, '...') : null, // Trimmed summary
            'content' => $this->content ?? null,
            'feature_image' => $this->feature_image_url,  // Fetch the correct S3 URL
            'clicks' => $this->clicks ?? null,
            'is_featured' => (bool) ($this->is_featured ?? false), // Ensure correct boolean handling
            'primary_category' => new CategoryResource($this->primaryCategory) ?? null,
            'progress' => new UserArticleResource($this->progressPercentage) ?? null,
            'secondary_categories' => !is_array($this->secondary_categories) ? json_decode($this->secondary_categories, true) : ($this->secondary_categories ?? []), // Ensure it's decoded JSON
            'created_at' => $this->created_at?->toDateTimeString() ?? '',
            'updated_at' => $this->updated_at?->toDateTimeString() ?? '',
        ];
    }


}
