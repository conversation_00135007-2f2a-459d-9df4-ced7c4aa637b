<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SitemapCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->title ?? '',
            'content' => $this->content ?? '',
            'slug' => $this->slug ?? '',
            'blog' => isset($this->primaryBlogs) && $this->primaryBlogs->isNotEmpty()
                ? SitemapBlogResource::collection($this->primaryBlogs) : [],
            'created_at' => $this->created_at ? $this->created_at->toDateTimeString() : null,
            'updated_at' => $this->updated_at ? $this->updated_at->toDateTimeString() : null,
        ];
    }

}
