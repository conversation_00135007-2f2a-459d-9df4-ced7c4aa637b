<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SitemapBlogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->getCleanSlug(),
            'content' => $this->content,
            'summary' => $this->summary,
            'feature_image' => $this->feature_image,
            'tags' => $this->tags,
            'clicks' => $this->clicks,
            'is_featured' => (bool)$this->is_featured,
            'created_at' => $this->created_at ?  $this->created_at->toDateTimeString() : null,
            'updated_at' => $this->updated_at ?  $this->updated_at->toDateTimeString() : null,
        ];
    }
}
