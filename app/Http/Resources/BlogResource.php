<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class BlogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id ?? null,
            'title' => $this->title ?? null,
            'slug' => $this->slug ?? null,
            'content' => $this->content ?? null,
            'summary' => $this->summary ?? null,
            'feature_image' => $this->feature_image_url,  // Fetch the correct S3 URL
            'tags' => $this->tags ?? null,
            'clicks' => $this->clicks ?? null,
            'is_featured' => (bool)$this->is_featured ?? null,
            'primary_category' => new CategoryResource($this->primaryCategory) ?? null,
            'secondary_categories' => !is_array($this->secondary_categories) ? json_decode($this->secondary_categories, true) : ($this->secondary_categories ?? []), // Ensure it's decoded JSON
            'created_at' => $this->created_at ?  $this->created_at->toDateTimeString() : null,
            'updated_at' => $this->updated_at ?  $this->updated_at->toDateTimeString() : null,
        ];
    }
}
