<?php

// app/Http/Middleware/BasicLogViewerAuth.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BasicLogViewerAuth
{
    public function handle(Request $request, Closure $next)
    {
        $user = $request->getUser();
        $pass = $request->getPassword();

        $validUser = 'devs';
        $validPass = 'Password123@';

        if ($user !== $validUser || $pass !== $validPass) {
            $headers = ['WWW-Authenticate' => 'Basic realm="Log Viewer"'];
            return response('Unauthorized.', Response::HTTP_UNAUTHORIZED, $headers);
        }

        return $next($request);
    }
}

