<?php

namespace App\Http\Controllers;

use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AddressController extends Controller
{
    use ApiResponseTrait;

    /**
     * Get all addresses for the authenticated user
     */
    public function index()
    {
        try {
            $user = Auth::user();
            $addresses = $user->addresses()
                ->orderByDesc('is_default')
                ->orderBy('created_at')
                ->get();

            // Mask sensitive information
            $maskedAddresses = $addresses->map(function ($address) {
                return [
                    'id' => $address->id,
                    'first_name' => $this->maskName($address->first_name),
                    'last_name' => $this->maskName($address->last_name),
                    'full_name' => $this->maskName($address->full_name),
                    'address' => $this->maskAddress($address->address),
                    'city' => $address->city,
                    'state' => $address->state,
                    'zip_code' => $address->zip_code,
                    'country' => $address->country,
                    'is_default' => $address->is_default,
                    'formatted_address' => $address->formatted_address,
                ];
            });

            return $this->successResponse($maskedAddresses, 'Addresses retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Failed to retrieve addresses: ' . $e->getMessage());
            return $this->errorResponse('Failed to retrieve addresses. Please try again.', 500);
        }
    }

    /**
     * Get a specific address for editing (returns unmasked data)
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            $address = $user->addresses()->findOrFail($id);

            return $this->successResponse($address, 'Address retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Failed to retrieve address: ' . $e->getMessage());
            return $this->errorResponse('Address not found.', 404);
        }
    }

    /**
     * Store a new address
     */
    public function store(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'zip_code' => 'required|string|max:20',
            'country' => 'required|string|max:255',
            'is_default' => 'boolean'
        ]);

        try {
            $user = Auth::user();

            $address = $user->addresses()->create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'zip_code' => $request->zip_code,
                'country' => $request->country,
                'is_default' => $request->boolean('is_default', false)
            ]);

            return $this->successResponse([
                'id' => $address->id,
                'message' => 'Address added successfully'
            ], 'Address created successfully');

        } catch (\Exception $e) {
            Log::error('Failed to create address: ' . $e->getMessage());
            return $this->errorResponse('Failed to create address. Please try again.', 500);
        }
    }

    /**
     * Update an existing address
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'zip_code' => 'required|string|max:20',
            'country' => 'required|string|max:255',
            'is_default' => 'boolean'
        ]);

        try {
            $user = Auth::user();
            $address = $user->addresses()->findOrFail($id);

            $address->update([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'zip_code' => $request->zip_code,
                'country' => $request->country,
                'is_default' => $request->boolean('is_default', false)
            ]);

            return $this->successResponse([
                'id' => $address->id,
                'message' => 'Address updated successfully'
            ], 'Address updated successfully');

        } catch (\Exception $e) {
            Log::error('Failed to update address: ' . $e->getMessage());
            return $this->errorResponse('Failed to update address. Please try again.', 500);
        }
    }

    /**
     * Set an address as default
     */
    public function setDefault($id)
    {
        try {
            $user = Auth::user();
            $address = $user->addresses()->findOrFail($id);

            $address->update(['is_default' => true]);

            return $this->successResponse([
                'id' => $address->id,
                'message' => 'Default address updated successfully'
            ], 'Default address set successfully');

        } catch (\Exception $e) {
            Log::error('Failed to set default address: ' . $e->getMessage());
            return $this->errorResponse('Failed to set default address. Please try again.', 500);
        }
    }

    /**
     * Delete an address
     */
    public function destroy($id)
    {
        try {
            $user = Auth::user();
            $address = $user->addresses()->findOrFail($id);

            // Prevent deletion if it's the only address
            if ($user->addresses()->count() === 1) {
                return $this->errorResponse('Cannot delete the only address. Please add another address first.', 422);
            }

            $address->delete();

            return $this->successResponse([], 'Address deleted successfully');

        } catch (\Exception $e) {
            Log::error('Failed to delete address: ' . $e->getMessage());
            return $this->errorResponse('Failed to delete address. Please try again.', 500);
        }
    }

    /**
     * Mask name for display (A**** M*****)
     */
    private function maskName($name)
    {
        if (!$name || strlen($name) === 0) return '';
        if (strlen($name) === 1) return $name;
        return $name[0] . str_repeat('*', strlen($name) - 1);
    }

    /**
     * Mask address for display (2****************)
     */
    private function maskAddress($address)
    {
        if (!$address || strlen($address) === 0) return '';
        if (strlen($address) === 1) return $address;
        return $address[0] . str_repeat('*', strlen($address) - 1);
    }
}
