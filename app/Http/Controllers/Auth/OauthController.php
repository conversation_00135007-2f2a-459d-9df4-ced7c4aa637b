<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Laravel\Socialite\Facades\Socialite;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Services\OAuthService;
class OauthController extends Controller
{
    protected $oauthService;

    public function __construct(OAuthService $oauthService)
    {
        $this->oauthService = $oauthService;
    }

    public function registerationRedirect(Request $request)
    {
        $uuid = $request->query('uuid');
//        dd($uuid);
        return $this->oauthService->redirect('google',$uuid,'register');
    }

    public function loginRedirect()
    {
        return $this->oauthService->redirect('google',null,'login');
    }

    public function googleCallback()
    {
        return $this->oauthService->handleCallback('google');

    }

    public function facebookRedirect(Request $request)
    {
        $uuid = $request->query('uuid');
        return $this->oauthService->redirect('facebook',$uuid);
    }

    public function facebookCallback()
    {
        return $this->oauthService->handleCallback('facebook');
    }


    public function fetchSignupEmail($provider, $uuid)
    {
        $cacheKey = "signup_{$provider}_{$uuid}";
        $data = Cache::get($cacheKey);

        if (!$data || !isset($data['email']) || now()->gt($data['token_expires_at'] ?? now())) {
            return response()->json(['error' => 'Signup session expired or not found.'], 404);
        }

        return response()->json([
            'email' => $data['email'],
        ]);
    }

}
