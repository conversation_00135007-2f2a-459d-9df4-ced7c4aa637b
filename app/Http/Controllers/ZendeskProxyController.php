<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class ZendeskProxyController extends Controller
{
    public function handle($path, Request $request)
    {
        $zendeskDomain = config('services.zendesk.domain'); // e.g., https://tradereply.zendesk.com/api/v2/
        $token = config('services.zendesk.token');

        $fullUrl = rtrim($zendeskDomain, '/') . '/' . ltrim($path, '/');

        $http = Http::withToken($token)->acceptJson();

        switch (strtolower($request->method())) {
            case 'get':
                $response = $http->get($fullUrl, $request->query());
                break;
            case 'post':
                $response = $http->post($fullUrl, $request->all());
                break;
            case 'put':
                $response = $http->put($fullUrl, $request->all());
                break;
            case 'delete':
                $response = $http->delete($fullUrl);
                break;
            default:
                return response()->json(['error' => 'Unsupported method'], 405);
        }

        return response($response->body(), $response->status())
            ->header('Content-Type', $response->header('Content-Type'));
    }

}

