<?php

namespace App\Http\Controllers;

use App\Models\LoginActivity;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginActivityController extends Controller
{
    use ApiResponseTrait;

    /**
     * Get recent login activities for the authenticated user
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();

            $currentToken = $request->bearerToken();

            $activities = LoginActivity::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($activity) use ($currentToken) {
                    return [
                        'id' => $activity->id,
                        'site' => $activity->site_name,
                        'device' => $activity->platform . ' ' . $activity->device_type,
                        'browser' => $activity->browser,
                        'date' => $activity->formatted_date,
                        'location' => $activity->location,
                        'ip_address' => $activity->ip_address,
                        'is_current' => $activity->session_token === $currentToken && !$activity->logged_out_at,
                        'logged_out_at' => $activity->logged_out_at,
                    ];
                });

            return $this->successResponse($activities, 'Login activities retrieved successfully');

        } catch (\Exception $e) {
            \Log::error('Error fetching login activities: ' . $e->getMessage());
            return $this->errorResponse(['Failed to fetch login activities', $e->getMessage()], 500);
        }
    }

    /**
     * Logout from all devices except current
     */
    public function logoutAllDevices(Request $request)
    {
        try {
            $user = Auth::user();
            $currentToken = $request->bearerToken();

            // Get all tokens except current one
            $tokens = $user->tokens()->where('token', '!=', hash('sha256', $currentToken))->get();

            // Delete all other tokens
            foreach ($tokens as $token) {
                $token->delete();
            }

            // Mark login activities as logged out (except current session)
            LoginActivity::where('user_id', $user->id)
                ->whereNull('logged_out_at')
                ->where('session_token', '!=', $currentToken)
                ->update(['logged_out_at' => now()]);

            return $this->successResponse([], 'Successfully logged out from all other devices');

        } catch (\Exception $e) {
            \Log::error('Error logging out from all devices: ' . $e->getMessage());
            return $this->errorResponse('Failed to logout from all devices', 500);
        }
    }

    /**
     * Logout from all devices including current
     */
    public function logoutAllDevicesIncludingCurrent(Request $request)
    {
        try {
            $user = Auth::user();

            // Delete all tokens
            $user->tokens()->delete();

            // Mark all login activities as logged out
            LoginActivity::where('user_id', $user->id)
                ->whereNull('logged_out_at')
                ->update(['logged_out_at' => now()]);

            return $this->successResponse([], 'Successfully logged out from all devices');

        } catch (\Exception $e) {
            \Log::error('Error logging out from all devices: ' . $e->getMessage());
            return $this->errorResponse('Failed to logout from all devices', 500);
        }
    }
}
