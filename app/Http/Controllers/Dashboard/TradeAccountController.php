<?php

namespace App\Http\Controllers\Dashboard;

use App\Models\TradeAccount;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class TradeAccountController extends Controller
{
    public function index(): JsonResponse
    {
        $tradeAccounts = TradeAccount::where('user_id', Auth::id())->get();
        return response()->json([
            'success' => true,
            'data' => $tradeAccounts->map(fn ($account) => [
                'id' => $account->id,
                'name' => $account->name,
            ]),
        ], 200);
    }
}
