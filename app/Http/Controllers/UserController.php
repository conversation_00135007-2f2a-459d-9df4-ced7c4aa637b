<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserPassword;
use App\Rules\NoProfanity;
use App\Rules\ValidUsername;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use SendGrid;
use SendGrid\Mail\Mail;

class UserController extends Controller
{
    //
    use ApiResponseTrait;

    public function index()
    {
        $user = auth()->user();

        $userData = $user->only([
            'id',
            'name',
            'email',
            'first_name',
            'last_name',
            'phone_number',
            'country',
            'language',
            'timezone',
            'currency',
            'number_format',
            'username',
            'username_change_count',
            'two_factor_enabled',
            'email_verified_at'
        ]);

        // Add computed security status fields
        $userData['is_email_verified'] = !is_null($user->email_verified_at);
        $userData['is_two_factor_enabled'] = (bool) $user->two_factor_enabled;
        $userData['has_phone_number'] = !is_null($user->phone_number) && !empty($user->phone_number);

        return $this->successResponse(
            $userData,
            'User personal information fetched successfully'
        );
    }


    public function update(User $user, Request $request)
    {

//        $checkUniqueness = filter_var($request->flag, FILTER_VALIDATE_BOOLEAN);

        $request->validate([
            'first_name' => 'nullable|string|max:50',
            'last_name' => 'nullable|string|max:50',
            'phone_number' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'language' => ['nullable', 'string', Rule::in(config('accountsettings.languages'))],
            'timezone' => ['nullable', 'string', Rule::in(config('accountsettings.timezones'))],
            'currency' => ['nullable', 'string', Rule::in(config('accountsettings.currencies'))],
            'number_format' => ['nullable', 'string', Rule::in(config('accountsettings.number_formats'))],
//            'username' => [
//                'string',
//                'max:20',
//                new ValidUsername,
//                new NoProfanity,
//            ],
            ]);

//        if ($checkUniqueness) {
//            $rules['username'][] = 'required';
//            $rules['username'][] = 'unique:users,username';
//        } else {
//            $rules['username'][] = 'nullable';
//        }

//        $messages = [
//            'username.unique' => 'This username is already taken.',
//        ];


        $data = $request->only([
            'first_name',
            'last_name',
            'phone_number',
            'country',
            'language',
            'timezone',
            'currency',
            'number_format',
            'email',
//            'username'
        ]);

        if ($user->id !== auth()->id()) {
            abort(403, 'Unauthorized');
        }

        if ($request->filled('email')) {
            if (strtolower($request->email) !== strtolower(auth()->user()->email)) {
                return response()->json(['message' => 'Current email does not match.'], 422);
            }

            if (strtolower($request->new_email) === strtolower($user->email)) {
                return response()->json(['message' => 'Kindly add a new email to update.'], 422);
            }

            $token = $this->generateToken();

            Cache::put(
                'email_change_' . $user->username,
                [
                    'new_email' => $request->new_email,
                    'old_email' => $user->email,
                    'token' => $token,
                ],
                now()->addMinutes(30)
            );

            return response()->json(['message' => 'Verification code sent to new email.', 'token' => $token]); // for testing only
        }

//        if (isset($data['username']) && $data['username'] !== $user->username) {
//            if ($user->username_change_count >= 2) {
//                return response()->json(['message' => 'Username change limit reached.'], 422);
//            }
//            $user->username_change_count += 1;
//        }

        if (isset($data['phone_number'])) {
            $data['phone_number'] = preg_replace('/\D/', '', $data['phone_number']);
        }

        $user->fill($data);
        $user->save();

        return response()->json([
            'message' => 'Account details updated successfully.',
            'user' => $user->fresh()
        ]);
    }


    /**
     * Initiate email update with verification
     */
    public function updateEmailDirect(Request $request)
    {
        try {
            $user = auth()->user();

            $request->validate([
                'email' => [
                    'required',
                    'string',
                    'email',
                    'max:255',
                    'regex:/\.[a-zA-Z]{2,4}$/', // Same validation as frontend
                    Rule::unique('users', 'email')->ignore($user->id),
                ],
            ], [
                'email.unique' => 'This email address is already associated with another account.',
                'email.email' => 'Please enter a valid email address.',
                'email.required' => 'Email address is required.',
                'email.max' => 'Email address must not exceed 255 characters.',
                'email.regex' => 'Please enter a valid email address.',
            ]);

            $newEmail = strtolower(trim($request->email));

            // Check if the email is actually different
            if ($newEmail === strtolower($user->email)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please enter a different email address to update.'
                ], 422);
            }

            // Generate verification code
            $verificationCode = $this->generateToken();

            // Create session ID for this email update verification
            $sessionId = \Illuminate\Support\Str::uuid();
            $cacheKey = "email_update_verification_{$user->id}_{$sessionId}";

            // Store verification data in cache (15 minutes expiration)
            Cache::put($cacheKey, [
                'user_id' => $user->id,
                'new_email' => $newEmail,
                'old_email' => $user->email,
                'code' => $verificationCode,
                'created_at' => now(),
                'verified' => false,
                'attempts' => 0
            ], now()->addMinutes(15));

            // Send verification email to new email address
            $emailSent = $this->sendEmailUpdateVerificationEmail($newEmail, $verificationCode);

            if (!$emailSent) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send verification email. Please try again.'
                ], 500);
            }

            // Mask the new email for display
            $maskedEmail = $this->maskEmail($newEmail);

            return response()->json([
                'success' => true,
                'message' => 'Verification code sent to your new email address.',
                'session_id' => $sessionId,
                'masked_email' => $maskedEmail,
                'redirect_to_verification' => true
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Get the first validation error message for email field
            $emailErrors = $e->errors()['email'] ?? [];
            $firstEmailError = is_array($emailErrors) && !empty($emailErrors) ? $emailErrors[0] : 'Validation failed.';

            return response()->json([
                'success' => false,
                'message' => $firstEmailError,
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to initiate email update. Please try again.'
            ], 500);
        }
    }

    /**
     * Verify email update code and complete the email update
     */
    public function verifyEmailUpdate(Request $request)
    {
        try {
            $user = auth()->user();

            $request->validate([
                'session_id' => 'required|string',
                'code' => 'required|string'
            ]);

            $cacheKey = "email_update_verification_{$user->id}_{$request->session_id}";
            $verificationData = Cache::get($cacheKey);

            if (!$verificationData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Verification session expired or invalid'
                ], 400);
            }

            // Check verification attempts for this session
            $attempts = $verificationData['attempts'] ?? 0;
            if ($attempts >= 5) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many failed attempts for this session'
                ], 400);
            }

            if ($verificationData['code'] !== $request->code) {
                // Increment attempts counter
                $verificationData['attempts'] = $attempts + 1;
                Cache::put($cacheKey, $verificationData, now()->addMinutes(15));

                return response()->json([
                    'success' => false,
                    'message' => 'Invalid verification code'
                ], 400);
            }

            // Verification successful - update the email
            $oldEmail = $verificationData['old_email'];
            $newEmail = $verificationData['new_email'];

            $user->email = $newEmail;
            $user->email_verified_at = null; // Reset email verification
            $user->save();

            // Send notification to original email address
            $this->sendEmailChangeNotification($oldEmail, $newEmail);

            // Clear the verification cache
            Cache::forget($cacheKey);

            return response()->json([
                'success' => true,
                'message' => 'Email address updated successfully.',
                'user' => $user->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to verify email update. Please try again.'
            ], 500);
        }
    }

    public function updateEmail(User $user, Request $request)
    {
        $request->validate([
            'token' => 'required|string'
        ]);

        if ($user->id !== auth()->id()) {
            abort(403, 'Unauthorized');
        }

        $cacheData = Cache::get('email_change_' . $user->username);

        if (!$cacheData || $cacheData['token'] !== $request->token) {
            return response()->json(['message' => 'Invalid or expired token.'], 422);
        }

        $user->email = $cacheData['new_email'];
        $user->save();

        Cache::forget('email_change_' . $user->username);

        return response()->json(['message' => 'Email updated successfully.']);
    }


    private function generateToken()
    {
        $characters = 'ACDEFGHJKMNPQRTUVWXYZ234679';
        return substr(str_shuffle(str_repeat($characters, 6)), 0, 6);
    }

    /**
     * Send email update verification email using SendGrid
     */
    private function sendEmailUpdateVerificationEmail($email, $code)
    {
        try {
            // Get SendGrid API key
            $apiKey = env('SENDGRID_API_KEY');
            if (!$apiKey) {
                \Log::error('SendGrid API key not configured');
                return false;
            }

            // Use verification template from config (same as security verification)
            $templateId = env('SENDGRID_EMAIL_VERIFICATION_CHECKUP_TEMPLATE_ID') ?: config('mail.templates.verification_token');
            if (!$templateId) {
                \Log::error('SendGrid template ID not configured');
                return false;
            }

            // Initialize SendGrid
            $sendgrid = new \SendGrid($apiKey, ['verify_ssl' => false]);

            // Create email message
            $message = new \SendGrid\Mail\Mail();
            $message->setFrom(env('MAIL_FROM_ADDRESS', '<EMAIL>'), env('MAIL_FROM_NAME', 'TradeReply'));
            $message->setReplyTo(env('MAIL_TO_REPLY', '<EMAIL>'), 'Support Team');
            $message->setSubject("Verify Your New Email Address");
            $message->addTo($email);
            $message->setTemplateId($templateId);

            // Add dynamic template data
            $message->addDynamicTemplateData('accountVerificationToken', $code);

            \Log::info('Sending email update verification via SendGrid', [
                'to' => $this->maskEmail($email),
                'template_id' => $templateId
            ]);

            // Send the email
            $response = $sendgrid->send($message);

            if ($response->statusCode() >= 200 && $response->statusCode() < 300) {
                \Log::info('Email update verification sent successfully', [
                    'to' => $this->maskEmail($email),
                    'status_code' => $response->statusCode()
                ]);
                return true;
            } else {
                \Log::error('SendGrid returned error status', [
                    'status_code' => $response->statusCode(),
                    'body' => $response->body()
                ]);
                return false;
            }

        } catch (\Exception $e) {
            \Log::error('Failed to send email update verification email', [
                'email' => $this->maskEmail($email),
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send email change notification to original email address using SendGrid
     */
    private function sendEmailChangeNotification($originalEmail, $newEmail)
    {
        try {
            // Get SendGrid API key
            $apiKey = env('SENDGRID_API_KEY');
            if (!$apiKey) {
                \Log::error('SendGrid API key not configured for email change notification');
                return false;
            }

            // Use the specified template ID for email change notification
            $templateId = env('SENDGRID_EMAIL_CHANGE_NOTIFICATION_TEMPLATE_ID') ?: config('mail.templates.email_change_notification');

            // Initialize SendGrid
            $sendgrid = new \SendGrid($apiKey, ['verify_ssl' => false]);

            // Create email message
            $message = new \SendGrid\Mail\Mail();
            $message->setFrom(env('MAIL_FROM_ADDRESS', '<EMAIL>'), env('MAIL_FROM_NAME', 'TradeReply'));
            $message->setReplyTo(env('MAIL_TO_REPLY', '<EMAIL>'), 'Support Team');
            $message->setSubject("Email Address Changed - Security Notice");
            $message->addTo($originalEmail);
            $message->setTemplateId($templateId);

            // Add dynamic template data
            $message->addDynamicTemplateData('accountOriginalEmail', $originalEmail);
            $message->addDynamicTemplateData('accountEmail', $newEmail);

            \Log::info('Sending email change notification via SendGrid', [
                'to' => $this->maskEmail($originalEmail),
                'new_email' => $this->maskEmail($newEmail),
                'template_id' => $templateId
            ]);

            // Send the email
            $response = $sendgrid->send($message);

            if ($response->statusCode() >= 200 && $response->statusCode() < 300) {
                \Log::info('Email change notification sent successfully', [
                    'to' => $this->maskEmail($originalEmail),
                    'status_code' => $response->statusCode()
                ]);
                return true;
            } else {
                \Log::error('SendGrid returned error status for email change notification', [
                    'status_code' => $response->statusCode(),
                    'body' => $response->body()
                ]);
                return false;
            }

        } catch (\Exception $e) {
            \Log::error('Failed to send email change notification', [
                'original_email' => $this->maskEmail($originalEmail),
                'new_email' => $this->maskEmail($newEmail),
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Mask email address for display
     * Format: ** + last letter + domain (**<EMAIL>)
     * Matches frontend hashInput() implementation for consistency with signup flow
     */
    private function maskEmail($email)
    {
        if (!$email) {
            return "";
        }

        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return $email;
        }

        $username = $parts[0];
        $domain = $parts[1];

        if (strlen($username) <= 1) {
            return "**@" . $domain;
        }

        $lastChar = $username[strlen($username) - 1];
        return "**" . $lastChar . '@' . $domain;
    }

    /**
     * Update user's username
     */
    public function updateUsername(Request $request)
    {
        try {
            $user = auth()->user();

            // Check if user has reached the change limit
            if ($user->username_change_count >= 2) {
                return $this->errorResponse('Username change limit reached. You can only change your username 2 times.', 422);
            }

            $request->validate([
                'username' => [
                    'required',
                    'string',
                    'min:3',
                    'max:20',
                    'regex:/^[a-zA-Z][a-zA-Z0-9_]*$/', // Must start with letter, can contain letters, numbers, underscores
                    Rule::unique('users', 'username')->ignore($user->id),
                    new ValidUsername(),
                    new NoProfanity(),
                ],
            ], [
                'username.required' => 'Username is required.',
                'username.min' => 'Username must be at least 3 characters.',
                'username.max' => 'Username cannot exceed 20 characters.',
                'username.regex' => 'Username must start with a letter and can only contain letters, numbers, and underscores.',
                'username.unique' => 'This username is already taken. Please choose another one.',
            ]);

            // Update username and increment change count
            $user->username = strtolower($request->username);
            $user->username_change_count = ($user->username_change_count ?? 0) + 1;
            $user->save();

            return $this->successResponse([
                'user' => $user->fresh(),
                'changes_remaining' => 2 - $user->username_change_count
            ], 'Username updated successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse('Validation failed.', 422, $e->errors());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update username. Please try again.', 500);
        }
    }

    /**
     * Check username availability
     */
    public function checkUsernameAvailability(Request $request)
    {
        try {
            $user = auth()->user();

            $request->validate([
                'username' => [
                    'required',
                    'string',
                    'min:3',
                    'max:20',
                    'regex:/^[a-zA-Z][a-zA-Z0-9_]*$/',
                ],
            ]);

            $username = strtolower($request->username);

            // Check if it's the user's current username
            if ($user->username === $username) {
                return $this->successResponse([
                    'available' => true,
                    'message' => 'This is your current username.'
                ]);
            }

            // Check if username is taken by another user
            $exists = User::where('username', $username)->where('id', '!=', $user->id)->exists();

            if ($exists) {
                return $this->errorResponse('This username is already taken. Please choose another one.', 422);
            }

            // Validate against rules
            $validator = validator(['username' => $username], [
                'username' => [new ValidUsername(), new NoProfanity()]
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->errors()->first('username'), 422);
            }

            return $this->successResponse([
                'available' => true,
                'message' => 'Username is available.'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse($e->errors()['username'][0] ?? 'Invalid username format.', 422);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to check username availability.', 500);
        }
    }

    /**
     * Get reserved usernames
     *
     * Returns a list of reserved usernames that cannot be used
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReservedUsernames()
    {
        try {
            // Get all routes from the application
            $routes = [];

            // Get routes from the router
            $routeCollection = \Route::getRoutes();
            foreach ($routeCollection as $route) {
                $uri = $route->uri();
                if (!empty($uri) && $uri !== '/' && !str_starts_with($uri, '_') && !str_starts_with($uri, 'api/')) {
                    // Remove parameters from route
                    $cleanRoute = preg_replace('/{.*?}/', '', $uri);
                    $cleanRoute = trim($cleanRoute, '/');
                    if (!empty($cleanRoute)) {
                        $routes[] = $cleanRoute;
                    }
                }
            }

            // Add common reserved names
            $reservedNames = [
                'admin', 'support', 'help', 'info', 'root', 'system', 'test',
                'username', 'null', 'user', 'guest', 'tradereply', 'trade_reply',
                'dashboard', 'account', 'marketplace', 'checkout', 'login', 'signup',
                'trading_calculator', 'trading-calculator', 'privacy', 'terms', 'contact',
                'blog', 'education', 'features', 'pricing', 'status', 'search',
                'settings', 'profile', 'notifications', 'messages', 'home', 'about',
                'faq', 'api', 'docs', 'documentation', 'support', 'careers', 'jobs',
                'press', 'news', 'events', 'partners', 'affiliates', 'investors',
                'legal', 'security', 'trust', 'safety', 'community', 'guidelines',
                'rules', 'policies', 'feedback', 'contact-us', 'help-center',
                'resources', 'tools', 'services', 'products', 'solutions'
            ];

            // Combine routes and reserved names
            $allReserved = array_merge($routes, $reservedNames);
            $allReserved = array_unique($allReserved);
            sort($allReserved);

            return $this->successResponse([
                'routes' => $allReserved
            ]);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get reserved usernames.', 500);
        }
    }

    /**
     * Change user password
     */
    public function changePassword(Request $request)
    {
        try {
            $user = auth()->user();
            \Log::info('Change password request received', [
                'user_id' => $user->id,
                'request_data' => $request->all()
            ]);

            // Validate the request
            $validated = $request->validate([
                'current_password' => 'required|string',
                'new_password' => 'required|string|min:8',
                'confirm_new_password' => 'required|string|same:new_password'
            ]);

            \Log::info('Validation passed', ['validated_data' => $validated]);

            // Custom password complexity validation
            $newPassword = $validated['new_password'];
            if (!preg_match('/[A-Z]/', $newPassword)) {
                return $this->errorResponse('Password must contain at least one uppercase letter.', 422);
            }
            if (!preg_match('/[a-z]/', $newPassword)) {
                return $this->errorResponse('Password must contain at least one lowercase letter.', 422);
            }
            if (!preg_match('/[0-9]/', $newPassword)) {
                return $this->errorResponse('Password must contain at least one number.', 422);
            }
            if (!preg_match('/[!@#$%^&*]/', $newPassword)) {
                return $this->errorResponse('Password must contain at least one special character (!@#$%^&*).', 422);
            }

            // Verify current password
            if (!Hash::check($validated['current_password'], $user->password)) {
                return $this->errorResponse('Current password is incorrect.', 422);
            }

            // Check if new password is same as current password
            if (Hash::check($validated['new_password'], $user->password)) {
                return $this->errorResponse('New password must be different from your current password.', 422);
            }

            // Check against recent password history (last 5 passwords)
            $recentPasswords = UserPassword::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->pluck('password')
                ->toArray();

            foreach ($recentPasswords as $oldHash) {
                if (Hash::check($validated['new_password'], $oldHash)) {
                    return $this->errorResponse('You\'ve used this password recently. Please choose a new one you haven\'t used before.', 422);
                }
            }

            // Update password in transaction
            DB::transaction(function () use ($user, $validated) {
                $hashedPassword = Hash::make($validated['new_password']);

                // Update user password
                $user->update([
                    'password' => $hashedPassword,
                    'login_attempts' => 0,
                    'lockout_cycles' => 0,
                    'lockout_until' => null,
                ]);

                // Manage password history (keep only last 5)
                $passwordCount = UserPassword::where('user_id', $user->id)->count();

                if ($passwordCount >= 5) {
                    UserPassword::where('user_id', $user->id)
                        ->orderBy('created_at', 'asc')
                        ->limit(1)
                        ->delete();
                }

                // Store new password in history
                UserPassword::create([
                    'user_id' => $user->id,
                    'password' => $hashedPassword,
                ]);
            });

            return $this->successResponse(
                ['message' => 'Password updated successfully.'],
                'Password updated successfully.'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation exception in changePassword', [
                'errors' => $e->errors(),
                'message' => $e->getMessage()
            ]);
            return $this->errorResponse('Validation failed.', 422, $e->errors());
        } catch (\Exception $e) {
            \Log::error('Exception in changePassword', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Failed to update password. Please try again.', 500);
        }
    }

}
