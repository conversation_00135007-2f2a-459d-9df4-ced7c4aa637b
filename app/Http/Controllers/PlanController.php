<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use Stripe\Stripe;
use App\Models\User;
use App\Models\UserSubscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Webhook;

class PlanController extends Controller
{
    public function index()
    {
        $plans = Plan::with('rules')
            ->get()
            ->map(function ($plan) {
                $title = strtolower($plan->title);
                $billing = strtolower($plan->billing_type);

                $order = match (true) {
                    str_contains($title, 'free')                                => 1,
                    str_contains($title, 'essential') && $billing === 'monthly' => 2,
                    str_contains($title, 'essential') && $billing === 'yearly'  => 3,
                    str_contains($title, 'plus') && $billing === 'monthly'      => 4,
                    str_contains($title, 'plus') && $billing === 'yearly'       => 5,
                    str_contains($title, 'premium') && $billing === 'monthly'   => 6,
                    str_contains($title, 'premium') && $billing === 'yearly'    => 7,
                    default => 0,
                };

                $plan->order = $order;
                return $plan;
            });

        return response()->json([
            'status' => 'success',
            'data'   => $plans,
        ]);
    }
    public function getActiveUserSubsciption()
    {
        $user = auth()->user();
        $user->load('activeSubscription.plan');

        $currentPlan = $user->activeSubscription?->plan;

        if (!$currentPlan) {
            return response()->json([
                'status' => 'error',
                'message' => 'No active subscription found.',
            ], 404);
        }

        $allPlans = Plan::get()->map(function ($plan) {
            $title = strtolower($plan->title);
            $billing = strtolower($plan->billing_type);

            $order = match (true) {
                str_contains($title, 'free')                                => 1,
                str_contains($title, 'essential') && $billing === 'monthly' => 2,
                str_contains($title, 'essential') && $billing === 'yearly'  => 3,
                str_contains($title, 'plus') && $billing === 'monthly'      => 4,
                str_contains($title, 'plus') && $billing === 'yearly'       => 5,
                str_contains($title, 'premium') && $billing === 'monthly'   => 6,
                str_contains($title, 'premium') && $billing === 'yearly'    => 7,
                default => 0,
            };

            $plan->order = $order;
            return $plan;
        })->sortBy('order')->values();

        $currentIndex = $allPlans->search(function ($plan) use ($currentPlan) {
            return $plan->id === $currentPlan->id;
        });

        $previousPlan = $allPlans->get($currentIndex - 1);
        $nextPlan     = $allPlans->get($currentIndex + 1);

        return response()->json([
            'status'        => 'success',
            'data'          => $user->activeSubscription,
            'is_trial'      => UserSubscription::where('user_id', $user->id)->where('is_trial', true)->exists(),
            'current_plan'  => $currentPlan,
            'previous_plan' => $previousPlan,
            'next_plan'     => $nextPlan,
        ]);
    }
    public function handleWebhook(Request $request)
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');

        try {
            $event = Webhook::constructEvent(
                $payload,
                $sigHeader,
                config('services.stripe.webhook_secret')
            );
        } catch (\Exception $e) {
            Log::error('Stripe webhook error: ' . $e->getMessage());
            return response()->json(['error' => 'Webhook verification failed.'], 400);
        }

        $eventType = $event->type;
        $data = $event->data->object;

        switch ($eventType) {
            case 'invoice.payment_succeeded':
                Log::info('Payment succeeded for invoice: ' . $data->id);
                break;

            case 'invoice.payment_failed':
                Log::warning('Payment failed for invoice: ' . $data->id);
                break;

            case 'customer.subscription.updated':
                $this->updateSubscription($data);
                break;

            case 'customer.subscription.deleted':
                $this->downgradeToFree($data);
                break;

            case 'invoice.upcoming':
                Log::info('Invoice upcoming for customer: ' . $data->customer);
                break;
        }

        return response()->json(['status' => 'success']);
    }

    protected function updateSubscription($data)
    {
        $user = User::where('stripe_customer_id', $data->customer)->first();
        if (!$user) {
            return;
        }

        $subscription = UserSubscription::where('stripe_subscription_id', $data->id)->first();

        $status = $data->cancel_at_period_end ? 'cancelled' : $data->status;
        $startsAt = Carbon::createFromTimestamp($data->current_period_start);
        $endsAt = Carbon::createFromTimestamp($data->current_period_end);

        $priceId = $data->items->data[0]->price->id ?? null;
        $plan = Plan::where('stripe_price_id', $priceId)->first();

        if ($subscription) {
            $wasTrial = $subscription->is_trial;

            $subscription->update([
                'ends_at' => $endsAt,
                'status' => $status,
                'is_trial' => $status === 'trialing',
            ]);

            if ($wasTrial && $status === 'active') {
                $subscription->update([
                    'is_trial' => false,
                    'starts_at' => $startsAt,
                    'ends_at' => $endsAt,
                    'status' => 'active',
                ]);
            }

        } else {
            UserSubscription::where('user_id', $user->id)
                ->where('status', 'active')
                ->update(['status' => 'deactive']);

            UserSubscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan?->id,
                'stripe_subscription_id' => $data->id,
                'is_trial' => $status === 'trialing',
                'status' => $status,
                'starts_at' => $startsAt,
                'ends_at' => $endsAt,
            ]);
        }
    }

    protected function downgradeToFree($data)
    {
        $user = User::where('stripe_customer_id', $data->customer)->first();
        if (!$user) {
            return;
        }

        UserSubscription::where('stripe_subscription_id', $data->id)
            ->update(['status' => 'deactive']);

        $freePlan = Plan::where('billing_type', 'free')->first();
        if (!$freePlan) {
            return;
        }

        UserSubscription::create([
            'user_id'     => $user->id,
            'plan_id'     => $freePlan->id,
            'status'      => 'active',
            'is_trial'    => false,
            'starts_at'   => now(),
            'ends_at'     => null,
        ]);
    }

    public function assignFreePlan()
    {
        $user = auth()->user();

        $freePlan = Plan::where('billing_type', 'free')->first();

        UserSubscription::where('user_id', $user->id)
                ->where('status', 'active')
                ->update(['status' => 'deactive']);

        UserSubscription::create([
            'user_id'     => $user->id,
            'plan_id'     => $freePlan->id,
            'status'      => 'active',
            'is_trial'    => false,
            'starts_at'   => now(),
            'ends_at'     => null,
        ]);

        $user->refresh();

        return response()->json([
            'data' => $user->activeSubscription
        ]);
    }
}
