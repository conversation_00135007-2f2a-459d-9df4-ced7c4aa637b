<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Validation\ValidationException;
use Throwable;
use App\Traits\ApiResponseTrait;

class Handler extends ExceptionHandler
{
    use ApiResponseTrait;

    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $exception)
    {
        if ($exception instanceof ValidationException) {
            // Check if this is an API request (for account updates, etc.)
            if ($request->is('api/*')) {
                // For API requests, return specific validation errors
                $errors = $exception->errors();
                $firstError = collect($errors)->flatten()->first();

                return $this->errorResponse(
                    $firstError ?: 'Validation failed',
                    422,
                    $errors
                );
            } else {
                // For login/auth requests, return generic message for security
                return $this->errorResponse(
                    'Incorrect email or password. Please try again',
                    422,
                    $exception->errors()
                );
            }
        }

        return parent::render($request, $exception);
    }
}
