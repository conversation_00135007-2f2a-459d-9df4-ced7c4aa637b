<?php

namespace App\Console\Commands;

use App\Models\Article;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\PortfolioFieldDefinition;

class UpdateAccountFieldValue extends Command
{
    protected $signature = 'update:account-field-value';
    protected $description = 'Updates the account field value';

    protected $accountFields = [
        'PORTFOLIO_TIMEZONE' => ['value' => 'UTC-5', 'placeholder' => 'Select', 'has_formula' => false],
        'PORTFOLIO_CURRENCY' => ['value' => 'USD', 'placeholder' => 'Select', 'has_formula' => false],
        'PORTFOLIO_STOCK_UNIT_OF_MEASUREMENT' => ['value' => 'SHARES', 'placeholder' => 'Select', 'has_formula' => false],
        'PORTFOLIO_CRYPTO_UNIT_OF_MEASUREMENT' => ['value' => 'WHOLE UNITS', 'placeholder' => 'Select', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_INITIAL_BALANCE' => ['value' => '10000.00', 'placeholder' => '$0', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_INITIAL_BALANCE_TYPE' => ['value' => 'CASH DEPOSIT', 'placeholder' => 'Select', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_AVAILABLE' => ['value' => '7000.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_ACCOUNT_SIZE' => ['value' => '10000.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_ACCOUNT_SIZE_AT_GOAL_CREATION' => ['value' => '10000.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_MANUAL_DEPOSIT' => ['value' => '500.00', 'placeholder' => '$0', 'has_formula' => false],
        'PORTFOLIO_MANUAL_DEPOSIT_TYPE' => ['value' => 'Cash', 'placeholder' => 'Select', 'has_formula' => false],
        'PORTFOLIO_WITHDRAWAL' => ['value' => '1000.00', 'placeholder' => '$0', 'has_formula' => false],
        'PORTFOLIO_TOTAL_CASH' => ['value' => '8000.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_ALLOCATED_CASH' => ['value' => '5000.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_UNALLOCATED_CASH' => ['value' => '3000.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_NET_CASH_CHANGES' => ['value' => '-200.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_CASH_RETENTION_RATIO' => ['value' => '80.00%', 'placeholder' => '0%', 'has_formula' => true],
        'PORTFOLIO_CAPITAL_RESERVE' => ['value' => '2000.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_TRADE_RESERVE' => ['value' => '3000.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_MAX_RISK_PERCENTAGE' => ['value' => '2.00%', 'placeholder' => '0%', 'has_formula' => false],
        'PORTFOLIO_MAX_RISK_TOLERANCE' => ['value' => 'CONSERVATIVE', 'placeholder' => 'Select', 'has_formula' => true],
        'PORTFOLIO_ACCOUNT_STOP_LOSS_VALUE' => ['value' => '200.00', 'placeholder' => '$0', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_STOP_LOSS_PERCENTAGE' => ['value' => '2.00%', 'placeholder' => '0%', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_STOP_RISK_VALUE' => ['value' => '300.00', 'placeholder' => '$0', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_STOP_RISK_PERCENTAGE' => ['value' => '3.00%', 'placeholder' => '0%', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_FIXED_STOP_VALUE' => ['value' => '100.00', 'placeholder' => '$0', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_FIXED_STOP_PERCENTAGE' => ['value' => '1.00%', 'placeholder' => '0%', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_GROWTH_GOAL' => ['value' => '15000.00', 'placeholder' => '$0', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_GROWTH_GOAL_FACTOR' => ['value' => '1.50', 'placeholder' => '0X', 'has_formula' => false],
        'PORTFOLIO_ACCOUNT_GROWTH_VALUE_OF_GOAL' => ['value' => '5000.00', 'placeholder' => '$0', 'has_formula' => true],
        'PORTFOLIO_ACCOUNT_GROWTH_PERCENTAGE_OF_GOAL' => ['value' => '50.00%', 'placeholder' => '0%', 'has_formula' => true],
        'PORTFOLIO_ACCOUNT_GROWTH_FACTOR_OF_GOAL' => ['value' => '1.50', 'placeholder' => '0X', 'has_formula' => true],
        'PORTFOLIO_PROFIT_ALLOCATION_TO_CAPITAL_RESERVE_PERCENTAGE' => ['value' => '20.00%', 'placeholder' => '0%', 'has_formula' => false],
    ];

    public function handle()
    {
        foreach ($this->accountFields as $key => $accountField) {
            $portfolio = PortfolioFieldDefinition::query()
                ->where('database_field', $key)
                ->first();

            if ($portfolio) {
                $portfolio->account_field_value = $accountField['value'];
                $portfolio->account_field_placeholder = $accountField['placeholder'];
                $portfolio->has_formula = $accountField['has_formula'];
                $portfolio->save();
            }
        }
    }
}
