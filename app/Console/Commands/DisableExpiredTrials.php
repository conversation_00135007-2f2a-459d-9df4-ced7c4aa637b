<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserSubscription;
use Carbon\Carbon;

class DisableExpiredTrials extends Command
{
    protected $signature = 'trials:disable';
    protected $description = 'Disable expired free trials';

    public function handle()
    {
        // Find expired trials
        $expiredTrials = UserSubscription::where('is_trial', true)
            ->where('trial_expires_at', '<', Carbon::now())
            ->get();

        foreach ($expiredTrials as $trial) {
            $trial->update([
                'is_trial' => false,
                'trial_expires_at' => null,
                'expires_at' => null, // Disable access
            ]);
        }

        $this->info('Expired free trials disabled.');
    }
}
