<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\ArticleFaq;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ImportEducationFaq extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-education-faq';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $csvPath = public_path('Education_faq.csv');

        if (!File::exists($csvPath)) {
            $this->error("CSV not found: $csvPath");
            return 1;
        }

        $csv = array_map('str_getcsv', file($csvPath));
        $header = array_map('trim', array_shift($csv));

        $count = 0;

        foreach ($csv as $row) {
            $slug = str_replace('_', '-', trim($row[0])) . '-education';
            $article = Article::where('slug', $slug)->first();

            if (!$article) {
                $this->warn("Article not found for slug: $slug");
                continue;
            }

            for ($i = 1; $i < count($row); $i += 2) {
                $question = trim($row[$i] ?? '');
                $answer = trim($row[$i + 1] ?? '');

                if ($question && $answer) {
                    ArticleFaq::create([
                        'article_id' => $article->id,
                        'question'   => $question,
                        'answer'     => $answer,
                    ]);
                    $count++;
                }
            }
        }

        $this->info("Imported $count FAQs into article_faqs table.");
        return 0;
    }
}
