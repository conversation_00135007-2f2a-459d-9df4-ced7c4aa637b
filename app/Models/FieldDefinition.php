<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FieldDefinition extends Model
{
    use HasFactory;

    protected $fillable = [
        'field_name',
        'database_field',
        'summary',
        'datatype',
        'expected_values',
        'has_formula',
        'metric_dimension'
    ];

    protected $casts = [
        'has_formula' => 'boolean',
    ];

    // Optionally, define relationships to scope-specific definitions.
    public function transactionDefinition()
    {
        return $this->hasOne(TransactionFieldDefinition::class);
    }

    public function tradeDefinition()
    {
        return $this->hasOne(TradeFieldDefinition::class);
    }

    public function portfolioDefinition()
    {
        return $this->hasOne(PortfolioFieldDefinition::class);
    }
}
