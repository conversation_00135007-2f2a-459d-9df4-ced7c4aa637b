<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TradeFormSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'trade_form_id',
        'section',
        'data',
        'formula_data'
    ];

    protected $casts = [
        'data'         => 'array',
        'formula_data' => 'array',
    ];

    public function form()
    {
        return $this->belongsTo(TradeForm::class, 'trade_form_id');
    }
}

