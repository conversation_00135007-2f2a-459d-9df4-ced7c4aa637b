<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductChildSubcategory extends Model
{
    use HasFactory;

    protected $table = 'product_categories';

    protected $fillable = [
        'name',
        'slug',
        'sort_order',
        'product_main_category_id',
    ];

    public function mainCategory()
    {
        return $this->belongsTo(ProductCategory::class, 'product_main_category_id');
    }

    public function childSubcategories()
    {
        return $this->hasMany(ProductChildSubcategory::class, 'product_subcategory_id');
    }

}
