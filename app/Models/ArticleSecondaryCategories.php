<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ArticleSecondaryCategories extends Model
{
    use HasFactory;

    protected $table = 'article_secondary_categories';

    protected $fillable = ['article_id','category_id'];

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function article()
    {
        return $this->belongsTo(Article::class, 'article_id');
    }
}
