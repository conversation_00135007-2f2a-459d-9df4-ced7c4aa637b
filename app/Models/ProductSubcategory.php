<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductSubcategory extends Model
{
    use HasFactory;


    protected $table = 'product_subcategories';

    protected $fillable = [
        'name',
        'slug',
        'sort_order',
        'product_subcategory_id',
    ];

    public function subcategory()
    {
        return $this->belongsTo(ProductSubcategory::class, 'product_subcategory_id');
    }
}
