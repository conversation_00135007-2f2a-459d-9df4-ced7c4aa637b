<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransactionFieldDefinition extends Model
{
    use HasFactory;
    protected $table = 'transaction_field_definitions';
    protected $fillable = [
        'field_definition_id',
        'database_field',
        'summary',
        'account_field',
        'account_field_value'
    ];

    public function fieldDefinition()
    {
        return $this->belongsTo(FieldDefinition::class);
    }
}
