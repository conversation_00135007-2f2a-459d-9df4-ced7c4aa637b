<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionFeaturePlan extends Model
{
    // use HasFactory;

    protected $table = 'subscription_feature_plans';

    protected $fillable = [
        'subscription_feature_id',
        'subscription_id',
        'feature_limit',
    ];

    // Relationship with Subscription (One-to-Many)
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    // Relationship with SubscriptionFeature (One-to-Many)
    public function feature()
    {
        return $this->belongsTo(SubscriptionFeature::class, 'subscription_feature_id');
    }
}
