<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
//    use HasFactory;

    protected $fillable = [
        'name',
        'expires_at',
        'price',
    ];

    // Relationship with SubscriptionFeaturePlan (One-to-Many)
    public function featurePlans()
    {
        return $this->hasMany(SubscriptionFeaturePlan::class);
    }

    // Get Features for a Subscription (Many-to-Many via Pivot Table)
    public function features()
    {
        return $this->belongsToMany(SubscriptionFeature::class, 'subscription_feature_plans')
            ->withPivot('feature_limit'); // Include feature limits in the pivot relationship
    }
}
