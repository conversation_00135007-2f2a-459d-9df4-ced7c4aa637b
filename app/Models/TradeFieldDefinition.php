<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TradeFieldDefinition extends Model
{
    use HasFactory;
    protected $table = 'trade_field_definitions';
    protected $fillable = [
        'field_definition_id',
        'database_field',
        'summary',
        'account_field'
    ];

    public function fieldDefinition()
    {
        return $this->belongsTo(FieldDefinition::class);
    }
}
