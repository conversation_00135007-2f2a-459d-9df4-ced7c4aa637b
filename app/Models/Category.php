<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Category extends Model
{
    use HasFactory;

    protected $table = 'categories';
    protected $fillable = ['title', 'content', 'slug','database_field'];

    public function getRouteKeyName()
    {
        return 'slug';
    }
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if ($category->title) {
                $category->slug = self::generateUniqueSlug($category->title);
                // Only set database_field from title if it is not provided.
                if (empty($category->database_field)) {
                    $category->database_field = self::generateDatabaseField($category->title);
                }
            }
        });

        static::updating(function ($category) {
            if ($category->isDirty('title')) {
                // Same condition here if you wish to update based on title only if not manually provided.
                if (empty($category->database_field)) {
                    $category->database_field = self::generateDatabaseField($category->title);
                }
                $category->slug = self::generateUniqueSlug($category->title);
            }
        });
    }

    /**
     * Generate database_field from the title.
     */
    private static function generateDatabaseField($title)
    {
        // Remove literal parentheses.
        $cleanTitle = str_replace(['(', ')'], '', $title);
        $cleanTitle = trim($cleanTitle);
        return 'cat_' . str_replace(' ', '_', strtolower($cleanTitle));
    }


    /**
     * Generate a unique slug based on the title.
     *
     * @param string $title
     * @return string
     */
    public static function generateUniqueSlug($title)
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $count = 1;

        while (self::where('slug', $slug)->exists()) {
            $slug = "{$originalSlug}-{$count}";
            $count++;
        }

        return $slug;
    }

    /**
     * Relationship: A category has many primary articles.
     */
    public function primaryArticles()
    {
        return $this->hasMany(Article::class, 'primary_category_id');
    }


    public function secondaryArticles()
    {
        return $this->hasMany(ArticleSecondaryCategories::class, 'category_id');
    }

    /**
     * Relationship: A category for article type blogs
     */
    public function primaryBlogs()
    {
        return $this->hasMany(Article::class, 'primary_category_id')->where('type', 'blog');
    }



    /**
     * Fetch paginated category list with counts.
     */
    public static function getCategoryList($perPage = 10)
    {
        return self::withCount([
            'primaryArticles as primary_count',
            'secondaryArticles as secondary_count'
        ])->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }



    /**
     * Create a new category
     */
    public static function createCategory(array $data)
    {
        // Create category
        self::create([
            'title' => $data['title'],
            'content' => $data['content'],
        ]);

        // Fetch latest paginated categories
        return self::orderBy('created_at', 'desc')->paginate(10);
    }

    /**
     * Update an existing category
     */
    public function updateCategory(array $data)
    {
        $this->update([
            'title' => $data['title'],
            'content' => $data['content'],
        ]);

        return $this->refresh(); // Fetch latest data after update
    }

    /**
     * Delete category and update related articles.
     */
    public function deleteCategory()
    {
        // Update primary categories
        $this->updatePrimaryCategory();

        // Check if there are any secondary articles before deleting
        if ($this->secondaryArticles()->exists()) {
            $this->secondaryArticles()->delete();
        }

        // Delete category
        return $this->delete();
    }


    /**
     * Updates primary category in `articles` table.
     */
    private function updatePrimaryCategory()
    {
        $articles = $this->primaryArticles()->get();

        foreach ($articles as $article) {
            // Fetch secondary categories for this article
            $secondaryCategoryIds = $article->secondaryCategories()->pluck('categories.id')->toArray();

            // Assign the first secondary category as primary, if available
            if (!empty($secondaryCategoryIds)) {
                $article->primary_category_id = array_shift($secondaryCategoryIds);
            } else {
                $article->primary_category_id = null;
            }

            $article->save();
            $article->secondaryCategories()->detach($this->id);
        }
    }


    public function blogSiteMap()
    {
        return $this->whereHas('primaryBlogs', fn($q) => $q->where('type', 'blog'))
            ->with(['primaryBlogs' => fn($q) => $q->where('type', 'blog')->latest()])
            ->orderBy('title')
            ->get()
            ->filter(fn($category) => $category->primaryBlogs->isNotEmpty())
            ->each(function ($category) {
                $category->setRelation(
                    'primaryBlogs',
                    $category->primaryBlogs->unique('title')->values()
                );
            });
    }





}
