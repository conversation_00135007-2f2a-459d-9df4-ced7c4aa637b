<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class FailedLoginAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'ip_address',
        'user_agent',
        'account_exists',
    ];

    protected $casts = [
        'account_exists' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get failed attempts count for email/IP combination within time window
     */
    public static function getFailedAttemptsCount($email, $ipAddress, $minutes = 60)
    {
        return self::where('email', $email)
            ->where('ip_address', $ipAddress)
            ->where('created_at', '>=', Carbon::now()->subMinutes($minutes))
            ->count();
    }

    /**
     * Get failed attempts count for IP address within time window
     */
    public static function getFailedAttemptsCountByIp($ipAddress, $minutes = 60)
    {
        return self::where('ip_address', $ipAddress)
            ->where('created_at', '>=', Carbon::now()->subMinutes($minutes))
            ->count();
    }

    /**
     * Record a failed login attempt
     */
    public static function recordFailedAttempt($email, $ipAddress, $userAgent = null, $accountExists = false)
    {
        return self::create([
            'email' => $email,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'account_exists' => $accountExists,
        ]);
    }

    /**
     * Clean up old failed attempts (older than specified hours)
     */
    public static function cleanupOldAttempts($hours = 24)
    {
        return self::where('created_at', '<', Carbon::now()->subHours($hours))->delete();
    }

    /**
     * Clear all failed attempts for a specific IP address
     */
    public static function clearFailedAttemptsByIp($ipAddress)
    {
        return self::where('ip_address', $ipAddress)->delete();
    }
}
