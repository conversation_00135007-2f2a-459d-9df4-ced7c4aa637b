<?php

namespace App\Models;

use App\Traits\CsvImporterTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use mysql_xdevapi\Exception;

class Education extends Model
{
    use HasFactory, CsvImporterTrait;

    protected $table = 'education';
    protected $fillable = [
        'title',
        'content',
        'slug',
        'feature_image',
        'tags',
        'primary_category_id',
        'secondary_categories',
        'is_featured',
        'summary'
    ];

    public const EDUCATION_PATH = '/education/featured/';

    protected static string $ENV;

    protected $attributes = [
        'is_featured' => 0,
    ];

    protected $appends = [
        'feature_image_url'
    ];

    protected $casts = [
        'secondary_categories' => 'array', // Automatically convert JSON to array
    ];

    public function getRouteKeyName()
    {
        return 'slug';
    }


    protected static function boot()
    {

        parent::boot();
        self::$ENV = env('APP_ENV') === 'production' ? '/main' : '/dev';

        static::deleting(function ($education) {
//            if ($education->feature_image) {
//                if (Storage::disk('s3')->exists(self::$ENV . self::EDUCATION_PATH . $education->feature_image)) {
//                    Storage::disk('s3')->delete(self::$ENV . self::EDUCATION_PATH . $education->feature_image);
//                }
//            }
        });

        static::creating(function ($education) {
            if ($education->title) {
                $education->slug = self::generateUniqueSlug($education->title);
            }
            if (request()->hasFile('feature_image')) {
                 $education->feature_image = self::uploadFeatureImage(request()->file('feature_image'));
            }
        });

        static::updating(function ($education) {
            $request = request(); // Get request instance safely

            // Check if the title has changed, then update the slug
                if ($education->isDirty('title')) {
                    $education->slug = self::generateUniqueSlug($education->title, $education->id);
                }


//            if ($request->hasFile('feature_image')) {
//                 $oldImage = $education->getOriginal('feature_image');
//
////                 Delete the old image if it exists in S3
//                 if ($oldImage && Storage::disk('s3')->exists(self::$ENV. self::EDUCATION_PATH . $oldImage)) {
//                 Storage::disk('s3')->delete(self::$ENV. self::EDUCATION_PATH . $oldImage);
//                 }
//                 $education->feature_image = self::uploadFeatureImage($request->file('feature_image'));
//            }
        });
    }


    public function setIsFeaturedAttribute($value)
    {
        $this->attributes['is_featured'] = $value ? 1 : 0;
    }


    public static function uploadFeatureImage($file)
    {
        $originalName = Str::random(40) . '-' . $file->getClientOriginalName();
        $path = self::$ENV . self::EDUCATION_PATH;

        try {
            $check = Storage::disk('s3')->putFileAs($path, $file, $originalName);
            Log::info('File uploaded successfully', ['status' => $check]);
            return $originalName;
        } catch (\Exception $e) {
            Log::error('S3 Upload Failed: ' . $e->getMessage());
        }
    }

    public function getFeatureImageUrlAttribute()
    {
        return $this->feature_image
            ? config('filesystems.disks.s3.url') . self::$ENV . self::EDUCATION_PATH . $this->feature_image
            : null;
    }


    /**
     * Generate a unique slug based on the title.
     *
     * @param string $title
     * @return string
     */
    public static function generateUniqueSlug($title, $id = null)
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $count = 1;

        while (self::where('slug', $slug)
            ->when($id, fn($query) => $query->where('id', '!=', $id)) // Exclude current record
            ->exists()) {
            $slug = "{$originalSlug}-{$count}";
            $count++;
        }

        return $slug;
    }


    /**
     * Categories relationship (one-to-many).
     */
    public function primaryCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'primary_category_id');
    }


    public function progressPercentage()
    {
        return $this->hasOne(UserArticleProgress::class, 'article_id', 'id');
    }
    




    /**
     * Import Education records from CSV
     *
     * @param string $filePath
     * @param array $mapping (CSV header to DB column mapping)
     * @return array
     * @throws Exception
     */
    public static function importFromCsv(string $file, array $mapping = [])
    {
        try {
            $educationInstance = new self();

            $records = $educationInstance->importCsv($file, $educationInstance->getFillable(), $mapping);

            $importedRecords = [];

            foreach ($records as $educationData) {
                $education = self::create((array)$educationData);
                $importedRecords[] = $education;
            }

            return $importedRecords;
        } catch (Exception $e) {
            throw new Exception("Error importing education records: " . $e->getMessage());
        }
    }

}
