<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class SecurityVerificationSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'email',
        'verification_code',
        'verified',
        'verified_at',
        'expires_at',
        'attempts',
    ];

    protected $casts = [
        'verified' => 'boolean',
        'verified_at' => 'datetime',
        'expires_at' => 'datetime',
        'attempts' => 'integer',
    ];

    /**
     * Get the user that owns the verification session
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the verification attempts for this session
     */
    public function verificationAttempts(): HasMany
    {
        return $this->hasMany(SecurityVerificationAttempt::class, 'session_id', 'session_id');
    }

    /**
     * Scope to get active (non-expired) sessions
     */
    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', now());
    }

    /**
     * Scope to get expired sessions
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope to get verified sessions
     */
    public function scopeVerified($query)
    {
        return $query->where('verified', true);
    }

    /**
     * Scope to get unverified sessions
     */
    public function scopeUnverified($query)
    {
        return $query->where('verified', false);
    }

    /**
     * Check if the session is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if the session is verified
     */
    public function isVerified(): bool
    {
        return $this->verified;
    }

    /**
     * Mark the session as verified
     */
    public function markAsVerified(): void
    {
        $this->update([
            'verified' => true,
            'verified_at' => now(),
        ]);
    }

    /**
     * Increment the attempts counter
     */
    public function incrementAttempts(): void
    {
        $this->increment('attempts');
    }

    /**
     * Reset the attempts counter (used when resending code)
     */
    public function resetAttempts(): void
    {
        $this->update(['attempts' => 0]);
    }

    /**
     * Update the verification code and reset attempts
     */
    public function updateVerificationCode(string $newCode): void
    {
        $this->update([
            'verification_code' => $newCode,
            'attempts' => 0,
            'created_at' => now(), // Reset creation time for new code
        ]);
    }

    /**
     * Find an active session by user and session ID
     */
    public static function findActiveSession(int $userId, string $sessionId): ?self
    {
        return static::where('user_id', $userId)
            ->where('session_id', $sessionId)
            ->active()
            ->first();
    }

    /**
     * Create a new verification session
     */
    public static function createSession(
        int $userId,
        string $sessionId,
        string $email,
        string $verificationCode,
        int $expiresInMinutes = 15
    ): self {
        return static::create([
            'user_id' => $userId,
            'session_id' => $sessionId,
            'email' => $email,
            'verification_code' => $verificationCode,
            'verified' => false,
            'expires_at' => now()->addMinutes($expiresInMinutes),
            'attempts' => 0,
        ]);
    }

    /**
     * Clean up expired sessions
     */
    public static function cleanupExpired(): int
    {
        return static::expired()->delete();
    }

    /**
     * Get sessions that have exceeded max attempts
     */
    public function scopeExceededAttempts($query, int $maxAttempts = 5)
    {
        return $query->where('attempts', '>=', $maxAttempts);
    }

    /**
     * Check if this session has exceeded max attempts
     */
    public function hasExceededAttempts(int $maxAttempts = 5): bool
    {
        return $this->attempts >= $maxAttempts;
    }
}
