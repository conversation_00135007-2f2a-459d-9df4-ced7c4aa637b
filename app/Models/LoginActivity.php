<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Jen<PERSON>gers\Agent\Agent;
use App\Services\GeoIpService;

class LoginActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'ip_address',
        'user_agent',
        'device_type',
        'browser',
        'platform',
        'city',
        'country',
        'session_token',
        'logged_out_at'
    ];

    protected $casts = [
        'logged_out_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function createFromRequest($request, $user, $sessionToken = null)
    {
        $agent = new Agent();
        $agent->setUserAgent($request->header('User-Agent'));

        return self::create([
            'user_id' => $user->id,
            'ip_address' => self::getRealIpAddress($request),
            'user_agent' => $request->header('User-Agent'),
            'device_type' => self::getDeviceType($agent),
            'browser' => $agent->browser(),
            'platform' => $agent->platform(),
            'city' => self::getCityFromIP(self::getRealIpAddress($request)),
            'country' => self::getCountryFromIP(self::getRealIpAddress($request)),
            'session_token' => $sessionToken,
        ]);
    }

    /**
     * Get the real IP address from request, handling proxies and load balancers
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    private static function getRealIpAddress($request): string
    {
        // Check for IP from various headers in order of preference
        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_REAL_IP',            // Nginx proxy
            'HTTP_X_FORWARDED_FOR',      // Standard forwarded header
            'HTTP_X_FORWARDED',          // Alternative forwarded header
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Alternative
            'HTTP_FORWARDED',            // Alternative
            'HTTP_CLIENT_IP',            // Alternative
            'REMOTE_ADDR'                // Standard remote address
        ];

        foreach ($ipHeaders as $header) {
            $ip = $request->server($header);

            if (!empty($ip) && $ip !== 'unknown') {
                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (strpos($ip, ',') !== false) {
                    $ips = explode(',', $ip);
                    $ip = trim($ips[0]); // Get the first IP (original client)
                }

                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // Fallback to Laravel's default method
        return $request->ip();
    }

    private static function getDeviceType($agent)
    {
        if ($agent->isMobile()) {
            return 'Mobile';
        } elseif ($agent->isTablet()) {
            return 'Tablet';
        } elseif ($agent->isDesktop()) {
            return 'Desktop';
        }
        return 'Unknown';
    }

    private static function getCityFromIP($ip)
    {
        return GeoIpService::getCity($ip);
    }

    private static function getCountryFromIP($ip)
    {
        return GeoIpService::getCountry($ip);
    }

    public function getFormattedDateAttribute()
    {
        return $this->created_at->format('M j, Y g:i A');
    }

    public function getLocationAttribute()
    {
        if ($this->city && $this->country) {
            return $this->city . ', ' . $this->country;
        }
        return 'Unknown Location';
    }

    public function getSiteNameAttribute()
    {
        return 'Tradereply Website';
    }
}
