<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Address extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'address',
        'city',
        'state',
        'zip_code',
        'country',
        'is_default'
    ];

    protected $casts = [
        'is_default' => 'boolean',
    ];

    /**
     * Get the user that owns the address.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full name attribute.
     */
    public function getFullNameAttribute()
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get the formatted address attribute.
     */
    public function getFormattedAddressAttribute()
    {
        return "{$this->address}, {$this->city}, {$this->state} {$this->zip_code}, {$this->country}";
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        // When setting an address as default, unset all other default addresses for the user
        static::saving(function ($address) {
            if ($address->is_default) {
                static::where('user_id', $address->user_id)
                    ->where('id', '!=', $address->id)
                    ->update(['is_default' => false]);
            }
        });

        // Ensure at least one address is default if it's the only address
        static::created(function ($address) {
            $userAddressCount = static::where('user_id', $address->user_id)->count();
            if ($userAddressCount === 1) {
                $address->update(['is_default' => true]);
            }
        });

        // If deleting the default address, set another address as default
        static::deleting(function ($address) {
            if ($address->is_default) {
                $nextAddress = static::where('user_id', $address->user_id)
                    ->where('id', '!=', $address->id)
                    ->first();
                
                if ($nextAddress) {
                    $nextAddress->update(['is_default' => true]);
                }
            }
        });
    }
}
