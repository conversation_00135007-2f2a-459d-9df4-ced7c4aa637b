<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_id',
        'is_free_subscription',
        'is_downgrade',
        // 'product_id',
        'order_type',
        'status',
        'billing_type',
        'price',
    ];

    protected $casts = [
        'is_free_subscription' => 'boolean',
        'is_downgrade' => 'boolean'
    ];

    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }
}
